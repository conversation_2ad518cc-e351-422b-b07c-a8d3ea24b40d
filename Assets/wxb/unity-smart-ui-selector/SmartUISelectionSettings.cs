using UnityEngine;

namespace SmartUISelection
{
    [CreateAssetMenu(fileName = "SmartUISelectionSettings", menuName = "Smart UI Selection/Settings")]
    public class SmartUISelectionSettings : ScriptableObject
    {
        [Header("Selection Behavior")]
        public bool selectOnlyVisible = true;
        public bool ignoreDisabled = true;
        public bool preferInteractive = true;
        public float selectionDistance = 10f;
        
        [Header("Element Filter")]
        public bool includeButtons = true;
        public bool includeImages = true;
        public bool includeTexts = true;
        public bool includeInputFields = true;
        public bool includeSliders = true;
        public bool includeCustomUI = true;
        
        [Header("Keyboard Shortcuts")]
        public KeyCode selectionKey = KeyCode.LeftControl;
        public bool useShiftModifier = true;

        // 单例实例
        private static SmartUISelectionSettings _instance;
        
        public static SmartUISelectionSettings Instance
        {
            get
            {
                if (_instance == null)
                {
                    // 尝试加载已存在的设置
                    _instance = Resources.Load<SmartUISelectionSettings>("SmartUISelectionSettings");
                    
                    // 如果没有找到，创建新的设置
                    if (_instance == null)
                    {
                        _instance = CreateInstance<SmartUISelectionSettings>();
                        
                        // 在编辑器模式下保存资源
#if UNITY_EDITOR
                        // 确保Resources文件夹存在
                        System.IO.Directory.CreateDirectory("Assets/Resources");
                        UnityEditor.AssetDatabase.CreateAsset(_instance, "Assets/Resources/SmartUISelectionSettings.asset");
                        UnityEditor.AssetDatabase.SaveAssets();
#endif
                    }
                }
                return _instance;
            }
        }

        // 保存设置
        public void Save()
        {
#if UNITY_EDITOR
            UnityEditor.EditorUtility.SetDirty(this);
            UnityEditor.AssetDatabase.SaveAssets();
#endif
        }
    }
}
