using UnityEngine;
using UnityEditor;
using UnityEngine.UI;
using System.Collections.Generic;
using System.Linq;

namespace SmartUISelection
{
    public class SmartUISelectorWindow : EditorWindow
    {
        // 选择设置
        public bool selectOnlyVisible = true;
        public bool ignoreDisabled = true;
        public bool preferInteractive = true;
        public float selectionDistance = 10f;

        // 过滤选项
        public bool includeButtons = true;
        public bool includeImages = true;
        public bool includeTexts = true;
        public bool includeInputFields = true;
        public bool includeSliders = true;
        public bool includeCustomUI = true;

        // 快捷键设置
        public KeyCode selectionKey = KeyCode.LeftControl;
        public bool useShiftModifier = true;

        private Vector2 scrollPosition;
        private List<GameObject> lastSelectedObjects = new List<GameObject>();

        [MenuItem("Window/Smart UI Selector")]
        public static void ShowWindow()
        {
            GetWindow<SmartUISelectorWindow>("Smart UI Selector");
        }

        private void OnEnable()
        {
            // 注册场景视图事件
            SceneView.duringSceneGui += OnSceneGUI;
        }

        private void OnDisable()
        {
            // 取消注册场景视图事件
            SceneView.duringSceneGui -= OnSceneGUI;
        }

        private void OnGUI()
        {
            GUILayout.Label("Smart UI Selection Settings", EditorStyles.boldLabel);

            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

            // 选择行为设置
            GUILayout.Label("Selection Behavior", EditorStyles.label);
            selectOnlyVisible = EditorGUILayout.Toggle("Only select visible elements", selectOnlyVisible);
            ignoreDisabled = EditorGUILayout.Toggle("Ignore disabled elements", ignoreDisabled);
            preferInteractive = EditorGUILayout.Toggle("Prefer interactive elements", preferInteractive);
            selectionDistance = EditorGUILayout.Slider("Max selection distance", selectionDistance, 1f, 50f);

            EditorGUILayout.Space();

            // 过滤选项
            GUILayout.Label("Element Filter", EditorStyles.label);
            includeButtons = EditorGUILayout.Toggle("Include Buttons", includeButtons);
            includeImages = EditorGUILayout.Toggle("Include Images", includeImages);
            includeTexts = EditorGUILayout.Toggle("Include Texts", includeTexts);
            includeInputFields = EditorGUILayout.Toggle("Include Input Fields", includeInputFields);
            includeSliders = EditorGUILayout.Toggle("Include Sliders", includeSliders);
            includeCustomUI = EditorGUILayout.Toggle("Include custom UI elements", includeCustomUI);

            EditorGUILayout.Space();

            // 快捷键设置
            GUILayout.Label("Keyboard Shortcuts", EditorStyles.label);
            selectionKey = (KeyCode)EditorGUILayout.EnumPopup("Selection Key", selectionKey);
            useShiftModifier = EditorGUILayout.Toggle("Use Shift modifier", useShiftModifier);

            EditorGUILayout.HelpBox("Hold " + selectionKey + (useShiftModifier ? " + Shift" : "") +
                                    " and click in Scene view to smart select UI elements", MessageType.Info);

            EditorGUILayout.Space();

            // 最近选择的对象
            GUILayout.Label("Recently Selected", EditorStyles.label);
            if (lastSelectedObjects.Count == 0)
            {
                EditorGUILayout.LabelField("No recent selections");
            }
            else
            {
                foreach (var obj in lastSelectedObjects.Take(5))
                {
                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.ObjectField(obj, typeof(GameObject), true);
                    if (GUILayout.Button("Select", GUILayout.Width(60)))
                    {
                        Selection.activeGameObject = obj;
                    }

                    EditorGUILayout.EndHorizontal();
                }
            }

            EditorGUILayout.EndScrollView();
        }

        private void OnSceneGUI(SceneView sceneView)
        {
            // 检查快捷键是否按下
            Event currentEvent = Event.current;
            bool shortcutPressed = currentEvent.modifiers == GetModifierCombination() &&
                                   currentEvent.type == EventType.MouseDown &&
                                   currentEvent.button == 0; // 左键点击

            if (shortcutPressed)
            {
                HandleSmartSelection(currentEvent.mousePosition);
            }

            if (shortcutPressed && currentEvent.type == EventType.MouseUp)
            {
                currentEvent.Use(); // 消费事件，防止其他操作
            }
        }

        private EventModifiers GetModifierCombination()
        {
            EventModifiers modifiers = 0;

            switch (selectionKey)
            {
                case KeyCode.LeftControl:
                case KeyCode.RightControl:
                    modifiers |= EventModifiers.Control;
                    break;
                case KeyCode.LeftAlt:
                case KeyCode.RightAlt:
                    modifiers |= EventModifiers.Alt;
                    break;
                case KeyCode.LeftCommand:
                case KeyCode.RightCommand:
                    modifiers |= EventModifiers.Command;
                    break;
            }

            if (useShiftModifier)
            {
                modifiers |= EventModifiers.Shift;
            }

            return modifiers;
        }

        private void HandleSmartSelection(Vector2 mousePosition)
        {
            // 从鼠标位置创建射线
            Ray ray = HandleUtility.GUIPointToWorldRay(mousePosition);

            // 获取所有UI元素
            List<GameObject> uiElements = FindAllUiElements();

            // 筛选符合条件的UI元素
            List<GameObject> candidates = FilterUiElements(uiElements);

            // 找到最适合的UI元素
            GameObject bestCandidate = FindBestCandidate(candidates, ray);

            if (bestCandidate != null)
            {
                // 选择最佳候选
                Selection.activeGameObject = bestCandidate;

                // 更新最近选择列表
                UpdateRecentSelections(bestCandidate);

                // 高亮显示选择
                HighlightSelection(bestCandidate);
            }
        }

        private List<GameObject> FindAllUiElements()
        {
            // 查找场景中所有Canvas下的UI元素
            Canvas[] canvases = FindObjectsOfType<Canvas>();
            List<GameObject> uiElements = new List<GameObject>();

            foreach (var canvas in canvases)
            {
                // 只考虑激活的Canvas
                if (!canvas.gameObject.activeInHierarchy) continue;

                // 递归查找所有子对象
                foreach (Transform child in canvas.transform)
                {
                    AddAllChildren(child, uiElements);
                }
            }

            return uiElements;
        }

        private void AddAllChildren(Transform parent, List<GameObject> list)
        {
            list.Add(parent.gameObject);
            foreach (Transform child in parent)
            {
                AddAllChildren(child, list);
            }
        }

        private List<GameObject> FilterUiElements(List<GameObject> elements)
        {
            List<GameObject> filtered = new List<GameObject>();

            foreach (var element in elements)
            {
                // 检查是否启用
                if (ignoreDisabled && !element.activeInHierarchy)
                    continue;

                // 检查是否可见
                if (selectOnlyVisible && !IsElementVisible(element))
                    continue;

                // 检查元素类型是否符合过滤条件
                if (!IsElementTypeAllowed(element))
                    continue;

                filtered.Add(element);
            }

            return filtered;
        }

        private bool IsElementVisible(GameObject element)
        {
            // 检查Graphic组件是否可见
            Graphic graphic = element.GetComponent<Graphic>();
            if (graphic != null)
            {
                return graphic.raycastTarget && graphic.IsActive();
            }

            // 对于没有Graphic的UI元素，简单认为可见
            return true;
        }

        private bool IsElementTypeAllowed(GameObject element)
        {
            if (element.GetComponent<Button>() != null)
                return includeButtons;

            if (element.GetComponent<Image>() != null &&
                element.GetComponent<Button>() == null) // 排除按钮的Image组件
                return includeImages;

            if (element.GetComponent<Text>() != null)
                return includeTexts;

            if (element.GetComponent<InputField>() != null)
                return includeInputFields;

            if (element.GetComponent<Slider>() != null)
                return includeSliders;

            // 自定义UI元素
            return includeCustomUI;
        }

        private GameObject FindBestCandidate(List<GameObject> candidates, Ray ray)
        {
            if (candidates.Count == 0)
                return null;

            // 为每个候选计算分数
            Dictionary<GameObject, float> scores = new Dictionary<GameObject, float>();

            foreach (var candidate in candidates)
            {
                float score = CalculateCandidateScore(candidate, ray);
                scores[candidate] = score;
            }

            // 按分数排序，取最高的
            return scores.OrderByDescending(pair => pair.Value).First().Key;
        }

        private float CalculateCandidateScore(GameObject candidate, Ray ray)
        {
            float score = 0;

            // 获取RectTransform
            RectTransform rectTransform = candidate.GetComponent<RectTransform>();
            if (rectTransform == null)
                return 0;

            // 计算射线到UI元素的距离
            float distance = CalculateDistanceToUiElement(rectTransform, ray);
            if (distance > selectionDistance)
                return 0;

            // 距离越近，分数越高
            score += (selectionDistance - distance) / selectionDistance * 50;

            // 交互元素加分
            if (preferInteractive && IsInteractiveElement(candidate))
                score += 30;

            // 层级较深的元素减分（优先选择顶层元素）
            int depth = GetElementDepth(candidate);
            score -= depth * 2;

            return score;
        }

        private float CalculateDistanceToUiElement(RectTransform rectTransform, Ray ray)
        {
            // 将UI元素转换为世界空间的矩形
            Vector3[] corners = new Vector3[4];
            rectTransform.GetWorldCorners(corners);

            // 计算射线与UI平面的交点
            Plane plane = new Plane(corners[0], corners[1], corners[2]);
            float enter;

            if (plane.Raycast(ray, out enter))
            {
                Vector3 hitPoint = ray.GetPoint(enter);

                // 检查点是否在矩形内
                Rect rect = new Rect(rectTransform.rect);
                RectTransformUtility.ScreenPointToLocalPointInRectangle(
                    rectTransform,
                    RectTransformUtility.WorldToScreenPoint(null, hitPoint),
                    null,
                    out Vector2 localPoint);

                if (rect.Contains(localPoint))
                {
                    return 0; // 点在UI元素内，距离为0
                }
                else
                {
                    // 计算到矩形边缘的距离
                    return CalculateDistanceToRectEdge(localPoint, rect);
                }
            }

            return float.MaxValue;
        }

        private float CalculateDistanceToRectEdge(Vector2 point, Rect rect)
        {
            // 计算点到矩形边缘的距离
            float dx = Mathf.Max(rect.xMin - point.x, point.x - rect.xMax, 0);
            float dy = Mathf.Max(rect.yMin - point.y, point.y - rect.yMax, 0);
            return Mathf.Sqrt(dx * dx + dy * dy);
        }

        private bool IsInteractiveElement(GameObject element)
        {
            return element.GetComponent<Button>() != null ||
                   element.GetComponent<InputField>() != null ||
                   element.GetComponent<Slider>() != null ||
                   element.GetComponent<Toggle>() != null;
        }

        private int GetElementDepth(GameObject element)
        {
            int depth = 0;
            Transform current = element.transform;

            while (current.parent != null)
            {
                depth++;
                current = current.parent;
            }

            return depth;
        }

        private void UpdateRecentSelections(GameObject selected)
        {
            // 移除已存在的相同对象
            lastSelectedObjects.Remove(selected);

            // 添加到列表开头
            lastSelectedObjects.Insert(0, selected);

            // 限制列表长度
            if (lastSelectedObjects.Count > 10)
            {
                lastSelectedObjects.RemoveRange(10, lastSelectedObjects.Count - 10);
            }
        }

        private void HighlightSelection(GameObject selected)
        {
            // 创建一个临时的选择高亮
            Selection.activeGameObject = selected;

            // 闪烁选择框以突出显示
            EditorApplication.delayCall += () =>
            {
                SceneView.RepaintAll();
                EditorApplication.delayCall += () => SceneView.RepaintAll();
            };
        }
    }
}