using UnityEngine;
using UnityEditor;
using UnityEngine.UI;

namespace SmartUISelection
{
    [InitializeOnLoad]
    public class SmartUISelectionToolbar
    {
        static SmartUISelectionToolbar()
        {
            // 注册工具栏绘制事件
            SceneView.duringSceneGui -= OnSceneGUI;
            SceneView.duringSceneGui += OnSceneGUI;
        }

        private static void OnSceneGUI(SceneView sceneView)
        {
            // 在工具栏添加一个按钮
            Handles.BeginGUI();

            GUILayout.BeginArea(new Rect(10, 10, 300, 30));
            GUILayout.BeginHorizontal();

            if (GUILayout.But<PERSON>("Smart UI Selector", EditorStyles.toolbarButton))
            {
                SmartUISelectorWindow.ShowWindow();
            }

            if (GUILayout.But<PERSON>("Select Parent Canvas", EditorStyles.toolbarButton))
            {
                SelectParentCanvas();
            }

            GUILayout.EndHorizontal();
            GUILayout.EndArea();

            Handles.EndGUI();
        }

        private static void SelectParentCanvas()
        {
            if (Selection.activeGameObject != null)
            {
                // 查找选中对象的父Canvas
                Canvas canvas = Selection.activeGameObject.GetComponentInParent<Canvas>();
                if (canvas != null)
                {
                    Selection.activeGameObject = canvas.gameObject;
                }
                else
                {
                    Debug.LogWarning("Selected object is not part of a Canvas");
                }
            }
        }
    }
}