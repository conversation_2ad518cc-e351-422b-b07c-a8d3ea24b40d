using UnityEngine;
using UnityEngine.UI;
using UnityEditor;
using UnityEngine.EventSystems;
using System.Collections.Generic;
using Unity.VisualScripting;
using UnityEngine.UI.Extensions;
using Menu = UnityEditor.Menu;

[InitializeOnLoad]
public static class UGUIQuickSelector
{
    private static bool isResetAllGraphics;
    private static bool isDrag;
    private static bool isDown;
    private static bool isMoved;

    // 插件开关状态
    private static bool isEnabled = false;


    // 高亮颜色
    private static Color highlightColor = new Color(0.8f, 0.8f, 0.8f, 0.6f);

    // 缓存的UI元素
    private static Graphic hoveredGraphic;
    private static Graphic[] allGraphics;

    static UGUIQuickSelector()
    {
        // 先移除可能存在的旧事件处理器，然后重新注册以确保优先级
        SceneView.duringSceneGui -= OnSceneGUI;
        SceneView.duringSceneGui += OnSceneGUI;

        // 初始化时刷新UI元素列表
        ResetGraphicsList();
        EditorApplication.hierarchyChanged -= ResetGraphicsList;
        EditorApplication.hierarchyChanged += ResetGraphicsList;
    }

    private static void OnSceneGUI(SceneView sceneView)
    {
        // 在工具栏添加一个按钮
        Handles.BeginGUI();

        GUILayout.BeginArea(new Rect(10, 10, 300, 30));
        GUILayout.BeginHorizontal();

        if (GUILayout.Button("Smart UI Selector", EditorStyles.toolbarButton))
        {
            ToggleUGUIQuickSelector();
        }

        GUILayout.EndHorizontal();
        GUILayout.EndArea();

        Handles.EndGUI();
    }

    // 刷新UI元素列表
    private static void ResetGraphicsList()
    {
        isResetAllGraphics = true;
    }

    private static Graphic[] GetAllGraphics()
    {
        if (isResetAllGraphics)
        {
            allGraphics = Object.FindObjectsOfType<Graphic>();
        }

        return allGraphics;
    }

    // 菜单选项 - 切换启用状态，并显示是否启用（√） (Ctrl + Alt + Q)
    [MenuItem("Tools/UGUI Quick Selector %&q")]
    private static void ToggleUGUIQuickSelector()
    {
        isEnabled = !isEnabled;
        string status = isEnabled ? "启用" : "禁用";
        ShowNotification($"UGUI Quick Selector 已{status}");

        // 如果启用，刷新UI元素列表
        if (isEnabled)
        {
            ResetGraphicsList();
        }
    }

    // 菜单选项验证 - 显示当前状态（勾选表示启用）
    [MenuItem("Tools/UGUI Quick Selector %&q", true)]
    private static bool ValidateToggleUGUIQuickSelector()
    {
        Menu.SetChecked("Tools/UGUI Quick Selector %&q", isEnabled);
        return true;
    }

    // 显示通知
    private static void ShowNotification(string message)
    {
        EditorWindow.focusedWindow?.ShowNotification(new GUIContent(message));
        Debug.Log(message);
    }

    private static void OnSceneGUI(SceneView sceneView)
    {
        if (!isEnabled) return;

        Event currentEvent = Event.current;

        // 提前处理事件，确保优先级
        if (currentEvent != null)
        {
            HandleUIHoverAndSelection(currentEvent, sceneView);
        }
    }

    private static void HandleUIHoverAndSelection(Event currentEvent, SceneView sceneView)
    {
        // 获取当前鼠标位置的射线
        Ray worldRay = HandleUtility.GUIPointToWorldRay(currentEvent.mousePosition);
        hoveredGraphic = null;
        float closestDistance = float.MaxValue;

        // 检查所有UI元素是否被射线击中
        foreach (Graphic graphic in GetAllGraphics())
        {
            if (graphic == null || !graphic.isActiveAndEnabled) continue;

            if (!graphic.gameObject.activeInHierarchy) continue;

            if (graphic is UIParticleSystem) continue;

            RectTransform rectTransform = graphic.rectTransform;
            if (rectTransform == null) continue;

            // 计算射线是否击中UI元素
            if (IsRayHittingRectTransform(worldRay, rectTransform, out float distance))
            {
                if (distance < closestDistance)
                {
                    closestDistance = distance;
                    hoveredGraphic = graphic;
                }
            }
        }

        // 如果有悬停的UI元素，处理所有相关事件
        if (hoveredGraphic != null)
        {
            if (currentEvent.button == 0)
            {
                DrawHighlight(hoveredGraphic.rectTransform);
            }

            // 拦截所有鼠标事件，防止传递给其他脚本
            if (currentEvent.type == EventType.MouseDown ||
                currentEvent.type == EventType.MouseUp ||
                currentEvent.type == EventType.MouseDrag ||
                currentEvent.type == EventType.MouseMove && currentEvent.button == 0)
            {
                if (currentEvent.type == EventType.KeyDown)
                {
                    isMoved = false;
                    isDown = true;
                }

                if (currentEvent.type == EventType.MouseMove)
                {
                    isMoved = true;
                    if (isDown)
                    {
                        isDrag = true;
                    }
                }

                // 处理鼠标点击选择
                if (currentEvent.type == EventType.MouseDown)
                {
                    Selection.activeObject = hoveredGraphic.gameObject;
                    EditorGUIUtility.PingObject(hoveredGraphic.gameObject);
                }

                if (currentEvent.type == EventType.MouseUp)
                {
                    isDown = false;
                    if (!isDrag)
                    {
                        //没有isDrag，算点击。此时判断重新选中父节点组件
                        Debug.LogError("点击");
                    }

                    // 完全吞掉事件，阻止传递给其他处理器
                    currentEvent.Use();
                }


                // 强制重绘场景视图
                sceneView.Repaint();
            }
        }
    }

    // 检查射线是否击中RectTransform，scene面板的视角和鼠标位置
    private static bool IsRayHittingRectTransform(Ray ray, RectTransform rectTransform, out float distance)
    {
        distance = 0;
        return RectTransformUtility.RectangleContainsScreenPoint(rectTransform, ray.origin, null);
    }

    // 绘制高亮效果
    private static void DrawHighlight(RectTransform rectTransform)
    {
        Vector3[] corners = new Vector3[4];
        rectTransform.GetWorldCorners(corners);

        Handles.color = highlightColor;
        Handles.DrawSolidRectangleWithOutline(corners, highlightColor, Color.white);

        // 强制刷新场景视图
        HandleUtility.Repaint();
    }
}