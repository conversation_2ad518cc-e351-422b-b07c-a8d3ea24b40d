using UnityEngine;
using UnityEngine.UI;
using UnityEditor;
using UnityEngine.EventSystems;
using System.Collections.Generic;

[InitializeOnLoad]
public static class UGUIQuickSelector
{
    // 插件开关状态
    private static bool isEnabled = false;


    // 高亮颜色
    private static Color highlightColor = new Color(0.2f, 0.6f, 1f, 0.3f);

    // 缓存的UI元素
    private static Graphic hoveredGraphic;
    private static List<Graphic> allGraphics = new List<Graphic>();

    static UGUIQuickSelector()
    {
        // 注册编辑器事件
        SceneView.duringSceneGui += OnSceneGUI;

        // 初始化时刷新UI元素列表
        RefreshGraphicsList();
        EditorApplication.hierarchyChanged += RefreshGraphicsList;
    }

    // 刷新UI元素列表
    private static void RefreshGraphicsList()
    {
        allGraphics.Clear();
        Graphic[] graphics = Object.FindObjectsOfType<Graphic>();
        allGraphics.AddRange(graphics);
    }

    // 菜单选项 - 切换启用状态，并显示是否启用（√） (Ctrl + Alt + Q)
    [MenuItem("Tools/UGUI Quick Selector %&q")]
    private static void ToggleUGUIQuickSelector()
    {
        isEnabled = !isEnabled;
        string status = isEnabled ? "启用" : "禁用";
        ShowNotification($"UGUI Quick Selector 已{status}");

        // 如果启用，刷新UI元素列表
        if (isEnabled)
        {
            RefreshGraphicsList();
        }
    }

    // 菜单选项验证 - 显示当前状态（勾选表示启用）
    [MenuItem("Tools/UGUI Quick Selector %&q", true)]
    private static bool ValidateToggleUGUIQuickSelector()
    {
        Menu.SetChecked("Tools/UGUI Quick Selector %&q", isEnabled);
        return true;
    }

    // 显示通知
    private static void ShowNotification(string message)
    {
        EditorWindow.focusedWindow?.ShowNotification(new GUIContent(message));
        Debug.Log(message);
    }

    private static void OnSceneGUI(SceneView sceneView)
    {
        if (!isEnabled) return;

        Event currentEvent = Event.current;
        HandleUIHoverAndSelection(currentEvent, sceneView);
    }

    private static void HandleUIHoverAndSelection(Event currentEvent, SceneView sceneView)
    {
        // 获取当前鼠标位置的射线
        Ray worldRay = HandleUtility.GUIPointToWorldRay(currentEvent.mousePosition);
        hoveredGraphic = null;
        float closestDistance = float.MaxValue;

        // 检查所有UI元素是否被射线击中
        foreach (Graphic graphic in allGraphics)
        {
            if (graphic == null || !graphic.isActiveAndEnabled) continue;

            RectTransform rectTransform = graphic.rectTransform;
            if (rectTransform == null) continue;

            // 计算射线是否击中UI元素
            if (IsRayHittingRectTransform(worldRay, rectTransform, out float distance))
            {
                if (distance < closestDistance)
                {
                    closestDistance = distance;
                    hoveredGraphic = graphic;
                }
            }
        }

        // 绘制高亮
        if (hoveredGraphic != null)
        {
            DrawHighlight(hoveredGraphic.rectTransform);

            // 处理鼠标点击选择
            if (currentEvent.type == EventType.MouseDown && currentEvent.button == 0)
            {
                Selection.activeObject = hoveredGraphic.gameObject;
                currentEvent.Use();
            }
        }
    }

    // 检查射线是否击中RectTransform，scene面板的视角和鼠标位置
    private static bool IsRayHittingRectTransform(Ray ray, RectTransform rectTransform, out float distance)
    {
        distance = 0;
        return RectTransformUtility.RectangleContainsScreenPoint(rectTransform, ray.origin, null);
    }

    // 绘制高亮效果
    private static void DrawHighlight(RectTransform rectTransform)
    {
        Vector3[] corners = new Vector3[4];
        rectTransform.GetWorldCorners(corners);

        Handles.color = highlightColor;
        Handles.DrawSolidRectangleWithOutline(corners, highlightColor, Color.white);

        // 强制刷新场景视图
        HandleUtility.Repaint();
    }
}