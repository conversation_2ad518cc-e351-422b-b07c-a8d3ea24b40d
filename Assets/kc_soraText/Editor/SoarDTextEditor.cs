using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using System;
using System.Reflection;
using UnityEngine.UI;

[CustomEditor(typeof(SoarDText))]
//[CanEditMultipleObjects]
public class SoarDTextEditor : UnityEditor.UI.TextEditor
{
    // SoarDText m_textEx;    //TextEx对象

    SerializedProperty m_Text;
    SerializedProperty m_FontData;

    SerializedProperty m_isStatic;
    SerializedProperty m_key;
    SerializedProperty m_supportEmoji;
    SerializedProperty supportHyperLink;
    SerializedProperty m_isEllipsis;

    SerializedProperty m_PointerTween;
    SerializedProperty m_TweenScale;

    protected override void OnEnable()
    {
        base.OnEnable();
        m_Text = serializedObject.FindProperty("m_Text");
        m_FontData = serializedObject.FindProperty("m_FontData");
        // m_textEx = (SoarDText)target;


        m_isStatic = serializedObject.FindProperty("m_isStatic");
        m_key = serializedObject.FindProperty("m_key");
        m_supportEmoji = serializedObject.FindProperty("m_supportEmoji");
        supportHyperLink = serializedObject.FindProperty("supportHyperLink");
        m_isEllipsis = serializedObject.FindProperty("m_isEllipsis");

        m_PointerTween = serializedObject.FindProperty("m_PointerTween");
        m_TweenScale = serializedObject.FindProperty("m_TweenScale");

        // if (m_textEx.colorType == BaseDef.TextFontColor.None)//不为空的话
        // {
        //     Debug.LogError("Color Type 不允许为空，请选择颜色类型", m_textEx.gameObject);
        // }
    }

    private static BindingFlags flags = BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance;

    public override void OnInspectorGUI()
    {
        EditorGUILayout.LabelField("拓展：");
        // BaseDef.TextFontColor colorType = (BaseDef.TextFontColor)EditorGUILayout.EnumPopup("ColorType:", m_textEx.colorType);
        // if(colorType != m_textEx.colorType)
        // {
        //     m_textEx.color = BaseDef.ColorDef[(int)colorType];
        //     m_textEx.colorType = colorType;
        // }
        EditorGUILayout.PropertyField(m_isStatic, new GUIContent("IsStatic"));
        if (m_isStatic.boolValue)
        {
            EditorGUILayout.PropertyField(m_key, new GUIContent("Key"));
        }

        EditorGUILayout.PropertyField(m_supportEmoji, new GUIContent("IsSupportEmoji"));
        EditorGUILayout.PropertyField(supportHyperLink, new GUIContent("IsSupportHyperlink"));
        EditorGUILayout.PropertyField(m_isEllipsis, new GUIContent("IsEllipsis（仅支持顶部定位）"));

        EditorGUILayout.PropertyField(m_PointerTween);
        EditorGUILayout.PropertyField(m_TweenScale);

        EditorGUILayout.LabelField("------------------------");

        if (GUI.changed)
            EditorUtility.SetDirty(target);
        serializedObject.ApplyModifiedProperties();

        // base.OnInspectorGUI();
        serializedObject.Update();

        EditorGUILayout.PropertyField(m_Text);
        EditorGUILayout.PropertyField(m_FontData);

        AppearanceControlsGUI();
        RaycastControlsGUI();
        MaskableControlsGUI();
        serializedObject.ApplyModifiedProperties();


        SoarDText text = target as SoarDText;
        if (text)
        {
            EditorGUI.BeginDisabledGroup(true);
            EditorGUILayout.FloatField("PreferredWidth", text.preferredWidth);
            EditorGUILayout.FloatField("PreferredHeight", text.preferredHeight);
            EditorGUI.EndDisabledGroup();
        }
    }
}