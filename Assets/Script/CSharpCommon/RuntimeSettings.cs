using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using LitJson;
using UnityEngine.UI;

public class CountryData
{
    public int id;
    public int prop;
    public int zone;
    public string country;
    public string country2;
    public string lang;
    public string pack;
}

public enum PackageChannel 
{
    Normal = 1,
    HuaWei = 2,
}
public enum PackageType
{
    EN = 1,
    EA = 2,
    SEA = 3,
    CN = 4,
    CNVX = 5,
    CNDB = 6,
}
public static partial class RuntimeSettings
{
    public static bool Update = false;
    public static bool ISCMPEnabled = false;

    public static Versions Version = Versions.FromString("0.0.3");

    public static string baseVersion = "0.0.1";

    public static string country = "";

    public static string review = "";
    public static string countryKey = "country";
    public static string ZoneKey = "ZoneKey";
    public static void SyncCountry(string remoteCountryCode)
    {
        string localCountry = PlayerPrefs.GetString(countryKey);
        if (string.IsNullOrEmpty(localCountry))
        {
            SetCountry(remoteCountryCode);
        }
        else
        {
            country = localCountry;
        }
        CalcZoneID();
    }
    private static void SetCountry(string localCountry)
    {
        PlayerPrefs.SetString(countryKey, localCountry);
        PlayerPrefs.Save();
        country = localCountry;
    }
    public static void CalcZoneID()
    {
        Debug.Log("1 ServerZoneID" + ServerZoneID);
        //先以本地为主 没有的做分服
        if (ServerZoneID > 0)
        {
            return;
        }
        int newZoneID = PlayerPrefs.GetInt(ZoneKey, 0);
        Debug.Log("newZoneID" + newZoneID);
        if (newZoneID > 0)
        {
            ServerZoneID = newZoneID;
            return;
        }
        TextAsset textAsset = Resources.Load<TextAsset>("Country");

        List<CountryData> countryDatas = JsonMapper.ToObject<List<CountryData>>(textAsset.text);
        Debug.Log("1 country" + country);
        if (string.IsNullOrEmpty(country))
        {
            string curLanguage = OneMT.SDK.OneMTTool.deviceLanguage().ToLower();
            Debug.Log("curLanguage" + curLanguage);

            int proptery = -1;
            foreach (CountryData counntryItem in countryDatas)
            {
                if (counntryItem.pack == g && counntryItem.prop > proptery)
                {
                    string[] langArry = counntryItem.lang.Split(",");
                    foreach (string lang in langArry)
                    {
                        if (lang.ToLower().Trim() == curLanguage.ToLower().Trim())
                        {
                            country = counntryItem.country;
                            proptery = counntryItem.prop;
                        }
                    }
                }
            }
        }
        Debug.Log("2 country" + country);

        if (!string.IsNullOrEmpty(country))
        {
            foreach (CountryData counntryItem in countryDatas)
            {
                if (counntryItem.country == country || counntryItem.country2 == country)
                {
                    if (counntryItem.pack == g)
                    {
                        ServerZoneID = counntryItem.zone;
                        PlayerPrefs.SetInt(ZoneKey, ServerZoneID);
                        PlayerPrefs.Save();
                        Debug.Log("2 ServerZoneID" + ServerZoneID);
                        Debug.Log("counntryItem.country" + counntryItem.country);
                        Debug.Log("counntryItem pac" + counntryItem.pack);

                        return;
                    }
                }
            }
        }
        int serverproptery = -1;
        foreach (CountryData counntryItem in countryDatas)
        {
            if (counntryItem.pack == g)
            {
                if (counntryItem.prop > serverproptery)
                {
                    ServerZoneID = counntryItem.zone;
                    serverproptery = counntryItem.prop;
                }
            }
        }
        Debug.Log("3 ServerZoneID" + ServerZoneID);

        PlayerPrefs.SetInt(ZoneKey, ServerZoneID);
        PlayerPrefs.Save();
    }
#if UNITY_ANDROID
    public static RuntimePlatformType Platform = RuntimePlatformType.Android;
#elif UNITY_IPHONE
    public static RuntimePlatformType Platform = RuntimePlatformType.iOS;
#elif UNITY_IOS
    public static RuntimePlatformType Platform = RuntimePlatformType.iOS;
#elif UNITY_WP8
    public static RuntimePlatformType Platform = RuntimePlatformType.WP;
#elif UNITY_EDITOR
    public static RuntimePlatformType Platform = RuntimePlatformType.Android;
#elif UNITY_STANDALONE_WIN
    public static RuntimePlatformType Platform = RuntimePlatformType.Win;
#else
    public static RuntimePlatformType Platform = RuntimePlatformType.Win;
#endif

    public static bool IsAudit = false;
    public static string PingJiaAndroidKey = "com.android.vending";
#if ONEMT_CDN_EN
    public static PackageType PackageType = PackageType.EN;
     public static int ServerZoneID = -1;
     public static string g = "mg";
     public static string PingJiaIOSKey = "6739125107";
#if UNITY_ANDROID
    public static string appUrl = "com.android.vending";
    //public static string appUrl = "https://play.google.com/store/apps/details?id=com.and.monstergo.online";
#elif UNITY_IOS
    public static string appUrl = "https://apps.apple.com/app/mech-assemble/id6739125107";
#endif
#elif ONEMT_CDN_EA
        public static PackageType PackageType = PackageType.EA;
        public static int ServerZoneID = 1003;
        public static string g = "mg_ea";
        public static string PingJiaIOSKey = "6742772771";
#if UNITY_ANDROID
    //public static string appUrl = "https://play.google.com/store/apps/details?id=com.and.mecharoguejp.online";
    public static string appUrl = "com.android.vending";
#elif UNITY_IOS
    public static string appUrl = "https://apps.apple.com/us/app/%E3%83%A1%E3%82%AB%E3%82%A2%E3%82%B5%E3%83%AB%E3%83%88/id6742772771";
#endif

#elif ONEMT_CDN_SEA
    public static int ServerZoneID = 1004;
    public static string g = "mg_sea";
    public static PackageType PackageType = PackageType.SEA;
    public static string PingJiaIOSKey = "6739125107";
#if UNITY_ANDROID
    //public static string appUrl = "https://play.google.com/store/apps/details?id=com.and.monstergo.online";
    public static string appUrl = "com.android.vending";

#elif UNITY_IOS
    public static string appUrl = "https://apps.apple.com/app/mech-assemble/id6739125107";
#endif

#elif ONEMT_CDN_CN
    public static int ServerZoneID = 1007;
    public static string g = "mg_cn";
    public static PackageType PackageType = PackageType.CN;
    public static string PingJiaIOSKey = "6739125107";
#if UNITY_ANDROID
    //public static string appUrl = "https://play.google.com/store/apps/details?id=com.and.monstergo.online";
    public static string appUrl = "com.android.vending";

#elif UNITY_IOS
    public static string appUrl = "https://apps.apple.com/app/mech-assemble/id6739125107";
#endif

#elif ONEMT_CDN_EN_TEST
    public static string PingJiaIOSKey = "6739125107";
    public static string g = "mg";
    public static int ServerZoneID = 1;
    public static PackageType PackageType = PackageType.EN;
#if UNITY_ANDROID
    //public static string appUrl = "https://play.google.com/store/apps/details?id=com.and.mecharoguejp.online";
    public static string appUrl = "com.android.vending";
#elif UNITY_IOS
    public static string appUrl = "https://apps.apple.com/us/app/%E3%83%A1%E3%82%AB%E3%82%A2%E3%82%B5%E3%83%AB%E3%83%88/id6742772771";
#endif
#elif ONEMT_CDN_EA_TEST
    public static PackageType PackageType = PackageType.EA;
    public static int ServerZoneID = 2;
    public static string g = "mg_ea";
    public static string PingJiaIOSKey = "6742772771";
#if UNITY_ANDROID
    //public static string appUrl = "https://play.google.com/store/apps/details?id=com.and.monstergo.online";
     public static string appUrl = "com.android.vending";

#elif UNITY_IOS
    public static string appUrl = "https://apps.apple.com/app/mech-assemble/id6739125107";
#endif

#elif ONEMT_CDN_SEA_TEST
    public static int ServerZoneID = 3;
    public static string g = "mg_sea";
    public static PackageType PackageType = PackageType.SEA;
    public static string PingJiaIOSKey = "6739125107";
#if UNITY_ANDROID
    //public static string appUrl = "https://play.google.com/store/apps/details?id=com.and.monstergo.online";
    public static string appUrl = "com.android.vending";

#elif UNITY_IOS
    public static string appUrl = "https://apps.apple.com/app/mech-assemble/id6739125107";
#endif
#elif ONEMT_CDN_CN_TEST
    public static int ServerZoneID = 3;
    public static string g = "mg_cn";
    public static PackageType PackageType = PackageType.CN;
    public static string PingJiaIOSKey = "6739125107";
#if UNITY_ANDROID
    //public static string appUrl = "https://play.google.com/store/apps/details?id=com.and.monstergo.online";
    public static string appUrl = "com.android.vending";

#elif UNITY_IOS
    public static string appUrl = "https://apps.apple.com/app/mech-assemble/id6739125107";
#endif

#else
    public static string PingJiaIOSKey = "6739125107";
    public static string g = "mg";
    public static int ServerZoneID = 1;
    public static PackageType PackageType = PackageType.EN;
    public static string appUrl = "com.android.vending";
    //public static string appUrl = "https://play.google.com/store/apps/details?id=com.onemt.and.shoujokaisen.test";
#endif
    
    #if !ONEMT_HUAWEI
        public static PackageChannel Channel = PackageChannel.Normal;
#else
        public static PackageChannel Channel = PackageChannel.HuaWei;
#endif



#if ONEMT_CDN_RELEASE
    public static bool Release = true;
    public static List<string> VersionURL = new List<string>()
    {
        "http://aliyunversion.menaapp.net/version.php",
        "http://sgversion.menaapp.net/version.php",
        "http://awsversion.menaapp.net/version.php",
    };
#elif ONEMT_CDN_CN_RELEASE
    public static bool Release = true;
    public static List<string> VersionURL = new List<string>()
    {
        "http://aliyunversion.menaapp.net/version.php",
        "http://sgversion.menaapp.net/version.php",
        "http://awsversion.menaapp.net/version.php",
    };

#else
    public static List<string> VersionURL = new List<string>()
    {
        "http://versiontest.onemt.co/version.php",
        "http://versiontest.onemt.co/version.php",
        "http://versiontest.onemt.co/version.php"
    };
    public static bool Release = false;
#endif

}
public enum RuntimePlatformType
{
    Current,
    Win,
    OSX,
    Android,
    iOS,
    WP
}