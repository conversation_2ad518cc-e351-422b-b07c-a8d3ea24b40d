using OneMT.SDK;
using OneMT.SDK.Ad;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDOneMTMgr
    {
        public bool ShowPayBack = false;
        public void Init()
        {
            if (RuntimeSettings.UseSDK)
            {
                if (!RuntimeSettings.Release)
                {
                    OneMT.SDK.OneMTSDK.SetLogEnabled(true);
                }
            }
        }
        public void AfterInit()
        {
            if (!RuntimeSettings.UseSDK)
            {
                return;
            }

            FreshSDKLang();
            TryRegistCMP();
        }

        public void FreshSDKLang()
        {
            if (!RuntimeSettings.UseSDK)
            {
                return;
            }
            
            string curLanguage = Global.gApp.gGameData.GetClientCurLanguage();
            int sdkId = -1;

            foreach (LanguageItem item in Language.Data.items)
            {
                if (item.abbr.Equals(curLanguage))
                {
                    sdkId = item.sdkId;
                    break;
                }
            }
            if (sdkId > 0)
            {
                OneMT.SDK.OneMTSDK.setAppLanguage((OneMT.SDK.OneMTAppLanguage)(sdkId));
            }
            else
            {
                OneMT.SDK.OneMTSDK.setAppLanguage(OneMT.SDK.OneMTAppLanguage.English);
            }
        }

        public void TryRegistCMP()
        {
            Action<bool> callback = delegate (bool isArgee) {
                if (!isArgee)
                {
                    //执行退出游戏
#if UNITY_EDITOR
                    UnityEditor.EditorApplication.isPlaying = false;
#else
                        Application.Quit();
#endif
                }
                else
                {
                    if(Global.gApp.CurScene is LoginScene)
                    {
                        StartLogin();
                    }
                }
            };
            OneMTTerms.RegisterTermsPrivacyAgreementCallback(callback);
        }

        public void StartLogin()
        {
            Global.gApp.gSystemMgr.gLoginAccountMgr.StartGetServerList();
        }
        public string deviceModel()
        {
            if (RuntimeSettings.UseSDK)
            {
                return OneMT.SDK.OneMTTool.deviceModel();
            }
            return string.Empty;
        }
        public string OSVersion()
        {
            if (RuntimeSettings.UseSDK)
            {
                return OneMT.SDK.OneMTTool.OSVersion();
            }
            return string.Empty;
        }
        public string deviceLanguage()
        {
            if (RuntimeSettings.UseSDK)
            {
                return OneMT.SDK.OneMTTool.deviceLanguage();
            }
            else
            {
                return string.Empty;
            }
        }
        public string carrierName()
        {
            if (RuntimeSettings.UseSDK)
            {
                return OneMT.SDK.OneMTTool.carrierName();
            }
            else
            {
                return string.Empty;
            }
        }
        //在 游戏 从 后 台进入前台并处于上线状态，就可以调用该方法。该方法会触发执行推送消 息 等操作。
        public void reline()
        {
            if (RuntimeSettings.UseSDK)
            {
                Debug.Log("UseSDK reline");
                OneMT.SDK.OneMTSDK.reline();
            }
        }
        // 在游戏下线或者断开服务，你就可以调用该方法，该方法会触发关闭所有窗口，用户中心，社区客服 等 。
        public void offline()
        {
            if (RuntimeSettings.UseSDK)
            {
                Debug.Log("UseSDK offline");
                OneMT.SDK.OneMTSDK.offline();
            }
        }
        //建议在调用online的时候，同时也调用updateGamePlayer接口，便于及时更新玩家信息。
        public void online()
        {
            if (RuntimeSettings.UseSDK)
            {

                Debug.Log("UseSDK online");
                //string playerId, string playerName, string serverId, string gameVersion, string vipLevel
                string playerId = Global.gApp.gSystemMgr.gRoleMgr.GetPlayerId().ToString();
                string playerName = Global.gApp.gSystemMgr.gRoleMgr.GetRoleName();
                string serverId = Global.gApp.gSystemMgr.gLoginAccountMgr.GetCurServerId().ToString();

                UpdateVer nativeVersion = RuntimeSettings.GeCurVerition();
                OneMTPlayerInfo playerInfo = new OneMTPlayerInfo(playerId, playerName,
                    serverId, Global.gApp.gSdkMgr.GetZone().ToString(),
                    Global.gApp.gSystemMgr.gVipMgr.GetVipLevel().ToString(), new Hashtable());

                OneMT.SDK.OneMTSDK.Online(playerInfo, nativeVersion.GetResVer());
                Global.gApp.gSdkMgr.gADMgr.StartInit();
                Global.gApp.gSdkMgr.gQuerySurveyMgr.registerQuestionnaireDataUpdateListener();
                updateGamePlayer();
                QueryPaybackStatus();
                Global.gApp.gSdkMgr.gVipSDKMgr.QueryVipRewardStatus();
            }
        }
        public void updateGamePlayer()
        {
            //string playerName, string playerId, string serverId, 
            // string sysPhoto, string extension, Action< bool > callback
            if (RuntimeSettings.UseSDK)
            {
                string playerId = Global.gApp.gSystemMgr.gRoleMgr.GetPlayerId().ToString();
                string playerName = Global.gApp.gSystemMgr.gRoleMgr.GetRoleName();
                string serverId = Global.gApp.gSystemMgr.gLoginAccountMgr.GetCurServerId().ToString();
                UpdateVer nativeVersion = RuntimeSettings.GeCurVerition();
                OneMT.SDK.OneMTSDK.updateGamePlayer(playerName, playerId, serverId, "todo.png", string.Empty, null);
            }
        }

        public void ShowLoginService(bool isForced)
        {
            if (RuntimeSettings.UseSDK)
            {
                ShowService(isForced);
            }
            else
            {
                StartLogin();
            }
        }

        public void ShowService(bool isForced)
        {
            //string lang = Global.gApp.gGameData.GetSystemCurLanguage();
            //ShowSDKH5Page("https://pte.onemt.com/terms" + "?lang=" + lang);
            if (RuntimeSettings.UseSDK)
            {
                OneMTTerms.ShowTermsPrivacyAgreementPage(isForced);
            }
        }    
        public void ShowPrivacy(bool isForced)
        {
            //string lang = Global.gApp.gGameData.GetSystemCurLanguage();
            //ShowSDKH5Page("https://pte.onemt.com/policy" + "?lang=" + lang);
            if (RuntimeSettings.UseSDK)
            {
                OneMTTerms.ShowTermsPrivacyAgreementPage(isForced);
            }
        }
        public void ShowSDKH5Page(string url)
        {
            if (RuntimeSettings.UseSDK)
            {
                OneMTSupport.showH5Page(url);
            }
        }
        public double GetMemory()
        {
            if (RuntimeSettings.UseSDK)
            {
                return OneMTTool.getTotalMemory() - OneMTTool.getUsedMemory();
            }
            return 0;
        }
        public void openLinkInBrowser(string url)
        {
            if (RuntimeSettings.UseSDK)
            {
                OneMTSupport.openLinkInBrowser(url);
            }
        }
        public void OnQueryPaybackStatus()
        {

        }

        public string GetBundleId()
        {
            try
            {

                Debug.Log("Application.identifier" + Application.identifier);
                return Application.identifier;
            }
            catch (Exception e)
            {
                return string.Empty;
            }
        }
        public string GetChannel()
        {
            if (RuntimeSettings.UseSDK)
            {
                Hashtable info = OneMTSDK.getAppInfo();
                // string appId = info["appId"];
                // string sdkVersion = info["sdkVersion"];
                // string appVersion = info["appVersion"];
                string channel = info["channel"].ToString();
                // string publisher = info["publisher"];
                // int environment = info["environment"];
                return channel;
            }
            else
            {
                return "M";
            }
        }
        public bool IsHuaWeiChannel()
        {
            if (Application.platform == RuntimePlatform.Android)
            {
                string chanel = GetChannel();
                if (chanel == "huawei")
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return false;
            }
        }
        public void ShowPaybackView()
        {
            OneMTPayback.ShowPaybackView();
        }
        //.调用查询接口，如果有查询到有催款，SDK会自动弹窗催款弹窗。
        public void QueryPaybackStatus()
        {
            if(!Global.gApp.gSystemMgr.gPlayerAssembleFinishResponse)
            {
                return;
            }
            if(!RuntimeSettings.UseSDK)
            {
                return;
            }
            if(RuntimeSettings.PackageType == PackageType.CN)
            {
                return;
            }
            OneMTPayback.QueryPaybackStatus(delegate (Hashtable data)
            {
                if ((data["rtncode"] as string) == "current_status")
                {
                    Hashtable rspdata = data["rspdata"] as Hashtable;
                    bool isSuccess = (bool)rspdata["isSuccess"];
                    if (isSuccess)
                    {
                        //成功，取值
                        bool status = (bool)rspdata["status"];
                        ShowPayBack = status;
                        Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.MainUI, 101);
                        //bool closeable = rspdata["closeable"];
                        //bool isPopup = rspdata["isPopup"];
                    }
                }
                else if ((data["rtncode"] as string) == "switch_role")
                {
                    //进行切换角色处理
                    Global.gApp.gTimerMgr.AddTimer(0.1f, 1, (_, _) =>
                      {
                          Global.gApp.gSystemMgr.gLoginAccountMgr.TryShowServerList();
                          ServerChooseUI serverChooseUI = Global.gApp.gUiMgr.GetOpenPanelCompent(LDUICfg.ServerChooseUI) as ServerChooseUI;

                          serverChooseUI?.ShowPaybackStatus(true);


                      });
                }
            });
        }
    }
}
