using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDVipSDKMgr
    {
        public bool VipRewardViewShow { private set; get; } = false;
        public void Init()
        {
        }
        public void ShowVipRewardView()
        {
            if(RuntimeSettings.UseSDK)
            {
                OneMTEvent.ShowVipRewardView("2");
            }
        }

        public void QueryVipRewardStatus()
        {
            if (RuntimeSettings.UseSDK)
            {
                OneMTEvent.QueryVipRewardStatus(OnQueryVipRewardStatus);
            }
        }
        //建议场景：status=1-展示入口且有红点  102 表示状态
        private void OnQueryVipRewardStatus(int state)
        {
            VipRewardViewShow = state == 1;
        }
        private void UnregisterVipRewardStatusCallback()
        {
            if (RuntimeSettings.UseSDK)
            {
                //OneMTEvent.UnregisterVipRewardStatusCallback();
            }
        }
    }
}
