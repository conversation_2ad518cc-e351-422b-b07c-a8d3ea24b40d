using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDLoginCfg
    {
        private static int AuditId = 101;
        private static int AuditJPId = 103;
        private static int AuditCNId = 107;
        private static DEVServerConfigItem ServerItem = null;
        public static DEVServerConfigItem GetServerItem()
        {
            Debug.Log("ZoneID"  + RuntimeSettings.ServerZoneID);
            if(ServerItem != null)
            {
                if (ServerItem.id == RuntimeSettings.ServerZoneID)
                {
                    return ServerItem;
                }
            }
            //string countryCode = GetCountryCode();
            if (!RuntimeSettings.Release)
            {
                ServerItem = DEVServerConfig.Data.Get(RuntimeSettings.ServerZoneID);
            }
            else
            {
                if(RuntimeSettings.IsAudit)
                {
                    if (RuntimeSettings.PackageType == PackageType.EN)
                    {
                        ServerItem = DEVServerConfig.Data.Get(AuditId);
                    }
                    else if (RuntimeSettings.PackageType == PackageType.EA)
                    {
                        ServerItem = DEVServerConfig.Data.Get(AuditJPId);
                    } 
                    else if (RuntimeSettings.PackageType == PackageType.CN)
                    {
                        ServerItem = DEVServerConfig.Data.Get(AuditCNId);
                    }
                }
                else
                {
                    ServerItem = DEVServerConfig.Data.Get(RuntimeSettings.ServerZoneID);
                }
                if(ServerItem == null)
                {
                    ServerItem = DEVServerConfig.Data.Get(1001);
                }
            }
            return ServerItem;
        }
        //private static string GetCountryCode()
        //{
        //    if(!string.IsNullOrEmpty(RuntimeSettings.country))
        //    {
        //        DEVServerConfigItem serverItem = FindReleaseServerCfgItem(RuntimeSettings.country);
        //        if (serverItem == null)
        //        {
        //            ResetDefaultCountryCode();
        //        }
        //        return RuntimeSettings.country;
        //    }
        //    else
        //    {
        //        string curLanguage = UiTools.MachineLanguage();
        //        foreach (DEVLanguageCountryConfigItem item in Global.gApp.gGameData.DEVLanguageCountryConfig.items)
        //        {
        //            foreach (string language in item.language)
        //            {
        //                if (language.Equals(curLanguage))
        //                {
        //                    RuntimeSettings.SetCountry(item.country);
        //                    return item.country;
        //                }
        //            }
        //        }
        //        ResetDefaultCountryCode();
        //        return RuntimeSettings.country;
        //    }

        //}
        //private static void ResetDefaultCountryCode()
        //{
        //    RuntimeSettings.SetCountry("US");
        //}
        //private static DEVServerConfigItem FindReleaseServerCfgItem(string countryCode)
        //{
        //    if (string.IsNullOrEmpty(countryCode)) { return null; }
        //    foreach (DEVServerConfigItem item in DEVServerConfig.Data.items)
        //    {
        //        if (item.id != DevId && item.id != AuditId)
        //        {
        //            foreach (string country in item.country)
        //            {
        //                if (country.Equals(countryCode))
        //                {
        //                    return item;
        //                }
        //            }
        //        }
        //    }
        //    return null;
        //}
    }
}
