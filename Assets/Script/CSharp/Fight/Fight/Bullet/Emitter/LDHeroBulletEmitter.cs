
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDHeroBulletData : LDLinkModeData
    {
        public double AtkVal = 0;
        public double DamageParam = 0;
        public int BulletId = -1;
        public Vector3 Position;
        public float OffsetAngleY = 0;
        public float OffsetPosZ = 0;
        public float Scale = 1;
        public Transform FirePoint = null;
        public bool MainDeal = false;
        public int ParentAdd = 0;
        public int StartForward = 0;
        public LDHeroWeapon HeroWeapon;
        public LDHeroSubWeapon SubWeapon;
        
        public LDCiTiaoItemBase CiTiaoItem = null;
        public LDCitiaoBuffMgr CitiaoBuffMgr;
        public override void SetEnabled(bool enable)
        {
            base.SetEnabled(enable);
            if (!enable)
            {
                Scale = 1;
                BulletId = -1;
                FirePoint = null;
                MainDeal = false;
                CitiaoBuffMgr = null;
                CiTiaoItem = null;
                ParentAdd = 0;
                StartForward = 0;
                HeroWeapon = null;
                SubWeapon = null;
            }
        }
        public bool MatchMax()
        {
            return Global.gApp.CurFightScene.gBulletMgr.ReachMaxBulletCount(BulletId);
        }
    }
    public class LDBulletData
    {
        public int BulletId = -1;
        public int ClipCount = -1;
        public int ClipSize = -1;
    }
    
    public class LDHeroBulletEmitter : IUpdate
    {
        public LDBulletData BulletData { private set; get; }
        public LDBulletData SubBulletData { private set; get; }
        private LDFiniteLinkList<LDHeroBulletData> m_FiniteLinkList;
        private LDHeroPlayer m_HeroPlayer;
        private LDMainRole m_MainRole;
        public LDHeroBulletEmitter(LDHeroPlayer player)
        {
            BulletData = new LDBulletData();
            SubBulletData = new LDBulletData();
            m_HeroPlayer = player;
            m_MainRole = player.HeroMgr.MainRole;
            m_FiniteLinkList = new LDFiniteLinkList<LDHeroBulletData>();
            m_FiniteLinkList.Init(128,10, SummonDelayBulletImp);
        }
        // 枪打出来的
        public LDTrackBullet GetBullet(int bulletId,double atk,LDCitiaoBuffMgr citiaoBuffMgr, LDBaseWeapon heroWeapon = null)
        {
            LDTrackBullet heroBullet = Global.gApp.CurFightScene.gBulletMgr.GetBullet(bulletId);
            heroBullet.AtkData.HeroPlayer = m_HeroPlayer;
            heroBullet.AtkData.CiTiaoBuffMgr = citiaoBuffMgr;
            CalcAtkParam(heroBullet, atk, citiaoBuffMgr, heroWeapon);
            CalcBuffInfo(heroBullet, heroWeapon);
            float scale = GetBulletScale(heroBullet.BulletItem, heroWeapon);
            heroBullet.AtkData.BulletScale = Vector3.one * scale;
            heroBullet.transform.localScale = heroBullet.AtkData.BulletScale;
            return heroBullet;
        }
        public LDTrackBullet FireByWeapon(int bulletId, double atk, LDCitiaoBuffMgr citiaoBuffMgr, LDHeroWeapon heroWeapon)
        {
            LDTrackBullet heroBullet = GetBullet(bulletId, atk,citiaoBuffMgr, heroWeapon);
            heroBullet.AtkData.OriBulletId = bulletId;
            heroBullet.AtkData.MainDeal = true;
            heroBullet.AtkData.Weapon = heroWeapon;
            return heroBullet;
        }
        public LDTrackBullet FireBySubWeapon(int bulletId, double atk, LDCitiaoBuffMgr citiaoBuffMgr, LDHeroSubWeapon heroWeapon)
        {
            LDTrackBullet heroBullet = GetBullet(bulletId, atk, citiaoBuffMgr, heroWeapon);
            heroBullet.AtkData.OriBulletId = bulletId;
            heroBullet.AtkData.MainDeal = true;
            heroBullet.AtkData.SubWeapon = heroWeapon;
            return heroBullet;
        }
        public LDTrackBullet SummonBullet(int bulletId, double atk, LDCitiaoBuffMgr citiaoBuffMgr)
        {
            return GetBullet(bulletId,atk, citiaoBuffMgr);
        }
        private void CalcAtkParam(LDTrackBullet heroBullet,double atk,LDCitiaoBuffMgr citiaoBuffMgr, LDBaseWeapon heroWeapon)
        {
            double typeDamage = GetBulletXiDamage(heroBullet, citiaoBuffMgr);
            heroBullet.SetData(atk, typeDamage);
            heroBullet.AtkData.AddFinalZengShangParam(m_HeroPlayer.HeroData.GetFinalZengShangVal(heroWeapon));
            heroBullet.AtkData.AddRealDamage(m_HeroPlayer.HeroData.GetRealDamage());
            heroBullet.AtkData.AddSkillBase(m_HeroPlayer.HeroData.GetSkillBaseVal(heroWeapon));
        }
        public double GetBulletXiDamage(LDTrackBullet heroBullet, LDCitiaoBuffMgr citiaoBuffMgr)
        {
            int damageType = heroBullet.BulletItem.damageType;
            MatchConfigItem luaConfigItem;
            MatchConfig.Data.TryGet(damageType, out luaConfigItem, false);
            if (luaConfigItem != null)
            {
                double xiDamageInc = 0;
                if (citiaoBuffMgr != null)
                {
                    Dictionary<int, int> XiData = m_HeroPlayer.GetXiData();
                    foreach (KeyValuePair<int, int> items in XiData)
                    {
                        MatchConfigItem matchConfigItem;
                        MatchConfig.Data.TryGet(items.Key, out matchConfigItem, false);

                        if (matchConfigItem != null)
                        {
                            xiDamageInc += citiaoBuffMgr.GetBuffVal(matchConfigItem.addOne) * items.Value;
                        }
                    }
                }

                double typeDamageParam = m_HeroPlayer.BuffMgr.GetBuffVal(luaConfigItem.add) +
                    m_HeroPlayer.GetBulletBuffVal(heroBullet.BulletItem.id,luaConfigItem.add) +
                    m_HeroPlayer.HeroData.GetAddParamByXi(damageType) + xiDamageInc;
                return typeDamageParam ;
            }
            else
            {
                return 0;
            }
        }
        private void CalcBuffInfo(LDTrackBullet heroBullet, LDBaseWeapon heroWeapon)
        {
            //LDBuffMgr buffMgr = null;
            //if (heroWeapon != null)
            //{
            //    buffMgr = heroWeapon.BuffMgr;
            //}
            int maxPenetrationVal = m_HeroPlayer.BulletChangeData.CalcBulletPieceVal(heroBullet.BulletItem);
            heroBullet.SetCurPenetrationVal(maxPenetrationVal);


            int maxBounceVal = m_HeroPlayer.BulletChangeData.CalcBulletBounce(heroBullet.BulletItem) + 
               EZMath.IDFloatToInt(m_HeroPlayer.GetBulletBuffVal(heroBullet.BulletItem.id, LDBuffType.BulletBounce_602));
            heroBullet.SetBounceVal(maxBounceVal);


            heroBullet.AtkData.HittEffectId = m_HeroPlayer.BulletChangeData.CalcBulletHitEffect(heroBullet.BulletItem);
            heroBullet.AtkData.BulletEffectId = m_HeroPlayer.BulletChangeData.CalcBulletEffect(heroBullet.BulletItem);
        }
        public void OnDUpdate(float dt)
        {
            m_FiniteLinkList.OnDUpdate(dt);
        }
        public LDHeroBulletData SummonDelayBulletBaseOnForward(int bulletId, double atk, LDCitiaoBuffMgr citiaoBuffMgr, Vector3 pos, float offAngle, float offset)
        {
            LDHeroBulletData heroBulletData = SummonDelayBullet(bulletId,atk, citiaoBuffMgr,pos, offAngle,offset);
            heroBulletData.FirePoint = m_HeroPlayer.AtkFight.RotateNode;
            return heroBulletData;
        }
        public LDHeroBulletData SummonDelayBulletBaseOnMechaFireForward(int bulletId, double atk, LDCitiaoBuffMgr citiaoBuffMgr, Vector3 pos, float offAngle, float offset)
        {
            LDHeroBulletData heroBulletData = SummonDelayBullet(bulletId,atk, citiaoBuffMgr,pos, offAngle,offset);
            LDHeroWeapon heroWeapon = m_HeroPlayer.MechaMgr.GetHeroWeapon();
            if(heroWeapon != null)
            {
                heroBulletData.FirePoint = heroWeapon.WeapenNode.FirePoint;
            }
            else
            {
                heroBulletData.FirePoint = m_HeroPlayer.AtkFight.RotateNode;
            }
            return heroBulletData;
        }
        public LDHeroBulletData SummonDelayBullet(int bulletId, double atk, LDCitiaoBuffMgr citiaoBuffMgr,Vector3 pos, float offAngle, float offset)
        {
            if (bulletId > 0)
            {
                LDHeroBulletData bulletInfo = m_FiniteLinkList.GetDataInfo();
                bulletInfo.MainDeal = true;
                bulletInfo.BulletId = bulletId;
                bulletInfo.CitiaoBuffMgr = citiaoBuffMgr;
                bulletInfo.AtkVal = atk;
                bulletInfo.Position = pos;
                bulletInfo.OffsetAngleY = offAngle;
                bulletInfo.OffsetPosZ = offset;
                bulletInfo.Scale = 1;
                return bulletInfo;
            }
            else
            {
                return null;
            }
        }
        private void SummonDelayBulletImp(LDHeroBulletData bulletInfo)
        {
            if (!bulletInfo.MatchMax())
            {
                Global.gApp.gMsgDispatcher.Broadcast<LDHeroBulletData>(MsgIds.SummonHeroDelayBulletStart, bulletInfo);
                AddBullet(bulletInfo);
            }
            //Global.gApp.gMsgDispatcher.Broadcast<LDHeroBulletData>(MsgIds.SummonHeroDelayBulletEnd, bulletInfo);
        }
        private float GetBulletScale(BulletItem bulletItem, LDBaseWeapon heroWeapon = null)
        {
            float externVal = m_HeroPlayer.GetBulletBuffVal(bulletItem.id, LDBuffType.BulletScale_603);
            return m_HeroPlayer.BulletChangeData.CalcBulletScale(bulletItem, externVal);
        }
        private void AddBullet(LDHeroBulletData bulletInfo)
        {
            LDTrackBullet LDTrackBullet = SummonBullet(bulletInfo.BulletId, bulletInfo.AtkVal, bulletInfo.CitiaoBuffMgr);
            LDTrackBullet.AtkData.MainDeal = bulletInfo.MainDeal;
            LDTrackBullet.AtkData.MulCiTiaoParam(bulletInfo.DamageParam);
            LDTrackBullet.AtkData.BulletScale = LDTrackBullet.AtkData.BulletScale * bulletInfo.Scale;
            LDTrackBullet.transform.localScale = LDTrackBullet.transform.localScale * bulletInfo.Scale;
            LDTrackBullet.AtkData.CitiaoItem = bulletInfo.CiTiaoItem;
            LDTrackBullet.AtkData.Weapon = bulletInfo.HeroWeapon;
            LDTrackBullet.AtkData.SubWeapon = bulletInfo.SubWeapon;
            if (bulletInfo.ParentAdd > 0)
            {

                if (bulletInfo.ParentAdd == 1)
                {
                    LDTrackBullet.Init(m_HeroPlayer.AtkFight.RotateNode, bulletInfo.OffsetAngleY, bulletInfo.OffsetPosZ);
                    LDTrackBullet.transform.SetParent(m_HeroPlayer.transform, true);
                    LDTrackBullet.transform.localPosition = Vector3.zero;
                    LDTrackBullet.SyncPos(LDTrackBullet.transform.GetPoisition());
                }
                else if (bulletInfo.ParentAdd == 2)
                {
                    LDTrackBullet.Init(m_HeroPlayer.AtkFight.RotateNode, bulletInfo.OffsetAngleY, bulletInfo.OffsetPosZ);
                    LDTrackBullet.transform.SetParent(m_HeroPlayer.AtkFight.RotateNode, true);
                    LDTrackBullet.transform.localPosition = Vector3.zero;
                    LDTrackBullet.SyncPos(LDTrackBullet.transform.GetPoisition());
                }
                else if(bulletInfo.ParentAdd == 3)
                {
                    LDTrackBullet.Init(bulletInfo.FirePoint, bulletInfo.OffsetAngleY, bulletInfo.OffsetPosZ);
                    LDTrackBullet.transform.SetParent(bulletInfo.FirePoint.transform, true);
                }
            }
            else
            {
                LDTrackBullet.Init(bulletInfo.FirePoint, bulletInfo.OffsetAngleY, bulletInfo.OffsetPosZ);
                if (bulletInfo.FirePoint == null)
                {
                    LDTrackBullet.SyncPos(bulletInfo.Position);
                }
            }
            if (bulletInfo.StartForward == 1)
            {
                LDTrackBullet.transform.SetForward(m_HeroPlayer.AtkFight.RotateNode.forward);
            }
        }
    }
}