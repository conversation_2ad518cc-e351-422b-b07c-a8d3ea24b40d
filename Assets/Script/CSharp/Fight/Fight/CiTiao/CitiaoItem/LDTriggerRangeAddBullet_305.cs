using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDTriggerRangeAddBullet_305 : LDEventCitiao
    {
        private int m_TriRate;
        private double m_DamageParam;
        private int m_BulletId;
        private int m_BulletCount;
        private float m_RadiusMin;
        private float m_RadiusMax;
        private int m_TirTimes;
        private float m_TriDt;
        private int m_BulletDir;


        private float m_CurTime;
        private float m_CurTriTimes;
        private List<LDSceneObj> m_HitList = new List<LDSceneObj>();

        public override void Init(LDCiTiaoBaseGroup ciTiaoBaseGroup, LDCiTiaoItemData ciTiaoItemData)
        {
            base.Init(ciTiaoBaseGroup, ciTiaoItemData);
            m_TriRate = EZMath.IDDoubleToInt(GetCitiaoParam()[1]);
            m_DamageParam = GetCitiaoParam()[2];
            m_BulletId = EZMath.IDDoubleToInt(GetCitiaoParam()[3]);
            m_BulletCount = EZMath.IDDoubleToInt(GetCitiaoParam()[4]);
            m_RadiusMin = GetCitiaoParam()[5];
            m_RadiusMax = GetCitiaoParam()[6] * (1 + (float)Global.gApp.CurFightScene.gPassHandler.GetExternAtkInc());
            m_TirTimes = EZMath.IDDoubleToInt(GetCitiaoParam()[7]);
            m_TriDt = GetCitiaoParam()[8];
            m_BulletDir = EZMath.IDDoubleToInt(GetCitiaoParam()[9]);

            m_CurTriTimes = GetTriTimes();
        }

        public override void TriggerSucess(int triggerType)
        {
            if (m_Restore)
            {
                m_CurTriTimes = GetTriTimes();
                return;
            } 
            
            if (!RandomMatch(m_TriRate)) return;
            base.TriggerSucess(triggerType);

            m_CurTime = m_TriDt;
            m_CurTriTimes = 0;
        }

        public override void OnDUpdate(float dt)
        {
            base.OnDUpdate(dt);

            if (m_CurTriTimes < GetTriTimes())
            {
                m_CurTime += dt;
                if (LDFightTools.GreaterOrEqualThenZero(m_CurTime, m_TriDt))
                {
                    m_CurTriTimes++;
                    m_CurTime -= m_TriDt;
                    TryAddBullet();
                }
            }
        }

        private void TryAddBullet()
        {
            LDHeroPlayer heroPlayer = CiTiaoMgr.HeroPlayer;

            float minDisSqr = m_RadiusMin * m_RadiusMin;
            float maxDisSqr = m_RadiusMax * m_RadiusMax;

            List<LDSceneObj> hitTargets = Global.gApp.CurFightScene.gPlayerMgr.SearchUtils.FindRangeObj(m_RadiusMax, heroPlayer.transform.GetPoisition(), LDFightConstVal.AIMask, heroPlayer.TeamId);
            int hitCount = hitTargets.Count;
            m_HitList.Clear();
            for (int i = 0; i < hitCount; i++)
            {
                LDSceneObj targetObj = hitTargets[i];
                
                if (!targetObj.Live)
                {
                    continue;
                }

                float disSqr = (targetObj.GetPos() - heroPlayer.GetPos()).sqrMagnitude;
                if (disSqr < minDisSqr || disSqr > maxDisSqr)
                {
                    continue;
                }
                    
                m_HitList.Add(targetObj);
            }
            
            for (int i = 0; i < GetBulletNum(); i++)
            {
                if (m_HitList.Count > 0)
                {
                    int randomIdx = RandomUtil.NextInt(0, m_HitList.Count);
                    LDSceneObj obj = m_HitList[randomIdx];
                    AddBullet(obj.transform.GetPoisition());
                    m_HitList.RemoveAt(randomIdx);
                }
                else
                {
                    Vector3 randomPos = RandomUtil.GetRandomPosXZ(heroPlayer.transform.GetPoisition(), m_RadiusMin, m_RadiusMax);
                    AddBullet(randomPos);
                }
            }
        }

        private void AddBullet(Vector3 pos)
        {
            LDHeroPlayer heroPlayer = CiTiaoMgr.HeroPlayer;
            double atk = heroPlayer.MechaMgr.GetAtk();
            double damageParam = GetDamageParam(m_DamageParam);

            LDHeroBulletData bulletData = heroPlayer.BulletEmitter.SummonDelayBullet(m_BulletId, atk, CitiaoBuffMgr, pos, 0f, 0f);
            bulletData.StartForward = m_BulletDir;
            bulletData.DamageParam = damageParam;
            bulletData.CiTiaoItem = this;
        }

        private int GetBulletNum()
        {
            int count = (m_BulletCount + CitiaoBuffMgr.GetBuffValInt(LDBuffType.CiitaoCurve_151)) * (1 + CitiaoBuffMgr.GetBuffValInt(LDBuffType.CiitaoCurveTimes_152));
            return count;
        }

        private int GetTriTimes()
        {
            int count = (m_TirTimes + CitiaoBuffMgr.GetBuffValInt(LDBuffType.SubWpnFireCount_103)) * (1 + CitiaoBuffMgr.GetBuffValInt(LDBuffType.SubWpnMaxClip_104));
            return count;
        }


        public override void TryCacheEffect()
        {
            CacheBulletEffect(EZMath.IDDoubleToInt(GetCitiaoParam()[3]));
        }
    }
}