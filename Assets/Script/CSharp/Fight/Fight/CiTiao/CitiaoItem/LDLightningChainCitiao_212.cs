using System.Collections.Generic;
using LD.Skip;
using UnityEngine;

namespace LD
{
    public class LDLightningChainCitiao_212 : LDEventCitiao
    {
        private int m_TriggerRata;
        private double m_DamageParam;
        private int m_BulletId = 1;
        private float m_Radius = 1;
        private int m_MaxCount;
        private int m_PointEffectId;

        private List<LDSceneObj> m_TargetObjList = new List<LDSceneObj>();
        private List<LDSceneObj> m_TempObjList = new List<LDSceneObj>();


        public override void Init(LDCiTiaoBaseGroup ciTiaoBaseGroup, LDCiTiaoItemData ciTiaoItemData)
        {
            base.Init(ciTiaoBaseGroup, ciTiaoItemData);

            m_TriggerRata = EZMath.IDDoubleToInt(GetCitiaoParam()[1]);
            m_DamageParam = GetCitiaoParam()[2];
            m_BulletId = EZMath.IDDoubleToInt(GetCitiaoParam()[3]);
            m_Radius = GetCitiaoParam()[4];
            m_MaxCount = EZMath.IDDoubleToInt(GetCitiaoParam()[5]);
            m_PointEffectId = EZMath.IDDoubleToInt(GetCitiaoParam()[6]);
        }

        public override void TriggerSucessWithObj(LDHeroPlayer heroPlayer, LDSceneObj AIMonster, LDAtkBullet atkBullet)
        {
            TryTriggerSuccess(AIMonster);
        }

        public override void TriggerSucessWithObj(LDHeroPlayer heroPlayer, LDSceneObj AIMonster)
        {
            TryTriggerSuccess(AIMonster);
        }

        private void TryTriggerSuccess(LDSceneObj AIMonster)
        {
            if (m_Restore) return;
            if(AIMonster == null) return;

            if (!RandomMatch(m_TriggerRata)) return;

            m_TargetObjList.Clear();
            m_TargetObjList.Add(AIMonster);
            TryFindTarget(AIMonster);
            if (m_TargetObjList.Count < 2) return;
            
            SummonBullets();

            base.TriggerSucess(-1);
        }

        private void TryFindTarget(LDSceneObj aiMonster)
        {
            if (m_TargetObjList.Count >= GetMaxCount()) return;
            
            m_TempObjList.Clear();
            Vector3 findPos = aiMonster.GetPos();
            int teamId = CiTiaoMgr.HeroPlayer.TeamId;
            List<LDSceneObj> colliderData = Global.gApp.CurFightScene.gPlayerMgr.SearchUtils.FindRangeObj(m_Radius, findPos, LDFightConstVal.AIMask, teamId);
            
            if (colliderData.Count <= 0) return;
            
            foreach (LDSceneObj obj in colliderData)
            {
                if (!m_TargetObjList.Contains(obj))
                {
                    m_TempObjList.Add(obj);
                }
            }
            
            if(m_TempObjList.Count <= 0) return;
            
            int index = RandomUtil.NextInt(0, m_TempObjList.Count);
            LDSceneObj target = m_TempObjList[index];
            m_TargetObjList.Add(target);
            TryFindTarget(target);
        }

        private void SummonBullets()
        {
            LDHeroPlayer heroPlayer = CiTiaoMgr.HeroPlayer;
            double atk = heroPlayer.MechaMgr.GetAtk();
            double damageParam = GetDamageParam(m_DamageParam);
            float posY = m_TargetObjList[0].GetPos().y;
            for (int i = 0; i < m_TargetObjList.Count; i++)
            {
                if (i == m_TargetObjList.Count - 1) break;
                
                LDTrackBullet bullet = heroPlayer.BulletEmitter.SummonBullet(m_BulletId, atk, CitiaoBuffMgr);
                bullet.Init(m_TargetObjList[i].transform, 0, 0);
                bullet.AtkData.MulCiTiaoParam(damageParam);
                bullet.AtkData.CitiaoItem = this;
                if (bullet.BulletMove is LDRayLesserMoveMode rayLesserMoveMode)
                {
                    rayLesserMoveMode.SetLockObj(m_TargetObjList[i], m_TargetObjList[i+1], posY);
                    rayLesserMoveMode.SetPointEffectId(GetPointEffectId());
                }
            }
        }

        private int GetMaxCount()
        {
            return m_MaxCount + CitiaoBuffMgr.GetBuffValInt(LDBuffType.LightningChainCount_166);
        }

        private int GetPointEffectId()
        {
            int buffEffectId = CitiaoBuffMgr.GetBuffValInt(LDBuffType.LightningChainPointEffect_167);
            if (buffEffectId > 0)
            {
                return buffEffectId;
            }

            return m_PointEffectId;
        }

        public override void TryCacheEffect()
        {
            base.TryCacheEffect();
            m_BulletId = EZMath.IDDoubleToInt(GetCitiaoParam()[3]);
            CacheBulletEffect(m_BulletId);
        }
    }
}