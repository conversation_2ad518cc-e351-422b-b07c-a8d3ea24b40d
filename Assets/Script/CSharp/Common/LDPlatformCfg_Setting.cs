using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDRenderSetting 
    {      
        public static float MaxRenderScale = 1;

        public static float GuidFightRenderScale = 0.85f;
        //Effect Limit
        public static int EffectLimit = 15;
        
        // 延迟特效最多数量比如被击特效，出生特效。c超过这个数。会拿到第一个。放到对应位置播放。
        public int DelayEffectLimit = 15;
        // 渲染尺度，用于控制整体渲染的大小比例 
        public float FightRenderScale = 0.7f;
        // 粒子尺度，用于控制粒子特效的数量缩放
        // 最大粒子数量，用于控制单个特效的粒子数量上限
        public int MaxParticleCount = 800;
        // 最大特效数量，用于控制单个场景中允许存在的特效数量上限
        public int MaxEffectCount = 200;
        // 目标帧率，用于控制游戏的帧率上限
        public int TargetFrame = 60;
        // 是否渲染阴影，控制场景中阴影的启用
        public bool RenderShadow = true;
        // 是否启用后期处理，控制如HDR、模糊等效果
        public bool PostProcessing = true;
        // 特效创建的时间间隔，控制特效生成的频率
        public int EffectDtCreate = 10;
        // 特效创建数量
        public int EffectMaxCreateCount = 64;
        
        // AI最大寻路数量，限制同时存在的AI寻路
        public int AIMaxAgentCount = 20;
        // 最大伤害提示数量，限制同时显示的伤害数字提示数量
        public int MaxDamageTips = 50;
        // AI创建的时间间隔，控制AI实例生成的频率
        public int AIDtCreate = 8;
        // 最大AI数量，限制同时存在的AI实例总数
        public int MaxAICount = 600; 
        // AI更新计数，控制AI更新的频率
        public int AIUpdateUpdateCount = 25;
        // AI旋转更新计数，控制AI旋转动作的更新频率
        public int AIRotateUpdateCount = 150;
        // AI Buff更新计数，控制AI Buff效果的更新频率
        public int AIBuffUpdateCount = 100;
        // 子弹更新频率，控制子弹物理模拟的更新频率
        public int BodyBulletUpdate = 5;

    }
    
    public partial class LDPlatformCfg
    {
        private static int m_FightQuality = -1;
        public static int FightQuality
        {
            get
            {
                if (m_FightQuality < 0)
                {
                    m_FightQuality = Global.gApp.gSystemMgr.gLocalDataMgr.GetIntVal(false, LDLocalDataKeys.Fight_Quality, 2); // 默认高
                }
                return m_FightQuality;
            }
            
            set
            {
                m_FightQuality = value;
                Global.gApp.gSystemMgr.gLocalDataMgr.SetIntVal(false, LDLocalDataKeys.Fight_Quality, value);
                LDSDKEvent.SwithRenderSetting(value);
            }
        }
        
        public static LDRenderSetting GetRenderSetting(bool useKcp)
        { 
            LDRenderSetting renderSetting = new LDRenderSetting();
            Global.LogEditor($"性能档位：{FightQuality}");
            if (FightQuality == 0)
            {
                renderSetting = GetLowRenderSetting();
            }
            else if (FightQuality == 1)
            {
                renderSetting = GetMiddleRenderSetting();
            }
            else
            {
                renderSetting = GetHighRenderSetting();
            }
            
            // kcp 逻辑使用中间档位 组队副本的 参数
            if (useKcp)
            {
                renderSetting.AIMaxAgentCount = 15;
                renderSetting.AIDtCreate = 8;
                renderSetting.MaxAICount = 400;
                renderSetting.AIUpdateUpdateCount = 20;
                renderSetting.AIRotateUpdateCount = 120;
                renderSetting.AIBuffUpdateCount = 100;
                renderSetting.BodyBulletUpdate = 3; 
            }
            return renderSetting;
        }
        public static LDRenderSetting GetHighRenderSetting()
        {
            LDRenderSetting renderSetting = new LDRenderSetting();
            renderSetting.FightRenderScale = 0.7f;//渲染尺度
            renderSetting.MaxParticleCount = 16000;//最大粒子数，非常有用
            renderSetting.MaxEffectCount = 4000;//最大特效数，非常有用
            renderSetting.EffectDtCreate = 10;//特效时间间隔
            renderSetting.EffectMaxCreateCount = 64;//特效创建数量
            renderSetting.MaxDamageTips = 50;//伤害数字数量，有用
            renderSetting.AIMaxAgentCount = 20;//AI最大寻路数量
            renderSetting.AIDtCreate = 8;//AI创建时间间隔，有用
            renderSetting.MaxAICount = 600;//最大怪物数量，非常有用，但不要减太多
            renderSetting.AIUpdateUpdateCount = 25;//AI更新频率
            renderSetting.AIRotateUpdateCount = 150;//AI旋转更新频率
            renderSetting.AIBuffUpdateCount = 100;//BUFF更新频率
            renderSetting.BodyBulletUpdate = 5;//子弹更新频率
            renderSetting.DelayEffectLimit = 30;//被击特效同时存在数量，非常有用，但不要减太多
            renderSetting.RenderShadow = true;//阴影渲染
            renderSetting.PostProcessing = true;//后处理
            return renderSetting;
        }
        public static LDRenderSetting GetMiddleRenderSetting()
        {
            LDRenderSetting renderSetting = new LDRenderSetting();
            renderSetting.FightRenderScale = 0.65f;
            renderSetting.MaxParticleCount = 600;//最大粒子数，非常有用
            renderSetting.MaxEffectCount = 160;//最大特效数，非常有用
            renderSetting.EffectDtCreate = 8;
            renderSetting.EffectMaxCreateCount = 50;
            renderSetting.MaxDamageTips = 40;//伤害数字数量，有用
            renderSetting.AIMaxAgentCount = 15;
            renderSetting.AIDtCreate = 6;//AI创建时间间隔，有用
            renderSetting.MaxAICount = 500;//最大怪物数量，非常有用，但不要减太多
            renderSetting.AIUpdateUpdateCount = 25;
            renderSetting.AIRotateUpdateCount = 150;
            renderSetting.AIBuffUpdateCount = 90;
            renderSetting.BodyBulletUpdate = 5;
            renderSetting.DelayEffectLimit = 20;//被击特效同时存在数量，非常有用，但不要减太多
            renderSetting.RenderShadow = true;
            renderSetting.PostProcessing = false;
            return renderSetting;
        }
        public static LDRenderSetting GetLowRenderSetting()
        {
            LDRenderSetting renderSetting = new LDRenderSetting();
            renderSetting.FightRenderScale = 0.6f;
            renderSetting.MaxParticleCount = 300;//最大粒子数，非常有用
            renderSetting.MaxEffectCount = 120;//最大特效数，非常有用
            renderSetting.EffectDtCreate = 5;
            renderSetting.EffectMaxCreateCount = 40;
            renderSetting.MaxDamageTips = 30;//伤害数字数量，有用
            renderSetting.AIMaxAgentCount = 10;
            renderSetting.AIDtCreate = 5;//AI创建时间间隔，有用
            renderSetting.MaxAICount = 400;//最大怪物数量，非常有用，但不要减太多
            renderSetting.AIUpdateUpdateCount = 25;
            renderSetting.AIRotateUpdateCount = 150;
            renderSetting.AIBuffUpdateCount = 80;
            renderSetting.BodyBulletUpdate = 5;
            renderSetting.DelayEffectLimit = 10;//被击特效同时存在数量，非常有用，但不要减太多
            renderSetting.RenderShadow = false;
            renderSetting.PostProcessing = false;
            return renderSetting;
        }
    }    
}