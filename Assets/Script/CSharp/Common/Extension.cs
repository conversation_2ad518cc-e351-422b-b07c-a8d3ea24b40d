using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using RTLTMPro;
using TMPro;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;

namespace LD
{
    public static class Extension 
    {
        private static Material GrayMat;
        private static Material UIGaussian;
        public static Sprite Enempty;
        public static bool RTL = false;
        public static void Extend()
        {
            JsonExtend.Extend();
            GrayMat = Resources.Load<Material>("Material/GrayMat");
            UIGaussian = Resources.Load<Material>("Material/UIGaussian");
            Enempty = Resources.Load<Sprite>("Enempty");
        }
        public static void SetForward(this Transform tsf, Vector3 vec3)
        {
            if (tsf == null) return;
            tsf.forward = vec3;
            //tsf.forward = LDFightTools.GetV3IntF(vec3.normalized);
        }
        public static void SetForward(this Transform tsf, Transform sdtTsf)
        {
            if (tsf == null) return;
            tsf.forward = sdtTsf.forward;
            //tsf.forward = LDFightTools.GetV3IntF(sdtTsf.forward);
        }

        public static void SetUp(this Transform tsf, Vector3 vec3)
        {
            if (tsf == null) return;
            tsf.up = (vec3);
            //tsf.up = LDFightTools.GetV3IntF(vec3);
        }
        public static void SetRight(this Transform tsf, Vector3 vec3)
        {
            if (tsf == null) return;
            tsf.right = (vec3);
            //tsf.right = LDFightTools.GetV3IntF(vec3);
        }
        public static void SetLocalEulerAngles(this Transform tsf, Vector3 vec3)
        {
            tsf.localEulerAngles = vec3;
            //tsf.localEulerAngles = LDFightTools.GetV3IntF(vec3);
        }
        public static void SetLocalPoisition(this Transform tsf, Vector3 vec3)
        {
            tsf.localPosition = vec3;
            //tsf.localPosition = LDFightTools.GetV3IntF(vec3);
        }
        public static void SetPoisition(this Transform tsf, Vector3 vec3)
        {
            tsf.position = (vec3);
            //tsf.position = LDFightTools.GetV3IntF(vec3);
        }
        public static Vector3 GetPoisition(this Transform tsf)
        {
            return tsf.position;
            return LDFightTools.GetV3IntF(tsf.position);
        }
        public static Vector3 GetLocalPoisition(this Transform tsf)
        {
            return tsf.localPosition;
            return LDFightTools.GetV3IntF(tsf.localPosition);
        }


        public static Vector3 GetForward(this Transform tsf)
        {
            return tsf.forward;
            return LDFightTools.GetV3IntF(tsf.forward);
        }
        public static Vector3 GetRight(this Transform tsf)
        {
            return tsf.right;
            return LDFightTools.GetV3IntF(tsf.right);
        }

        public static Vector3 GetUp(this Transform tsf)
        {
            return tsf.up;
            return LDFightTools.GetV3IntF(tsf.up);
        }

        public static void SetLocalScale(this Transform tsf, Vector3 vec3)
        {
            tsf.localScale = vec3;
            return;
            tsf.localScale = LDFightTools.GetV3IntF(vec3);
        }
        public static Vector3 GetLocalScale(this Transform tsf)
        {
            return tsf.localScale;
            return LDFightTools.GetV3IntF(tsf.localScale);
        }
        public static void TranslateForwardM(this Transform tsf, float val)
        {
            Vector3 pos = tsf.transform.GetLocalPoisition();
            pos += tsf.GetForward() * LDFightTools.GetIntF(val);
            tsf.transform.localPosition = pos;
            LDFightTools.UnifiedTransformPos(tsf);
        }
        public static void TranslateWorld(this Transform tsf,Vector3 Vec)
        {
            Vector3 pos = tsf.transform.localPosition;
            pos += LDFightTools.GetV3IntF(Vec);
            tsf.transform.localPosition = pos;
            LDFightTools.UnifiedTransformPos(tsf);
        }
        public static void RotateUpM(this Transform tsf,Transform tsfTarget, float val)
        {
            if (tsf == null) return;
            Vector3 axis = tsfTarget.GetForward();
            Quaternion rot = Quaternion.Euler(0, val, 0);//在Y轴上旋转角度
            Vector3 newVec = rot * axis; //按rot旋转v0，得到目标v1
            tsf.SetForward(newVec);
        }


        public static void SetTips(this Text text, int tipsId, params object[] args)
        {
            if (text == null)
            {
                Debug.LogError("Text isNull ");
                return;
            }
            if (tipsId > 0)
            {
                text.SetText(UiTools.Localize(tipsId,text, args));
            }
            else
            {
                text.text = text.name + "    改tipsId";
            }
        }

        public static void SetTips(this TextMeshProUGUI text, int tipsId, params object[] args)
        {
            if (text == null || tipsId <= 0) return;
            text.SetProText(UiTools.Localize(tipsId,text, args));
        }

        public static void SetText(this Text text, int val)
        {
            text.text = val.ToString();
        }

        public static void SetText(this Text text, float val)
        {
            text.text = val.ToString();
        }
        public static void SetText(this Text text, string val)
        {
            if (RTL)
            {
                val = RTLTextTools.GetRTLText(text, val);
            }

            text.text = val;
        }

        public static void SetText(this TMPro.TextMeshProUGUI text, int val)
        {
            text.text = val.ToString();
        }
        public static void SetText(this Text text, long val)
        {
            text.text = val.ToString();
        }
        public static void SetText(this TMPro.TextMeshProUGUI text, float val)
        {
            text.text = val.ToString();
        }
        public static void SetProText(this TMPro.TextMeshProUGUI text, string val)
        {
            if (RTL)
            {
                val = RTLTextTools.GetRTLText(text, val);
            }
            text.text = val;
        }

        public static void SetQuaColor(this Text text, int qua)
        {
            Color color = LDCommonColorTools.GetColor(LDUIResTools.GetQualityColor(qua));
            text.color = color;
        }

        public static void SetTextColor(this Text text, Color color)
        {
            text.color = color;
        }
        public static void SetTextColor(this Text text, string colorStr)
        {
            Color color;
            if (ColorUtility.TryParseHtmlString(colorStr, out color))
            {
                text.color = color;
            }
        }
        public static void SetQuaColor(this TMPro.TextMeshProUGUI text, int qua)
        {
            Color[] colors = LDCommonColorTools.GetColorArray(LDUIResTools.GetQualityTMPColors(qua));

            Color color0 = colors.Length > 0 ? colors[0] : Color.white;
            Color color1 = colors.Length > 1 ? colors[1] : Color.white;
            Color color2 = colors.Length > 2 ? colors[2] : Color.white;
            Color color3 = colors.Length > 3 ? colors[3] : Color.white;

            text.enableVertexGradient = true;
            text.colorGradient = new VertexGradient(color0, color1, color2, color3);
        }



        const string CostRichText1 = "<color=" + "#EF320C" + ">{0}</color>";
        public static void SetCostText(this Text text, long ownedNum, long num)
        {
            string numStr = UiTools.FormateMoney(num);

            if (ownedNum < num)
            {
                text.text = string.Format(CostRichText1, numStr);
            }
            else
            {
                text.text = numStr;
            }
        }

        const string CostRichText3 = "<color=" + "#EF320C" + ">{0}</color>";
        public static void SetCostTextRich(this Text text,string str, long ownedNum, long num)
        {

            if (ownedNum < num)
            {
                text.text = string.Format(CostRichText3, str);
            }
            else
            {
                text.text = $"{str}";
            }

        }
        const string CostRichText2 = "<color=" + "#EF320C" + ">{0}</color>/{1}";

        public static void SetCostTextRich(this Text text, long ownedNum, long num)
        {
            string ownedNumStr = UiTools.FormateMoney(ownedNum);
            string numStr = UiTools.FormateMoney(num);

            if (ownedNum < num)
            {
                text.text = string.Format(CostRichText2, ownedNumStr, numStr);
            }
            else
            {
                text.text = $"{ownedNumStr}/{numStr}";
            }
        }
        public static void AddListener(this RectTransform_Button_Animator_Container btnContainer, UnityAction action)
        {
            btnContainer.button.AddListener(action);
        }
        public static void AddListener(this RectTransform_Button_Container btnContainer, UnityAction action)
        {
            btnContainer.button.AddListener(action);
        }
        public static void AddListenerNoAudio(this RectTransform_Button_Container btnContainer, UnityAction action)
        {
            btnContainer.button.AddListenerNoAudio(action);
        }

        public static void AddListener(this RectTransform_Button_Image_Container btnContainer, UnityAction action)
        {
            btnContainer.button.AddListener(action);
        }
        public static void AddListenerNoAudio(this RectTransform_Button_Image_Container btnContainer, UnityAction action)
        {
            btnContainer.button.AddListenerNoAudio(action);
        }

        public static void AddListener(this Button btn, UnityAction action)
        {
            btn.onClick.RemoveAllListeners();
            btn.onClick.AddListener(action);
            btn.onClick.AddListener(Global.gApp.gAudioSource.NormalClick);
        }

        /// <summary>
        /// 音效自己控制
        /// </summary>
        /// <param name="btn"></param>
        /// <param name="action"></param>
        public static void AddListenerNoAudio(this Button btn, UnityAction action)
        {
            btn.onClick.RemoveAllListeners();
            btn.onClick.AddListener(action);
        }

        public static void SetSprite(this Image image, Sprite sprite)
        {
            image.sprite = sprite;
            image.enabled = sprite != null;
        }

        public static void SetFillAmount(this Image image, double val, double max)
        {
            image.fillAmount = (float)(Math.Abs(max) > double.Epsilon ? val / max : 0);
        }

        public static void SetGray(this Image image, bool isGray = true)
        {
           image.material = isGray ? GrayMat : null;
        }
        public static void SetGaussian(this RawImage image, bool isGray = true)
        {
           image.material = isGray ? UIGaussian : null;
        }

        public static T TryAddComponent<T>(this GameObject go) where T : Component
        {
            T comp = go.GetComponent<T>();
            if (comp == null)
            {
                comp = go.AddComponent<T>();
            }

            return comp;
        }

        public static T TryAddComponent<T>(this MonoBehaviour go) where T : Component
        {
            T comp = go.GetComponent<T>();
            if (comp == null && go.gameObject != null)
            {
                comp = go.gameObject.AddComponent<T>();
            }

            return comp;
        }
        public static void SetLayer(this GameObject t, int layer)
        {
            SetLayer(t.transform, layer);
        }

        private static void SetLayer(this Transform t, int layer)
        {
            t.gameObject.layer = layer;
            foreach (Transform child in t)
            {
                SetLayer(child, layer);
            }
        }

        public static void AddListener(this Toggle toggle, UnityAction<bool> action)
        {
            toggle.onValueChanged.RemoveAllListeners();
            toggle.onValueChanged.AddListener(action);
        }
        public static void AddListener(this Dropdown dropdown, UnityAction<int> action)
        {
            dropdown.onValueChanged.RemoveAllListeners();
            dropdown.onValueChanged.AddListener(action);
        }
        public static void AddListener(this InputField inputField, UnityAction<string> action)
        {
            inputField.onValueChanged.RemoveAllListeners();
            inputField.onValueChanged.AddListener(action);
        }
        public static void AddListener(this Slider slider, UnityAction<float> action)
        {
            slider.onValueChanged.RemoveAllListeners();
            slider.onValueChanged.AddListener(action);
        }

        public static void VerticalScrollTo(this ScrollRect scrollRect, int idx, bool centerAlign = true)
        {
            Transform child = scrollRect.content.GetChild(idx);
            scrollRect.VerticalScrollTo(child.rectTransform());
        }

        public static void VerticalScrollTo(this ScrollRect scrollRect, RectTransform child, bool centerAlign = true)
        {
            Canvas.ForceUpdateCanvases();
            RectTransform content = scrollRect.content;
            RectTransform viewport = scrollRect.viewport;
            if (content.anchorMin.y != content.anchorMax.y)
            {
                Debug.LogWarning("垂直滚动需要Content使用相同的Y锚点值");
                return;
            }

            Vector3[] viewportCorners = new Vector3[4];
            viewport.GetWorldCorners(viewportCorners);
            Vector3[] childCorners = new Vector3[4];
            child.GetWorldCorners(childCorners);
            Vector3 childCenter = (childCorners[0] + childCorners[2]) / 2f;
            Vector3 childTop = new Vector3(childCenter.x, childCorners[1].y, childCenter.z);
            Vector3 viewportCenter = viewport.InverseTransformPoint(childCenter);
            Vector3 viewportTop = viewport.InverseTransformPoint(childTop);
            float scrollOffset = 0f;
            if (centerAlign)
            {
                scrollOffset = viewportCenter.y;
            }
            else
            {
                scrollOffset = viewportTop.y;
            }
            float contentHeight = content.rect.height;
            float viewportHeight = viewport.rect.height;
            float scrollRange = Mathf.Max(0, contentHeight - viewportHeight);

            if (Mathf.Approximately(scrollRange, 0f)) return;

            float normalizedPos = 1f + (scrollOffset + scrollRange/2f) / scrollRange;
            scrollRect.verticalNormalizedPosition = Mathf.Clamp01(normalizedPos);
        }
    }
}