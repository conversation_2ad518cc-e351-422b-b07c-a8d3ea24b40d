//-----------------------------------------------------------------------------
/// <summary>
/// This is an Open Source File Created by: <PERSON>. Twitter: @konash
/// This File allow the users to use arabic text in XNA and Unity platform.
/// It flips the characters and replace them with the appropriate ones to connect the letters in the correct way.
/// 
/// The project is available on GitHub here: https://github.com/Konash/arabic-support-unity
/// Unity Asset Store link: https://www.assetstore.unity3d.com/en/#!/content/2674
/// Please help in improving the plugin. 
/// 
/// I would love to see the work you use this plugin for. Send me a copy at: abdullah.konash[at]gmail[dot]com
/// </summary>
/// 
/// <license>
/// MIT License
/// 
/// Copyright(c) 2018
/// <PERSON>
/// 
/// Permission is hereby granted, free of charge, to any person obtaining a copy
/// of this software and associated documentation files (the "Software"), to deal
/// /// in the Software without restriction, including without limitation the rights
/// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
/// copies of the Software, and to permit persons to whom the Software is
/// furnished to do so, subject to the following conditions:
/// 
/// The above copyright notice and this permission notice shall be included in all
/// copies or substantial portions of the Software.
/// 
/// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
/// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
/// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
/// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
/// SOFTWARE.
/// </license>

//-----------------------------------------------------------------------------


#region Using Statements
using System;
using System.Collections.Generic;
using System.Text;
using UnityEngine;
using System.Text.RegularExpressions;

#endregion

// 0x200F  强制右向左符号
// 0x200B  强制左向右符号

namespace ArabicSupport
{
	
	public class ArabicFixer
	{	
		internal static StringBuilder s_sbLTRStr = new StringBuilder();
		internal static StringBuilder s_sbArabStr = new StringBuilder();
		public static bool ContainsRTL = false;
		/// <summary>
		/// Fix the specified string.
		/// </summary>
		/// <param name='str'>
		/// String to be fixed.
		/// </param>
		public static string Fix(string str)
		{
            // Debug.Log($"before Fix:{str}");
            str = Fix(str, false, false);
            // Debug.Log($"after Fix:{str}");
			return str;
		}
		
		public static string Fix(string str, bool rtl)
		{
			ContainsRTL = false;
			if(string.IsNullOrEmpty(str))
			{
				return str;
			}
			if(rtl)
			{
				return Fix(str);
			}
			else
			{
				for(int i=0; i<str.Length; i++)
				{
					char ch = str[i];
					if(ArabicFixerTool.IsRTL(ch))
					{
						s_sbArabStr.Append(ch);
					}
					else
					{
						s_sbLTRStr.Append(Fix(s_sbArabStr.ToString()));
						s_sbLTRStr.Append(ch);
						s_sbArabStr.Clear();
					}
				}
				if(s_sbArabStr.Length>0)
				{
					s_sbLTRStr.Append(Fix(s_sbArabStr.ToString()));
					s_sbArabStr.Clear();
				}
				string result = s_sbLTRStr.ToString();
				s_sbLTRStr.Clear();
				
				return result;
			}
		}
		
		/// <summary>
		/// Fix the specified string with customization options.
		/// </summary>
		/// <param name='str'>
		/// String to be fixed.
		/// </param>
		/// <param name='showTashkeel'>
		/// Show tashkeel.
		/// </param>
		/// <param name='useHinduNumbers'>
		/// Use hindu numbers.
		/// </param>
		public static string Fix(string str, bool showTashkeel, bool useHinduNumbers)
		{
			ContainsRTL = false;
			if(string.IsNullOrEmpty(str))
            {
                return str;
            }
			ArabicFixerTool.showTashkeel = showTashkeel;
			ArabicFixerTool.useHinduNumbers =useHinduNumbers;
			
			if(str.Contains("\n"))
				str = str.Replace("\n", Environment.NewLine);
			
			if(str.Contains(Environment.NewLine))
			{
				string[] stringSeparators = new string[] {Environment.NewLine};
				string[] strSplit = str.Split(stringSeparators, StringSplitOptions.None);
				
				if(strSplit.Length == 0)
				{
					string strRst = ArabicFixerTool.FixLine(str);
					ContainsRTL = ArabicFixerTool.ContainsRTL;
					return strRst;
				}
				else if(strSplit.Length == 1)
				{
					string strRst = ArabicFixerTool.FixLine(str);
					ContainsRTL = ArabicFixerTool.ContainsRTL;
					return strRst;
				}
				else
				{
					string outputString = ArabicFixerTool.FixLine(strSplit[0]);
					ContainsRTL = ContainsRTL || ArabicFixerTool.ContainsRTL;
					int iteration = 1;
					if(strSplit.Length > 1)
					{
						while(iteration < strSplit.Length)
						{
							outputString += Environment.NewLine + ArabicFixerTool.FixLine(strSplit[iteration]);
							iteration++;
						}
					}				
					return outputString;
				}	
			}
			else
			{
				string strRst = ArabicFixerTool.FixLine(str);
				ContainsRTL = ArabicFixerTool.ContainsRTL;
				return strRst;
			}
			
		}
 
        public static string Fix(string str, bool showTashkeel, bool combineTashkeel, bool useHinduNumbers)
        {
            ArabicFixerTool.combineTashkeel = combineTashkeel;
            return Fix(str, showTashkeel, useHinduNumbers);
        }


    }
	
}

/// <summary>
/// Arabic Contextual forms General - Unicode
/// </summary>
internal enum IsolatedArabicLetters
{
	Hamza = 0xFE80,
	Alef = 0xFE8D,
	AlefHamza = 0xFE83,
	WawHamza = 0xFE85,
	AlefMaksoor = 0xFE87,
	AlefMaksora = 0xFBFC,
	HamzaNabera = 0xFE89,
	Ba = 0xFE8F,
	Ta = 0xFE95,
	Tha2 = 0xFE99,
	Jeem = 0xFE9D,
	H7aa = 0xFEA1,
	Khaa2 = 0xFEA5,
	Dal = 0xFEA9,
	Thal = 0xFEAB,
	Ra2 = 0xFEAD,
	Zeen = 0xFEAF,
	Seen = 0xFEB1,
	Sheen = 0xFEB5,
	S9a = 0xFEB9,
	Dha = 0xFEBD,
	T6a = 0xFEC1,
	T6ha = 0xFEC5,
	Ain = 0xFEC9,
	Gain = 0xFECD,
	Fa = 0xFED1,
	Gaf = 0xFED5,
	Kaf = 0xFED9,
	Lam = 0xFEDD,
	Meem = 0xFEE1,
	Noon = 0xFEE5,
	Ha = 0xFEE9,
	Waw = 0xFEED,
	Ya = 0xFEF1,
	AlefMad = 0xFE81,
	TaMarboota = 0xFE93,
	PersianPe = 0xFB56,  	// Persian Letters;
	PersianChe = 0xFB7A,
	PersianZe = 0xFB8A,
	PersianGaf = 0xFB92,
	PersianGaf2 = 0xFB8E,
	PersianYeh = 0xFBFC,
	
}

/// <summary>
/// Arabic Contextual forms - Isolated
/// </summary>
internal enum GeneralArabicLetters
{
	Hamza = 0x0621,
	Alef = 0x0627,
	AlefHamza = 0x0623,
	WawHamza = 0x0624,
	AlefMaksoor = 0x0625,
	AlefMagsora = 0x0649,
	HamzaNabera = 0x0626,
	Ba = 0x0628,
	Ta = 0x062A,
	Tha2 = 0x062B,
	Jeem = 0x062C,
	H7aa = 0x062D,
	Khaa2 = 0x062E,
	Dal = 0x062F,
	Thal = 0x0630,
	Ra2 = 0x0631,
	Zeen = 0x0632,
	Seen = 0x0633,
	Sheen = 0x0634,
	S9a = 0x0635,
	Dha = 0x0636,
	T6a = 0x0637,
	T6ha = 0x0638,
	Ain = 0x0639,
	Gain = 0x063A,
	Fa = 0x0641,
	Gaf = 0x0642,
	Kaf = 0x0643,
	Lam = 0x0644,
	Meem = 0x0645,
	Noon = 0x0646,
	Ha = 0x0647,
	Waw = 0x0648,
	Ya = 0x064A,
	AlefMad = 0x0622,
	TaMarboota = 0x0629,
	PersianPe = 0x067E,		// Persian Letters;
	PersianChe = 0x0686,
	PersianZe = 0x0698,
	PersianGaf = 0x06AF,
	PersianGaf2 = 0x06A9,
	PersianYeh = 0x06CC,
	
}

/// <summary>
/// Data Structure for conversion
/// </summary>
internal struct ArabicMapping
{
	public int from;
	public int to;
	public ArabicMapping(int from, int to)
	{
		this.from = from;
		this.to = to;
	}
}

/// <summary>
/// Sets up and creates the conversion table 
/// </summary>
internal class ArabicTable
{
	
	private static ArabicMapping[] mapList;
	private static ArabicTable arabicMapper;
	
	/// <summary>
	/// Setting up the conversion table
	/// </summary>
	private ArabicTable()
	{
		mapList = new [] {
			new ArabicMapping((int)GeneralArabicLetters.Hamza, (int)IsolatedArabicLetters.Hamza),
			new ArabicMapping((int)GeneralArabicLetters.Alef, (int)IsolatedArabicLetters.Alef),
			new ArabicMapping((int)GeneralArabicLetters.AlefHamza, (int)IsolatedArabicLetters.AlefHamza),
			new ArabicMapping((int)GeneralArabicLetters.WawHamza, (int)IsolatedArabicLetters.WawHamza),
			new ArabicMapping((int)GeneralArabicLetters.AlefMaksoor, (int)IsolatedArabicLetters.AlefMaksoor),
			new ArabicMapping((int)GeneralArabicLetters.AlefMagsora, (int)IsolatedArabicLetters.AlefMaksora),
			new ArabicMapping((int)GeneralArabicLetters.HamzaNabera, (int)IsolatedArabicLetters.HamzaNabera),
			new ArabicMapping((int)GeneralArabicLetters.Ba, (int)IsolatedArabicLetters.Ba),
			new ArabicMapping((int)GeneralArabicLetters.Ta, (int)IsolatedArabicLetters.Ta),
			new ArabicMapping((int)GeneralArabicLetters.Tha2, (int)IsolatedArabicLetters.Tha2),
			new ArabicMapping((int)GeneralArabicLetters.Jeem, (int)IsolatedArabicLetters.Jeem),
			new ArabicMapping((int)GeneralArabicLetters.H7aa, (int)IsolatedArabicLetters.H7aa),
			new ArabicMapping((int)GeneralArabicLetters.Khaa2, (int)IsolatedArabicLetters.Khaa2),
			new ArabicMapping((int)GeneralArabicLetters.Dal, (int)IsolatedArabicLetters.Dal),
			new ArabicMapping((int)GeneralArabicLetters.Thal, (int)IsolatedArabicLetters.Thal),
			new ArabicMapping((int)GeneralArabicLetters.Ra2, (int)IsolatedArabicLetters.Ra2),
			new ArabicMapping((int)GeneralArabicLetters.Zeen, (int)IsolatedArabicLetters.Zeen),
			new ArabicMapping((int)GeneralArabicLetters.Seen, (int)IsolatedArabicLetters.Seen),
			new ArabicMapping((int)GeneralArabicLetters.Sheen, (int)IsolatedArabicLetters.Sheen),
			new ArabicMapping((int)GeneralArabicLetters.S9a, (int)IsolatedArabicLetters.S9a),
			new ArabicMapping((int)GeneralArabicLetters.Dha, (int)IsolatedArabicLetters.Dha),
			new ArabicMapping((int)GeneralArabicLetters.T6a, (int)IsolatedArabicLetters.T6a),
			new ArabicMapping((int)GeneralArabicLetters.T6ha, (int)IsolatedArabicLetters.T6ha),
			new ArabicMapping((int)GeneralArabicLetters.Ain, (int)IsolatedArabicLetters.Ain),
			new ArabicMapping((int)GeneralArabicLetters.Gain, (int)IsolatedArabicLetters.Gain),
			new ArabicMapping((int)GeneralArabicLetters.Fa, (int)IsolatedArabicLetters.Fa),
			new ArabicMapping((int)GeneralArabicLetters.Gaf, (int)IsolatedArabicLetters.Gaf),
			new ArabicMapping((int)GeneralArabicLetters.Kaf, (int)IsolatedArabicLetters.Kaf),
			new ArabicMapping((int)GeneralArabicLetters.Lam, (int)IsolatedArabicLetters.Lam),
			new ArabicMapping((int)GeneralArabicLetters.Meem, (int)IsolatedArabicLetters.Meem),
			new ArabicMapping((int)GeneralArabicLetters.Noon, (int)IsolatedArabicLetters.Noon),
			new ArabicMapping((int)GeneralArabicLetters.Ha, (int)IsolatedArabicLetters.Ha),
			new ArabicMapping((int)GeneralArabicLetters.Waw, (int)IsolatedArabicLetters.Waw),
			new ArabicMapping((int)GeneralArabicLetters.Ya, (int)IsolatedArabicLetters.Ya),
			new ArabicMapping((int)GeneralArabicLetters.AlefMad, (int)IsolatedArabicLetters.AlefMad),
			new ArabicMapping((int)GeneralArabicLetters.TaMarboota, (int)IsolatedArabicLetters.TaMarboota),
			new ArabicMapping((int)GeneralArabicLetters.PersianPe, (int)IsolatedArabicLetters.PersianPe),
			new ArabicMapping((int)GeneralArabicLetters.PersianChe, (int)IsolatedArabicLetters.PersianChe),
			new ArabicMapping((int)GeneralArabicLetters.PersianZe, (int)IsolatedArabicLetters.PersianZe),
			new ArabicMapping((int)GeneralArabicLetters.PersianGaf, (int)IsolatedArabicLetters.PersianGaf),
			new ArabicMapping((int)GeneralArabicLetters.PersianGaf2, (int)IsolatedArabicLetters.PersianGaf2),
			new ArabicMapping((int)GeneralArabicLetters.PersianYeh, (int)IsolatedArabicLetters.PersianYeh)
		};
	}
	
	/// <summary>
	/// Singleton design pattern, Get the mapper. If it was not created before, create it.
	/// </summary>
	internal static ArabicTable ArabicMapper
	{
		get
		{
			if (arabicMapper == null)
				arabicMapper = new ArabicTable();
			return arabicMapper;
		}
	}
	
	internal int Convert(int toBeConverted)
	{
		for (int i = 0; i < mapList.Length; i++) {
			var arabicMap = mapList[i];
			
			if (arabicMap.from == toBeConverted)
			{
				return arabicMap.to;
			}
		}
		
		return toBeConverted;
	}
	
	
}


internal class TashkeelLocation
{
	public char tashkeel;
	public int position;
	public TashkeelLocation(char tashkeel, int position)
	{
		this.tashkeel = tashkeel;
		this.position = position;
	}
}

public class RichTextMatch
{
	public RichTextMatch pair;
	public string value;
	public string keyWord;	//标签关键字
	public int tagVal;	// 0 开始标签 1结束标签
	public int startIdx;
	public int endIdx;
	public bool needSwap; //是否需要交换标签
	public bool isAppend;
}

internal class ArabicFixerTool
{
	internal struct PairInfo
	{
		public char ch;
		public bool reverse;
	}
	internal static bool showTashkeel = true;
	internal static bool combineTashkeel = true;
	internal static bool useHinduNumbers = false;
	public static bool ContainsRTL = false;
  
	internal static StringBuilder internalStringBuilder = new StringBuilder();
	public static List<RichTextMatch> s_richLst = new List<RichTextMatch>();
	static Regex s_rgxRichText = new Regex(@"</?\w+(?:=[^>]*)?>");
	static Queue<RichTextMatch> s_qRichText = new Queue<RichTextMatch>();
	static Stack<RichTextMatch> s_stkRTTmp = new Stack<RichTextMatch>();
	static readonly char[] s_nearNexPunctuation={'-', '+', '.'};		//靠近后文的符号
	static readonly char[] s_nearLastPunctuation={'%'};		//靠近后文的符号
	static readonly char[] s_replacePunctuation = { '“', '”', '(', ')', '（', '）', '[', ']', '【', '】', '{', '}', '≤', '≥', '<', '>' };
	internal static List<char> s_ltrChList = new List<char>(); //左向右内容列表
	internal static Stack<PairInfo> s_stkPairSymbol = new Stack<PairInfo>();

	internal static bool IsNearNextPunctuation(char c)
    {
        for (int i = 0; i < s_nearNexPunctuation.Length; i++)
        {
            if (c == s_nearNexPunctuation[i])
            {
                return true;
            }
        }
        return false;
    }
	internal static bool IsNearLastPunctuation(char c)
    {
        for (int i = 0; i < s_nearLastPunctuation.Length; i++)
        {
            if (c == s_nearLastPunctuation[i])
            {
                return true;
            }
        }
        return false;
    }

	internal static void RemoveTashkeel(ref string str, out List<TashkeelLocation> tashkeelLocation)
	{
		tashkeelLocation = new List<TashkeelLocation>();

		var lastSplitIndex = 0;
		internalStringBuilder.Clear();
		internalStringBuilder.EnsureCapacity(str.Length);

		int index = 0;

		void IncrementSB(ref string str, int i) {
			if (i - lastSplitIndex > 0) {
				internalStringBuilder.Append(str, lastSplitIndex, i - lastSplitIndex);
			}
			lastSplitIndex = i + 1;
		}

		for (int i = 0; i < str.Length; i++) {
			if (str[i] == (char)0x064B) {
				// Tanween Fatha
				tashkeelLocation.Add(new TashkeelLocation((char)0x064B, i));
				index++;
				IncrementSB(ref str, i);
			} else if (str[i] == (char)0x064C) {
				// Tanween Damma
				tashkeelLocation.Add(new TashkeelLocation((char)0x064C, i));
				index++;
				IncrementSB(ref str, i);
			} else if (str[i] == (char)0x064D) {
				// Tanween Kasra
				tashkeelLocation.Add(new TashkeelLocation((char)0x064D, i));
				index++;
				IncrementSB(ref str, i);
			} else if (str[i] == (char)0x064E) {
				// Fatha
				if (index > 0 && combineTashkeel) {
					if (tashkeelLocation[index - 1].tashkeel == (char)0x0651) // Shadda
					{
						tashkeelLocation[index - 1].tashkeel = (char)0xFC60; // Shadda With Fatha
						IncrementSB(ref str, i);
						continue;
					}
				}

				tashkeelLocation.Add(new TashkeelLocation((char)0x064E, i));
				index++;
				IncrementSB(ref str, i);
			} else if (str[i] == (char)0x064F) {
				// DAMMA
				if (index > 0 && combineTashkeel) {
					if (tashkeelLocation[index - 1].tashkeel == (char)0x0651) {
						// SHADDA
						tashkeelLocation[index - 1].tashkeel = (char)0xFC61; // Shadda With DAMMA
						IncrementSB(ref str, i);
						continue;
					}
				}

				tashkeelLocation.Add(new TashkeelLocation((char)0x064F, i));
				index++;
				IncrementSB(ref str, i);
			} else if (str[i] == (char)0x0650) {
				// KASRA
				if (index > 0 && combineTashkeel) {
					if (tashkeelLocation[index - 1].tashkeel == (char)0x0651) {
						// SHADDA
						tashkeelLocation[index - 1].tashkeel = (char)0xFC62; // Shadda With KASRA
						IncrementSB(ref str, i);
						continue;
					}
				}

				tashkeelLocation.Add(new TashkeelLocation((char)0x0650, i));
				index++;
				IncrementSB(ref str, i);
			} else if (str[i] == (char)0x0651) {
				// SHADDA
				if (index > 0 && combineTashkeel) {
					if (tashkeelLocation[index - 1].tashkeel == (char)0x064E) // FATHA
					{
						tashkeelLocation[index - 1].tashkeel = (char)0xFC60; // Shadda With Fatha
						IncrementSB(ref str, i);
						continue;
					}

					if (tashkeelLocation[index - 1].tashkeel == (char)0x064F) // DAMMA
					{
						tashkeelLocation[index - 1].tashkeel = (char)0xFC61; // Shadda With DAMMA
						IncrementSB(ref str, i);
						continue;
					}

					if (tashkeelLocation[index - 1].tashkeel == (char)0x0650) // KASRA
					{
						tashkeelLocation[index - 1].tashkeel = (char)0xFC62; // Shadda With KASRA
						IncrementSB(ref str, i);
						continue;
					}
				}

				tashkeelLocation.Add(new TashkeelLocation((char)0x0651, i));
				index++;
				IncrementSB(ref str, i);
			} else if (str[i] == (char)0x0652) {
				// SUKUN
				tashkeelLocation.Add(new TashkeelLocation((char)0x0652, i));
				index++;
				IncrementSB(ref str, i);
			} else if (str[i] == (char)0x0653) {
				// MADDAH ABOVE
				tashkeelLocation.Add(new TashkeelLocation((char)0x0653, i));
				index++;
				IncrementSB(ref str, i);
			} else if (str[i] == (char)0xFC60) {
				IncrementSB(ref str, i);
			} else if (str[i] == (char)0xFC61) {
				IncrementSB(ref str, i);
			} else if (str[i] == (char)0xFC62) {
				IncrementSB(ref str, i);
			}
		}

		if (lastSplitIndex != 0) {
			IncrementSB(ref str, str.Length);
			str = internalStringBuilder.ToString();
		}
	}
	
	internal static void ReturnTashkeel(ref char[] letters, List<TashkeelLocation> tashkeelLocation)
	{
		Array.Resize(ref letters, letters.Length + tashkeelLocation.Count);

		for (int i = 0; i < tashkeelLocation.Count; i++) {
			var tl = tashkeelLocation[i];

			for (int j = letters.Length - 1; j > tl.position; j--) {
				letters[j] = letters[j - 1];
			}
			
			letters[tl.position] = tl.tashkeel;
		}
	}



	//连续非阿语 非数字字符列表
	internal static void AddLTRChar(char ch)
	{
		s_ltrChList.Add(ch);
	}


	internal static void AddRichTag(string strValue)
	{
		for (int i = strValue.Length-1; i >= 0; i--)
		{
			AddLTRChar(strValue[strValue.Length - 1 - i]);
		}
	}

	internal static void AppendCharListFinal(List<char> charList)
	{
		if (charList != null && charList.Count > 0)
		{
			for (int j = 0; j < charList.Count; j++)
				internalStringBuilder.Append(charList[charList.Count - 1 - j]);
			charList.Clear();
		}
	}

	internal static void AppendCharList() 
	{
		AppendCharListFinal(s_ltrChList);
	}


	/// <summary>
	/// Converts a string to a form in which the sting will be displayed correctly for arabic text.
	/// </summary>
	/// <param name="str">String to be converted. Example: "Aaa"</param>
	/// <returns>Converted string. Example: "aa aaa A" without the spaces.</returns>
	internal static string FixLine(string str)
	{
		ContainsRTL = false;
		// Debug.Log($"FixLine:{str}");
		RemoveTashkeel(ref str, out var tashkeelLocation);
		ParseRichText(str);
		if(s_ltrChList.Count>0)
		{
			Debug.LogError($"FixLine,有脏数据：{string.Concat(s_ltrChList)}");
		}
		
		char[] lettersOrigin = new char[str.Length];
		char[] lettersFinal = str.ToCharArray();
		
		for (int i = 0; i < lettersOrigin.Length; i++)
		{
			lettersOrigin[i] = (char)ArabicTable.ArabicMapper.Convert(str[i]);
		}
		
		
		for (int i = 0; i<lettersOrigin.Length; i++)
		{
			bool skip = false;

			// For special Lam Letter connections.
			if (lettersOrigin[i] == (char)IsolatedArabicLetters.Lam)
			{
				
				if (i < lettersOrigin.Length - 1)
				{
					if ((lettersOrigin[i + 1] == (char)IsolatedArabicLetters.AlefMaksoor))
					{
						lettersOrigin[i] = (char)0xFEF7;
						lettersFinal[i + 1] = (char)0xFFFF;
						skip = true;
					}
					else if ((lettersOrigin[i + 1] == (char)IsolatedArabicLetters.Alef))
					{
						lettersOrigin[i] = (char)0xFEF9;
						lettersFinal[i + 1] = (char)0xFFFF;
						skip = true;
					}
					else if ((lettersOrigin[i + 1] == (char)IsolatedArabicLetters.AlefHamza))
					{
						lettersOrigin[i] = (char)0xFEF5;
						lettersFinal[i + 1] = (char)0xFFFF;
						skip = true;
					}
					else if ((lettersOrigin[i + 1] == (char)IsolatedArabicLetters.AlefMad))
					{
						lettersOrigin[i] = (char)0xFEF3;
						lettersFinal[i + 1] = (char)0xFFFF;
						skip = true;
					}
				}
				
			}
			
			
			if (!IsIgnoredCharacter(lettersOrigin[i]))
			{
				if (IsMiddleLetter(lettersOrigin, i))
					lettersFinal[i] = (char)(lettersOrigin[i] + 3);
				else if (IsFinishingLetter(lettersOrigin, i))
					lettersFinal[i] = (char)(lettersOrigin[i] + 1);
				else if (IsLeadingLetter(lettersOrigin, i))
					lettersFinal[i] = (char)(lettersOrigin[i] + 2);
			}
			
			if (skip)
				i++;
			
			//chaning numbers to hindu
			if(useHinduNumbers) {
				lettersFinal[i] = (char)HandleInduNumber(lettersOrigin[i], lettersFinal[i]);
			}
			
		}
		
		//Return the Tashkeel to their places.
		if(showTashkeel && tashkeelLocation.Count > 0)
			ReturnTashkeel(ref lettersFinal, tashkeelLocation);

		internalStringBuilder.Clear();
		internalStringBuilder.EnsureCapacity(lettersFinal.Length);
		s_stkPairSymbol.Clear();

		bool isLastRTL = true;
		bool isLastLTR = false;
		RichTextMatch currMt = s_qRichText.Count>0 ? s_qRichText.Dequeue() : null;
		for (int i = 0; i < lettersFinal.Length; i++)
		{
			char letterI = lettersFinal[i];
			// Debug.Log($"{letterI}, IsPunctuation:{char.IsPunctuation(letterI)}, IsSymbol:{char.IsSymbol(letterI)}");
			if(letterI=='<')
			{
				if(currMt!=null)
				{
					string strCurrVal = currMt.value;
					bool isMatch = true;
					int nowIdx = i;
					for(int j=0; j<=strCurrVal.Length-1; j++)
					{
						if(strCurrVal[j] != lettersFinal[nowIdx])
						{
							isMatch = false;
							break;
						}
						nowIdx ++;
					}
					if(isMatch && currMt.pair!=null)
					{
						if(currMt.tagVal==0 && currMt.needSwap)
						{
							AddRichTag(currMt.needSwap?currMt.pair.value:strCurrVal);
							AppendCharList();
							currMt.isAppend = true;
						}
						else if(currMt.tagVal==1 && (currMt.pair.isAppend ||IsLastPairEndSymbol(lettersFinal, i)))
						// else if(currMt.tagVal==1 && (currMt.pair.isAppend))
						{
							if(s_ltrChList.Count>0 && currMt.needSwap)
							{
								AppendCharList();
							}
							AddRichTag(currMt.needSwap?currMt.pair.value:strCurrVal);
							AppendCharList();
						}
						else
						{
							AddRichTag(currMt.needSwap?currMt.pair.value:strCurrVal);
						}
						i += strCurrVal.Length-1;
						currMt = s_qRichText.Count>0 ? s_qRichText.Dequeue() : null;
						continue;
					}
				}
			}

			if(char.IsWhiteSpace(letterI))
			{
				if(s_ltrChList.Count>0)
				{
					if((currMt==null||currMt.tagVal==0) && IsRTLNextWord(lettersFinal, i, false))
					{
						AppendCharList();
						internalStringBuilder.Append(letterI);
					}
					else
					{
						AddLTRChar(letterI);
					}
				}
				else
				{
					internalStringBuilder.Append(letterI);
				}
			}
			else if (char.IsSymbol(letterI) || char.IsPunctuation(letterI))
			{
				int replaceIdx = ReplacePunctuation(letterI);
				if(replaceIdx>0)
				{
					if(replaceIdx%2 == 1) // 左边符号 (
					{
						PairInfo info = new PairInfo(){ch=letterI, reverse=true};
						if(s_ltrChList.Count>0)
						{
							
							if(IsRTLNextWord(lettersFinal, i) && (currMt==null || currMt.tagVal==0))
							{
								AppendCharList();
								internalStringBuilder.Append(s_replacePunctuation[replaceIdx]);
							}
							else
							{
								info.reverse = false;
								AddLTRChar(letterI);
							}
						}
						else
						{
							internalStringBuilder.Append(s_replacePunctuation[replaceIdx]);
						}
						s_stkPairSymbol.Push(info);
					}
					else // 右边符号)
					{
						PairInfo pairInfo = s_stkPairSymbol.Count>0 ? s_stkPairSymbol.Peek(): new PairInfo();
						if(pairInfo.ch==s_replacePunctuation[replaceIdx])
						{
							//匹配上
							s_stkPairSymbol.Pop();
							if(pairInfo.reverse)
							{
								AppendCharList();
								internalStringBuilder.Append(s_replacePunctuation[replaceIdx]);
							}
							else
							{
								AddLTRChar(letterI);
							}
						}
						else
						{
							// 没匹配上 就根据当前在左向右还是右向左队列处理
							if(s_ltrChList.Count>0)
							{
								AddLTRChar(letterI);
							}
							else
							{
								internalStringBuilder.Append(s_replacePunctuation[replaceIdx]);
							}

						}
					}
				}
				else
				{
					// 非成对的符号
					bool isNearNext = IsNearNextPunctuation(letterI);
					bool isNearLast = IsNearLastPunctuation(letterI);
					if(s_ltrChList.Count>0)
					{
						// 引号这个符号比较特殊，先写死判断，后面如果还有其他特殊符号，再拉一个配置出来
						if((!IsLTRNextWord(lettersFinal, i) && !isNearLast && !(currMt!=null&&currMt.tagVal==1&&!currMt.needSwap)) || letterI=='\"')
						{
							AppendCharList();
							internalStringBuilder.Append(letterI);
						}
						else
						{
							AddLTRChar(letterI);
						}
					}
					else
					{
						if(isLastLTR && IsLTRNextWord(lettersFinal, i))
						{
							AddLTRChar(letterI);
						}
						else
						{
							internalStringBuilder.Append(letterI);
						}
					}
				}
			}
			else if (IsRTL(letterI))
			{
				if(s_ltrChList.Count>0)
				{
					AppendCharList();
				}
				if(letterI != 0x200F)
				{
					internalStringBuilder.Append(letterI);
				}
				
				isLastRTL = true;
				isLastLTR = false;
				ContainsRTL = true;
			}
			else
			{
				if (letterI != 0xFFFF)
				{
					AddLTRChar(letterI);
					isLastRTL = false;
					isLastLTR = true;
				}
			}
		}

		// AppendCharList();
		if(s_ltrChList.Count>0)
		{
			AppendCharList();
		}

		char[] charArray = internalStringBuilder.ToString().ToCharArray();
		Array.Reverse(charArray);
		
		return string.Concat(charArray);
	}

	// 从当前位置往左，第一个非空格非标点的字符是不是阿拉伯字符
    public static bool IsRTLNextWord(char[] sb, int index, bool ignorePunctuation=false)
    {
        bool isRTL = false;
        for (int i=index+1; i<=sb.Length-1; i++)
        {
			char ch = sb[i];
            // 找到第一个非空格非标点的字符，判断是不是阿拉伯字符
            if (char.IsWhiteSpace(ch) || (!ignorePunctuation && char.IsPunctuation(ch)))
            {
                continue;
            }

			// 富文本不判断
			bool isInRichText = IsIdxInRichText(i);
			if(!isInRichText)
			{
				isRTL = IsRTL(ch);
				break;
			}
        }
        return isRTL;
    }
	public static bool IsLTRNextWord(char[] sb, int index, bool ignorePunctuation=false)
    {
        bool isLTR = true;
        for (int i=index+1; i<=sb.Length-1; i++)
        {
			char ch = sb[i];
            // 找到第一个非空格非标点的字符，判断是不是阿拉伯字符
            if (char.IsWhiteSpace(ch) || (!ignorePunctuation && char.IsPunctuation(ch)))
            {
                continue;
            }

			// 富文本不判断
			bool isInRichText = IsIdxInRichText(i);
			if(!isInRichText)
			{
				isLTR = !IsRTL(ch);
				break;
			}
        }
        return isLTR;
    }

	//上一个符号是成对符号的结束符
	public static bool IsLastPairEndSymbol(char[] sb, int index)
	{
        for (int i=index-1; i>=0; i--)
        {
			char ch = sb[i];
            if (char.IsWhiteSpace(ch))
            {
                continue;
            }
			bool isInRichText = IsIdxInRichText(i);
			if(!isInRichText)
			{
				int symbolIdx = ReplacePunctuation(ch);
				return symbolIdx>0 && symbolIdx%2==0;
			}

        }
        return false;
	}

	static bool IsIdxInRichText(int i)
	{
		bool isInRichText = false;
		for(int idx=0; idx<=s_richLst.Count-1; idx++)
		{
			RichTextMatch richTextMatch = s_richLst[idx];
			if(i>= richTextMatch.startIdx && i<= richTextMatch.endIdx)
			{
				isInRichText = true;
				break;
			}
		}
		return isInRichText;
	}

	// 替换左右标点符号
    internal static int ReplacePunctuation(char c)
    {
        for (int i = 0; i < s_replacePunctuation.Length; i++)
        {
            if (c == s_replacePunctuation[i])
            {
                return i + ((i % 2 == 0) ? 1 : -1);
            }
        }
        return -1;
    }

	public static bool IsRTL(char c)
    {
        int num = c;
        if ((num >= 0x0600 && num <= 0x06FF) ||
           (num >= 0x0750 && num <= 0x07FF) ||
           (num >= 0x0870 && num <= 0x089F) ||
           (num >= 0x08A0 && num <= 0x08FF) ||
           (num >= 0xFB50 && num <= 0xFDFF) ||
           (num >= 0xFE70 && num <= 0xFEFF) || num==0x200F )      // 大于第一平面的字符不处理
        {
            return true;
        }
        return false;
    }
	
	internal static ushort HandleInduNumber(ushort letterOrigin, ushort letterFinal) {
		if(letterOrigin ==  0x0030)
			return 0x0660;
		else if(letterOrigin == 0x0031)
			return 0x0661;
		else if(letterOrigin == 0x0032)
			return 0x0662;
		else if(letterOrigin == 0x0033)
			return 0x0663;
		else if(letterOrigin == 0x0034)
			return 0x0664;
		else if(letterOrigin == 0x0035)
			return 0x0665;
		else if(letterOrigin == 0x0036)
			return 0x0666;
		else if(letterOrigin == 0x0037)
			return 0x0667;
		else if(letterOrigin == 0x0038)
			return 0x0668;
		else if(letterOrigin == 0x0039)
			return 0x0669;
		else 
			return letterFinal;
	}
	
	/// <summary>
	/// English letters, numbers and punctuation characters are ignored. This checks if the ch is an ignored character.
	/// </summary>
	/// <param name="ch">The character to be checked for skipping</param>
	/// <returns>True if the character should be ignored, false if it should not be ignored.</returns>
	internal static bool IsIgnoredCharacter(char ch)
	{
		bool isPunctuation = char.IsPunctuation(ch);
		bool isNumber = char.IsNumber(ch);
		bool isLower = char.IsLower(ch);
		bool isUpper = char.IsUpper(ch);
		bool isSymbol = char.IsSymbol(ch);
		bool isPersianCharacter = ch == (char)0xFB56 || ch == (char)0xFB7A || ch == (char)0xFB8A || ch == (char)0xFB92 || ch == (char)0xFB8E;
        bool isPresentationFormB = (ch <= (char)0xFEFF && ch >= (char)0xFE70);
        bool isAcceptableCharacter = isPresentationFormB || isPersianCharacter || ch == (char)0xFBFC;
        
        return isPunctuation ||
            isNumber ||
                isLower ||
                isUpper ||
                isSymbol ||
                !isAcceptableCharacter ||
                ch == 'a' || ch == '>' || ch == '<' || ch == (char)0x061B;
	}
	
	/// <summary>
	/// Checks if the letter at index value is a leading character in Arabic or not.
	/// </summary>
	/// <param name="letters">The whole word that contains the character to be checked</param>
	/// <param name="index">The index of the character to be checked</param>
	/// <returns>True if the character at index is a leading character, else, returns false</returns>
	internal static bool IsLeadingLetter(char[] letters, int index)
	{

		bool lettersThatCannotBeBeforeALeadingLetter = index == 0 
			|| letters[index - 1] == ' ' 
				|| letters[index - 1] == '*' // ??? Remove?
				|| letters[index - 1] == 'A' // ??? Remove?
				|| char.IsPunctuation(letters[index - 1])
				|| letters[index - 1] == '>' 
				|| letters[index - 1] == '<' 
				|| letters[index - 1] == (int)IsolatedArabicLetters.Alef
				|| letters[index - 1] == (int)IsolatedArabicLetters.Dal 
				|| letters[index - 1] == (int)IsolatedArabicLetters.Thal
				|| letters[index - 1] == (int)IsolatedArabicLetters.Ra2 
				|| letters[index - 1] == (int)IsolatedArabicLetters.Zeen 
				|| letters[index - 1] == (int)IsolatedArabicLetters.PersianZe
				//|| letters[index - 1] == (int)IsolatedArabicLetters.AlefMaksora 
				|| letters[index - 1] == (int)IsolatedArabicLetters.Waw
				|| letters[index - 1] == (int)IsolatedArabicLetters.AlefMad
                || letters[index - 1] == (int)IsolatedArabicLetters.AlefHamza
                || letters[index - 1] == (int)IsolatedArabicLetters.Hamza
                || letters[index - 1] == (int)IsolatedArabicLetters.AlefMaksoor 
				|| letters[index - 1] == (int)IsolatedArabicLetters.WawHamza;

		bool lettersThatCannotBeALeadingLetter = letters[index] != ' ' 
			&& letters[index] != (int)IsolatedArabicLetters.Dal
			&& letters[index] != (int)IsolatedArabicLetters.Thal
				&& letters[index] != (int)IsolatedArabicLetters.Ra2 
				&& letters[index] != (int)IsolatedArabicLetters.Zeen 
				&& letters[index] != (int)IsolatedArabicLetters.PersianZe
				&& letters[index] != (int)IsolatedArabicLetters.Alef 
				&& letters[index] != (int)IsolatedArabicLetters.AlefHamza
				&& letters[index] != (int)IsolatedArabicLetters.AlefMaksoor
				&& letters[index] != (int)IsolatedArabicLetters.AlefMad
				&& letters[index] != (int)IsolatedArabicLetters.WawHamza
				&& letters[index] != (int)IsolatedArabicLetters.Waw
				&& letters[index] != (int)IsolatedArabicLetters.Hamza;

		bool lettersThatCannotBeAfterLeadingLetter = index < letters.Length - 1 
				&& letters[index + 1] != ' '
				&& letters[index + 1] != '\n'
				&& letters[index + 1] != '\r'
				&& !char.IsPunctuation(letters[index + 1] )
				&& !char.IsNumber(letters[index + 1])
				&& !char.IsSymbol(letters[index + 1])
				&& !char.IsLower(letters[index + 1])
				&& !char.IsUpper(letters[index + 1])
				&& letters[index + 1] != (int)IsolatedArabicLetters.Hamza;

		return lettersThatCannotBeBeforeALeadingLetter && lettersThatCannotBeALeadingLetter && lettersThatCannotBeAfterLeadingLetter;
	}
	
	/// <summary>
	/// Checks if the letter at index value is a finishing character in Arabic or not.
	/// </summary>
	/// <param name="letters">The whole word that contains the character to be checked</param>
	/// <param name="index">The index of the character to be checked</param>
	/// <returns>True if the character at index is a finishing character, else, returns false</returns>
	internal static bool IsFinishingLetter(char[] letters, int index)
	{
		bool lettersThatCannotBeBeforeAFinishingLetter = (index == 0) ? false : 
				letters[index - 1] != ' '

				&& letters[index - 1] != (int)IsolatedArabicLetters.Dal 
				&& letters[index - 1] != (int)IsolatedArabicLetters.Thal
				&& letters[index - 1] != (int)IsolatedArabicLetters.Ra2 
				&& letters[index - 1] != (int)IsolatedArabicLetters.Zeen 
				&& letters[index - 1] != (int)IsolatedArabicLetters.PersianZe
				//&& letters[index - 1] != (int)IsolatedArabicLetters.AlefMaksora 
				&& letters[index - 1] != (int)IsolatedArabicLetters.Waw
				&& letters[index - 1] != (int)IsolatedArabicLetters.Alef 
				&& letters[index - 1] != (int)IsolatedArabicLetters.AlefMad
				&& letters[index - 1] != (int)IsolatedArabicLetters.AlefHamza 
				&& letters[index - 1] != (int)IsolatedArabicLetters.AlefMaksoor
				&& letters[index - 1] != (int)IsolatedArabicLetters.WawHamza 
				&& letters[index - 1] != (int)IsolatedArabicLetters.Hamza

				&& !char.IsPunctuation(letters[index - 1]) 
                && !char.IsSymbol(letters[index-1])
				&& letters[index - 1] != '>' 
				&& letters[index - 1] != '<';
				

		bool lettersThatCannotBeFinishingLetters = letters[index] != ' ' && letters[index] != (int)IsolatedArabicLetters.Hamza;

		return lettersThatCannotBeBeforeAFinishingLetter && lettersThatCannotBeFinishingLetters;
	}
	
	/// <summary>
	/// Checks if the letter at index value is a middle character in Arabic or not.
	/// </summary>
	/// <param name="letters">The whole word that contains the character to be checked</param>
	/// <param name="index">The index of the character to be checked</param>
	/// <returns>True if the character at index is a middle character, else, returns false</returns>
	internal static bool IsMiddleLetter(char[] letters, int index)
	{
		bool lettersThatCannotBeMiddleLetters = (index == 0) ? false : 
			letters[index] != (int)IsolatedArabicLetters.Alef 
				&& letters[index] != (int)IsolatedArabicLetters.Dal
				&& letters[index] != (int)IsolatedArabicLetters.Thal 
				&& letters[index] != (int)IsolatedArabicLetters.Ra2
				&& letters[index] != (int)IsolatedArabicLetters.Zeen 
				&& letters[index] != (int)IsolatedArabicLetters.PersianZe 
				//&& letters[index] != (int)IsolatedArabicLetters.AlefMaksora
				&& letters[index] != (int)IsolatedArabicLetters.Waw 
				&& letters[index] != (int)IsolatedArabicLetters.AlefMad
				&& letters[index] != (int)IsolatedArabicLetters.AlefHamza 
				&& letters[index] != (int)IsolatedArabicLetters.AlefMaksoor
				&& letters[index] != (int)IsolatedArabicLetters.WawHamza 
				&& letters[index] != (int)IsolatedArabicLetters.Hamza;

		bool lettersThatCannotBeBeforeMiddleCharacters = (index == 0) ? false :
				letters[index - 1] != (int)IsolatedArabicLetters.Alef 
				&& letters[index - 1] != (int)IsolatedArabicLetters.Dal
				&& letters[index - 1] != (int)IsolatedArabicLetters.Thal 
				&& letters[index - 1] != (int)IsolatedArabicLetters.Ra2
				&& letters[index - 1] != (int)IsolatedArabicLetters.Zeen 
				&& letters[index - 1] != (int)IsolatedArabicLetters.PersianZe 
				//&& letters[index - 1] != (int)IsolatedArabicLetters.AlefMaksora
				&& letters[index - 1] != (int)IsolatedArabicLetters.Waw 
				&& letters[index - 1] != (int)IsolatedArabicLetters.AlefMad
				&& letters[index - 1] != (int)IsolatedArabicLetters.AlefHamza 
				&& letters[index - 1] != (int)IsolatedArabicLetters.AlefMaksoor
				&& letters[index - 1] != (int)IsolatedArabicLetters.WawHamza 
				&& letters[index - 1] != (int)IsolatedArabicLetters.Hamza
				&& !char.IsPunctuation(letters[index - 1])
				&& letters[index - 1] != '>' 
				&& letters[index - 1] != '<' 
				&& letters[index - 1] != ' ' 
				&& letters[index - 1] != '*';

		bool lettersThatCannotBeAfterMiddleCharacters = (index < letters.Length - 1) && (letters[index + 1] != ' ' 
			&& letters[index + 1] != '\r' 
			&& letters[index + 1] != (int)IsolatedArabicLetters.Hamza
			&& !char.IsNumber(letters[index + 1])
			&& !char.IsSymbol(letters[index + 1])
			&& !char.IsPunctuation(letters[index + 1]));

		return lettersThatCannotBeAfterMiddleCharacters && 
		       lettersThatCannotBeBeforeMiddleCharacters &&
		       lettersThatCannotBeMiddleLetters && 
		       !char.IsPunctuation(letters[index + 1]);
	}

	static void ParseRichText(string input)
	{
		int idx = 0;
		s_stkRTTmp.Clear();
		s_richLst.Clear();
		s_qRichText.Clear();
        while(idx<(input.Length-1))
        {
            Match match = s_rgxRichText.Match(input, idx);
			string strValue = match.Value;
            if(match == null || string.IsNullOrEmpty(strValue))
            {
                break;
            }
            RichTextMatch currMatch = new RichTextMatch
            {
                value = strValue,
                startIdx = match.Index,
                endIdx = match.Index + match.Length - 1
            };

            if (strValue[1] == '/')	//结束标签
			{
				currMatch.tagVal = 1;
				currMatch.keyWord = strValue.Substring(2, strValue.Length-3);

				RichTextMatch lastMatch = s_stkRTTmp.Count>0? s_stkRTTmp.Pop() :null;
				if(lastMatch!=null && lastMatch.keyWord==currMatch.keyWord && lastMatch.tagVal+currMatch.tagVal==1)
				{
					lastMatch.pair = currMatch;
					currMatch.pair = lastMatch;

					bool needSwap = false;
					for(int j=lastMatch.endIdx+1; j<currMatch.startIdx; j++)
					{
						if(IsRTL(input[j]))
						{
							needSwap = true;
							break;
						}
					}
					currMatch.needSwap = needSwap;
					lastMatch.needSwap = currMatch.needSwap;
				}
				else
				{
					s_stkRTTmp.Push(lastMatch);
					s_stkRTTmp.Push(currMatch);
				}
			}
			else	// 开始标签
			{
				currMatch.tagVal = 0;
				int equalIdx = strValue.IndexOf('=');
				if(equalIdx>0)
				{
					currMatch.keyWord = strValue.Substring(1, equalIdx-1);
				}
				else
				{
					currMatch.keyWord = strValue.Substring(1, strValue.Length-2);
				}
				s_stkRTTmp.Push(currMatch);
			}

            s_qRichText.Enqueue(currMatch);
			s_richLst.Add(currMatch);
            // Debug.Log($"{currMatch.value},{currMatch.keyWord}, {currMatch.tagVal},{currMatch.startIdx},{currMatch.endIdx},{currMatch.pair==null}");
            idx = match.Index + match.Length;
        }

		// while(s_stkRichText.Count>0)
		// {
		// 	RichTextMatch match = s_stkRichText.Pop();
		// 	if(match == null)
		// 	{
		// 		break;
		// 	}
		// 	Debug.Log($"{match.keyWord}, {match.tagVal},{match.startIdx},{match.endIdx},{match.pair==null}");
		// }
	}
}
