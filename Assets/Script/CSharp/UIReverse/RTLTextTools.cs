using ArabicSupport;
using RTLTMPro;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using System.Text.RegularExpressions;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace LD
{
    public class LDStringBulider
    {
        private static string EnterStr  = "\n";
        private static string SpaceStr  = " ";
        private StringBuilder mStringBuilder = new StringBuilder(128);
        public int Length { private set; get; }
        public void AppendEnter()
        {
            Append(EnterStr);
        }       
        public void AppendSpace()
        {
            Append(SpaceStr);
        }
        public void Append(string val)
        {
            Length++;
            mStringBuilder.Append(val);
        }      
        public override string ToString()
        {
            return mStringBuilder.ToString();
        }
        public void Clear()
        {
            Length = 0;
            mStringBuilder.Clear();
        }
    }
    public class RTLTextTools
    {
        //private static FastStringBuilder OutBulder = new FastStringBuilder(2048);
        private static LDStringBulider InBulder = new LDStringBulider();
        public static TextGenerator mGen = new TextGenerator();
        public static StringBuilder mStringBuilder = new StringBuilder();
        public static LDStringBulider mStringBuilderTemp = new LDStringBulider();
        public static LDStringBulider mStringBuilderRealTemp = new LDStringBulider();
        public static string EnterStr = "\n";
        public static string SpaceStr = " ";
        public static string BreakRightToLeft(TextGenerationSettings mSettings, string visualStr, float maxWidth)
        {
            mStringBuilder.Clear();
            mStringBuilderTemp.Clear();
            var Enterlines = visualStr.Split(EnterStr);
            for (int i = 0; i < Enterlines.Length; i++)
            {
                if (i > 0)
                {
                    mStringBuilder.Append(EnterStr);
                }

                string[] spaceLine = Enterlines[i].Split(SpaceStr);
                mStringBuilderTemp.Clear();
                mStringBuilderTemp.Append(spaceLine[0]);
                for (int j = 1; j < spaceLine.Length; j++)
                {
                    float w = mGen.GetPreferredWidth(mStringBuilderTemp.ToString() + SpaceStr + spaceLine[j], mSettings);
                    if (w >= maxWidth)
                    {
                        mStringBuilder.Append(mStringBuilderTemp.ToString());
                        mStringBuilder.Append(EnterStr);
                        mStringBuilderTemp.Clear();
                        mStringBuilderTemp.Append(spaceLine[j]);
                    }
                    else
                    {
                        mStringBuilderTemp.Append(SpaceStr);
                        mStringBuilderTemp.Append(spaceLine[j]);
                    }
                }
                if (mStringBuilderTemp.Length > 0)
                {
                    mStringBuilder.Append(mStringBuilderTemp.ToString());
                }
            }

            return mStringBuilder.ToString();



            // while (end > 0)
            // {
            //     int bestLen = 0;
            //
            //     // ① 从右往左试探宽度
            //     for (int len = 1; end - len >= 0; ++len)
            //     {
            //         string slice = visualStr.Substring(end - len, len);
            //         float w = mGen.GetPreferredWidth(slice, mSettings);
            //         if (w > maxWidth) break;
            //         bestLen = len;
            //     }
            //     if (bestLen == 0) bestLen = 1;          // 超长单词退回到字符级
            //
            //     // ② 把这一行加入列表（最右边的行会排在前面）
            //     lines.Add(visualStr.Substring(end - bestLen, bestLen));
            //     end -= bestLen;
            // }
            //
            // // ③ **不要再反转**，直接按收集顺序拼接
            // return string.Join(EnterStr, lines);
        }


        public static string GetRTLText(Text text, string val)
        {

            if (!val.Contains("</"))
            {
                val = GetNormalNewLineText(text, val);
            }
            else
            {
                val = GetRichNewLineText(text, val);
            }
            val = ArabicFixer.Fix(val, true);
            return val;
        }
        public static string GetRTLText(TMPro.TextMeshProUGUI text, string val)
        {
            //val = RTLSupport.ToVisualString(val,true,true,true,true);
            text.isRightToLeftText = true;
            // OutBulder.Clear();
            string[] textArray = val.Split(EnterStr);
            if (textArray.Length == 1)
            {
                val = RTLSupport.ToVisualString(val, false, true, true);
            }
            else
            {
                InBulder.Clear();
                for (int i = 0; i < textArray.Length; i++)
                {
                    InBulder.Append(RTLSupport.ToVisualString(textArray[i], false, true, true));
                    if (i != textArray.Length - 1)
                    {
                        InBulder.Append(EnterStr);
                    }
                }
                val = InBulder.ToString();
            }
            return val;
        }


        public static string GetNormalNewLineText(Text text, string val)
        {
            if (text.gameObject.GetComponent<ContentSizeFitter>() == null)
            {
                // 2. 计算行宽上限（文本矩形宽度减去左右 padding）
                float rectWidth = text.rectTransform.rect.width;
                int fontSize = text.fontSize;
                text.lineSpacing = 1;
                if (text.resizeTextForBestFit)
                {
                    int newFontSize = Mathf.FloorToInt(text.rectTransform.rect.height / 3);
                    if (newFontSize > text.resizeTextMinSize)
                    {
                        text.fontSize = Mathf.Min(newFontSize, text.resizeTextMaxSize);
                    }
                    else
                    {
                        text.fontSize = text.resizeTextMinSize;
                    }
                }

                // float paddingL    = mText.padding.left;
                // float paddingR    = mText.padding.right;
                // float maxLineW    = rectWidth - paddingL - paddingR;
                float maxLineW = rectWidth;
                // 3. 把 Text 的排版参数填到 TextGenerationSettings
                TextGenerationSettings mSettings = text.GetGenerationSettings(Vector2.zero); // 高度 0 = 不限制
                mSettings.generateOutOfBounds = true; // 允许超框以便测宽
                mSettings.horizontalOverflow = HorizontalWrapMode.Overflow;

                // 4. 按行宽从右往左插入 \u2028
                string wrapped = BreakRightToLeft(mSettings, val, maxLineW);
                if (text.resizeTextForBestFit)
                {
                    text.fontSize = fontSize;
                }
                // 5. 写回 Text
                return wrapped;
            }
            return val;
        }


        public static List<string> AtomicTokens(string s)
        {
            var tokens = new List<string>();
            var pairTag = new Regex(@"<(\w+)[^>]*>.*?</\1>", RegexOptions.Singleline);
            int i = 0, n = s.Length;
            while (i < n)
            {
                // 1. 整对标签
                var m = pairTag.Match(s, i);
                if (m.Success && m.Index == i)
                {
                    tokens.Add(m.Value);
                    i += m.Length;
                    continue;
                }
                // 2. 单标签（<br>、<color=#FF…> 等也可当原子）
                if (s[i] == '<')
                {
                    int e = s.IndexOf('>', i);
                    if (e > i)
                    {
                        tokens.Add(s.Substring(i, e - i + 1));
                        i = e + 1;
                        continue;
                    }
                }
                // 3. 普通字符
                tokens.Add(s[i].ToString());
                i++;
            }
            return tokens;
        }

        static readonly Regex kRichTagRegex = new Regex(@"<[^>]+>", RegexOptions.Compiled);
        /// <summary>
        /// 对 atom 列表从尾部开始：尽最大可能取 N 个 atom 放进一行 → 第一行；剩下继续
        /// </summary>
        public static string WrapRightToLeft(TextGenerationSettings mSettings, string visualStr, float maxWidth)
        {
            mStringBuilder.Clear();
            mStringBuilderTemp.Clear();
            mStringBuilderRealTemp.Clear();
            var Enterlines = visualStr.Split(EnterStr);
            for (int i = 0; i < Enterlines.Length; i++)
            {
                if (i > 0)
                {
                    mStringBuilder.Append(EnterStr);
                }

                List<string> atoms = AtomicTokens(Enterlines[i]);

                mStringBuilderTemp.Clear();
                mStringBuilderRealTemp.Clear();
                StringBuilder holdStrBuilder = new StringBuilder();

                atoms.Add(SpaceStr);

                foreach (string strVal in atoms)
                {
                    if (strVal == SpaceStr)
                    {
                        if (holdStrBuilder.Length > 0)
                        {
                            string holdStr = holdStrBuilder.ToString();
                            holdStrBuilder.Clear();
                            if (mStringBuilderTemp.Length == 0)
                            {
                                mStringBuilderTemp.Append(holdStr);
                                mStringBuilderRealTemp.Append(holdStr);
                            }
                            else
                            {
                                float w = mGen.GetPreferredWidth(mStringBuilderTemp.ToString() + SpaceStr + holdStr,
                                    mSettings);
                                if (w >= maxWidth)
                                {
                                    mStringBuilder.Append(mStringBuilderRealTemp.ToString());
                                    mStringBuilder.Append(EnterStr);
                                    mStringBuilderTemp.Clear();
                                    mStringBuilderTemp.Append(holdStr);

                                    mStringBuilderRealTemp.Clear();
                                    mStringBuilderRealTemp.Append(holdStr);
                                }
                                else
                                {
                                    mStringBuilderTemp.Append(SpaceStr);
                                    mStringBuilderTemp.Append(holdStr);

                                    mStringBuilderRealTemp.Append(SpaceStr);
                                    mStringBuilderRealTemp.Append(holdStr);
                                }
                            }
                        }
                    }
                    else
                    {
                        if (!strVal.Contains("</"))
                        {
                            holdStrBuilder.Append(strVal);
                        }
                        else
                        {
                            string holdStr;
                            if (holdStrBuilder.Length > 0)
                            {
                                holdStr = holdStrBuilder.ToString();
                                holdStrBuilder.Clear();
                                if (mStringBuilderTemp.Length == 0)
                                {
                                    mStringBuilderTemp.Append(holdStr);
                                    mStringBuilderRealTemp.Append(holdStr);
                                }
                                else
                                {
                                    float w = mGen.GetPreferredWidth(mStringBuilderTemp.ToString() + SpaceStr + holdStr,
                                        mSettings);
                                    if (w >= maxWidth)
                                    {
                                        mStringBuilder.Append(mStringBuilderRealTemp.ToString());
                                        mStringBuilder.Append(EnterStr);
                                        mStringBuilderTemp.Clear();
                                        mStringBuilderTemp.Append(holdStr);

                                        mStringBuilderRealTemp.Clear();
                                        mStringBuilderRealTemp.Append(holdStr);
                                    }
                                    else
                                    {
                                        mStringBuilderTemp.Append(holdStr);
                                        mStringBuilderRealTemp.Append(holdStr);
                                    }
                                }
                            }

                            holdStr = kRichTagRegex.Replace(strVal, ""); ;
                            string realStr = strVal;
                            if (mStringBuilderTemp.Length == 0)
                            {
                                mStringBuilderTemp.Append(holdStr);
                                mStringBuilderRealTemp.Append(realStr);
                            }
                            else
                            {
                                float w = mGen.GetPreferredWidth(mStringBuilderTemp .ToString()+ holdStr,
                                    mSettings);
                                if (w >= maxWidth)
                                {
                                    mStringBuilder.Append(mStringBuilderRealTemp.ToString());
                                    mStringBuilder.Append(EnterStr);
                                    mStringBuilderTemp.Clear();
                                    mStringBuilderTemp.Append(holdStr);

                                    mStringBuilderRealTemp.Clear();
                                    mStringBuilderRealTemp.Append(realStr);
                                }
                                else
                                {
                                    mStringBuilderTemp.Append(holdStr);
                                    mStringBuilderRealTemp.Append(realStr);
                                }
                            }
                        }
                    }
                }

                if (mStringBuilderRealTemp.Length > 0)
                {
                    mStringBuilder.Append(mStringBuilderRealTemp.ToString());
                }
            }
            return mStringBuilder.ToString();
        }

        public static string GetRichNewLineText(Text text, string val)
        {
            if (text.gameObject.GetComponent<ContentSizeFitter>() == null)
            {
                // 2. 计算行宽上限（文本矩形宽度减去左右 padding）
                // float paddingL    = mText.padding.left;
                // float paddingR    = mText.padding.right;
                // float maxLineW    = rectWidth - paddingL - paddingR;
                // 3. 把 Text 的排版参数填到 TextGenerationSettings

                // —— 3. 取 TextGenerator 设置 ——
                float maxW = text.rectTransform.rect.width;
                int fontSize = text.fontSize;
                text.lineSpacing = 1;
                if (text.resizeTextForBestFit)
                {
                    int newFontSize = Mathf.FloorToInt(text.rectTransform.rect.height / 3);
                    if (newFontSize > text.resizeTextMinSize)
                    {
                        text.fontSize = Mathf.Min(newFontSize, text.resizeTextMaxSize);
                    }
                    else
                    {
                        text.fontSize = text.resizeTextMinSize;
                    }
                }

                TextGenerationSettings mStg = text.GetGenerationSettings(new Vector2(maxW, 0));
                mStg.generateOutOfBounds = true;
                mStg.horizontalOverflow = HorizontalWrapMode.Overflow;

                // —— 4. 从右向左断行 ——
                string wrapped = WrapRightToLeft(mStg, val, maxW);

                if (text.resizeTextForBestFit)
                {
                    text.fontSize = fontSize;
                }
                // 5. 写回 Text
                return wrapped;
            }
            return val;
        }
        /*
        public static string ApplyBidiAlgorithm(string input, Text textComponent = null, TextMeshProUGUI textMeshProUGUI = null)
        {
            // finalText.Clear();
            // RTLSupport.FixRTL(input, finalText);
            // finalText.Reverse();
            // return finalText.ToString();
            // string input = text;
            // string pattern = @"<color=#([A-Fa-f0-9]{6,8})>(.*?)";
            // string pattern2 = @"<color=#{0}>{1}</color>";
            // text = "<color=#FF0000>Bruto</color> adcd <color=#FFFDFF>Brdfdsuto</color>";
            if (string.IsNullOrEmpty(input))
            {
                return string.Empty;
            }

            return input;

            // if (textComponent != null)
            // {
            //     Regex RichTextTagRegex = new Regex(@"<[^>]*>");
            //     string newInput = RichTextTagRegex.Replace(input,"");
            //     textComponent.text = newInput;
            //     
            //     Canvas.ForceUpdateCanvases();
            //     
            //     string newString = string.Empty;
            //     
            //     for (int i = 0; i < textComponent.cachedTextGenerator.lines.Count; i++)
            //     {
            //         int startIndex = textComponent.cachedTextGenerator.lines[i].startCharIdx;
            //         int endIndex = (i == textComponent.cachedTextGenerator.lines.Count - 1)
            //             ? textComponent.text.Length
            //             : textComponent.cachedTextGenerator.lines[i + 1].startCharIdx;
            //         int length = endIndex - startIndex;
            //         string lineWords = input.Substring(startIndex, length);
            //         
            //         lineWords = GetOriginalTextLine(input, lineWords, startIndex, length);
            //         newString += lineWords;
            //         if (!((i + 1) == textComponent.cachedTextGenerator.lines.Count))
            //         {
            //             if (!newString.EndsWith(EnterStr))
            //             {
            //                 newString += EnterStr;
            //             }
            //         }
            //     }
            //     return ArabicFixer.Fix(newString, true);
            // }

            return ArabicFixer.Fix(input, true);
            List<LDStringClas> stringListlist = new List<LDStringClas>();
            List<LDStringClas> FinalStringListlist = new List<LDStringClas>();
            string RealFinalText = string.Empty;
            string slpitStr = EnterStr;
            if (slpitStr.Contains("\r\n"))
            {
                slpitStr = "\r\n";
            }

            string[] textArray = input.Split(slpitStr);
            int index = 0;
            bool autoSize = false;
            if (textComponent != null)
            {
                autoSize = textComponent.resizeTextForBestFit;
                textComponent.resizeTextForBestFit = false;
            }
            else if (textMeshProUGUI != null)
            {
                autoSize = textMeshProUGUI.enableAutoSizing;
                textMeshProUGUI.enableAutoSizing = false;
            }

            foreach (string text in textArray)
            {
                FinalStringListlist.Clear();
                stringListlist.Clear();
                if (textArray.Length > 1 && index > 0)
                {
                    RealFinalText += slpitStr;
                }

                index++;

                string pattern = @"(<color=[^>]*>.*?</color>|[^<]+)";
                MatchCollection matches = Regex.Matches(text, pattern);

                foreach (Match OutMatch in matches)
                {
                    string pattern2 = @"<color=#(?<color>[A-Fa-f0-9]{6})>(?<text>.*?)</color>";
                    Match match = Regex.Match(OutMatch.Value, pattern2);


                    if (match.Success)
                    {
                        string color = match.Groups["color"].Value; // 提取颜色值
                        string text2 = match.Groups["text"].Value; // 提取文本


                        string[] SpaceSplit = text2.Split(SpaceStr);
                        if (SpaceSplit.Length > 1)
                        {
                            foreach (string spaceSplit in SpaceSplit)
                            {
                                LDStringClas stringClas = new LDStringClas();
                                stringListlist.Add(stringClas);
                                stringClas.Content = spaceSplit;
                                stringClas.ColorVal = color;
                                stringClas.SpaceCalc = true;
                            }
                        }
                        else
                        {
                            LDStringClas stringClas = new LDStringClas();
                            stringListlist.Add(stringClas);
                            stringClas.Content = text2;
                            stringClas.ColorVal = color;
                        }
                    }
                    else
                    {
                        string[] SpaceSplit = OutMatch.Value.Split(SpaceStr);
                        if (SpaceSplit.Length > 1)
                        {
                            int spaceIndex = -1;
                            foreach (string spaceSplit in SpaceSplit)
                            {
                                spaceIndex++;
                                LDStringClas stringClas = new LDStringClas();
                                stringListlist.Add(stringClas);
                                if (spaceIndex > 0)
                                {
                                    stringClas.Content = SpaceStr + spaceSplit;
                                }
                                else
                                {
                                    stringClas.Content = spaceSplit;
                                }

                                stringClas.SpaceCalc = true;
                            }
                        }
                        else
                        {
                            LDStringClas stringClas = new LDStringClas();
                            stringListlist.Add(stringClas);
                            stringClas.Content = OutMatch.Value;
                        }
                    }
                }

                List<int> indexList = new List<int>();
                float currentLineWidth = 0f;
                bool nextEnter = false;

                foreach (LDStringClas stringClas in stringListlist)
                {
                    indexList.Clear();
                    if (textComponent != null)
                    {
                        if (nextEnter)
                        {
                            stringClas.Content = stringClas.Content.Trim();
                            nextEnter = false;
                        }

                        nextEnter = CalcText(stringClas, textComponent, indexList, FinalStringListlist, ref currentLineWidth);
                    }
                    else if (textMeshProUGUI != null)
                    {
                        if (nextEnter)
                        {
                            stringClas.Content = stringClas.Content.Trim();
                            nextEnter = false;
                        }

                        nextEnter = CalcTextMeshPro(stringClas, textMeshProUGUI, indexList, FinalStringListlist, ref currentLineWidth);
                    }
                    else
                    {
                        FinalStringListlist.Add(stringClas);
                    }
                }


                List<LDStringClas> RealCalcList = new List<LDStringClas>();

                int calcIndex = 0;
                foreach (LDStringClas stringClas in FinalStringListlist)
                {
                    calcIndex++;
                    if (stringClas.EnterCalc)
                    {
                        if (RealCalcList.Count > 0)
                        {
                            RealFinalText += GetText(RealCalcList);
                            RealCalcList.Clear();
                        }

                        RealCalcList.Add(stringClas);
                        RealFinalText += GetText(RealCalcList);
                        if (calcIndex < FinalStringListlist.Count)
                        {
                            RealFinalText += slpitStr;
                        }

                        RealCalcList.Clear();
                    }
                    else if (stringClas.PreEnterCalc)
                    {
                        if (RealCalcList.Count > 0)
                        {
                            RealFinalText += GetText(RealCalcList);
                            RealCalcList.Clear();
                        }

                        RealCalcList.Add(stringClas);
                        RealFinalText += slpitStr + GetText(RealCalcList);
                        RealCalcList.Clear();
                    }
                    else
                    {
                        RealCalcList.Add(stringClas);
                    }
                }

                if (RealCalcList.Count > 0)
                {
                    RealFinalText += GetText(RealCalcList);
                }
            }

            if (textComponent != null)
            {
                textComponent.resizeTextForBestFit = autoSize;
            }
            else if (textMeshProUGUI != null)
            {
                textMeshProUGUI.enableAutoSizing = autoSize;
            }

            // 生成最终文本
            return RealFinalText;
        }


        public static HashSet<char> RTLCharSet = new HashSet<char>()
        {
            '%', '.', '（', '）', '(', ')', '-', '/', '《', '》', '[', ']', '「', '」', '『', '』', '【', '】', '〔', '〕', '〈', '〉', '〖', '〗', '〘', '〙', '〚', '〛', '｛', '｝', '〖', '〗', '〘', '〙', '〚', '〛', '〔',
            '〕', '〘', '〙', '〚', '〛',
        };

        private static bool IsRTL(char c)
        {
            if (RTLCharSet.Contains(c))
            {
                return false;
            }

            // 检查字符是否属于RTL字符集
            return (c >= 0x0591 && c <= 0x07FF) || (char.IsPunctuation(c) || char.IsSymbol(c)); // 包含希伯来语和阿拉伯语字符
        }

        private class LDStringClas
        {
            public string ColorStr = "<color=#{0}>{1}</color>";
            public List<string> Segments = new List<string>();
            public string Content;
            public string ColorVal;
            public int Size = -1;
            public bool EnterCalc;
            public bool PreEnterCalc;
            public bool SpaceCalc;

            public string GetString()
            {
                Segments.Reverse();
                if (!string.IsNullOrEmpty(ColorVal))
                {
                    return string.Format(ColorStr, ColorVal, string.Join("", Segments));
                }
                else
                {
                    return string.Join("", Segments);
                }
            }
        }

        public static string ApplyBidiAlgorithmSimble(string input)
        {
            if (string.IsNullOrEmpty(input))
            {
                return string.Empty;
            }

            return ArabicFixer.Fix(input, true);
            List<LDStringClas> stringListlist = new List<LDStringClas>();
            List<LDStringClas> FinalStringListlist = new List<LDStringClas>();
            string RealFinalText = string.Empty;
            string[] textArray = input.Split(EnterStr);
            int index = 0;
            foreach (string text in textArray)
            {
                FinalStringListlist.Clear();
                stringListlist.Clear();
                if (textArray.Length > 1 && index > 0)
                {
                    RealFinalText += EnterStr;
                }

                index++;

                string patternColor = @"(<color=[^>]*>.*?</color>|[^<]+)";

                MatchCollection matches = Regex.Matches(text, patternColor);

                foreach (Match OutMatch in matches)
                {
                    string pattern2 = @"<color=#(?<color>[A-Fa-f0-9]{6})>(?<text>.*?)</color>";
                    Match match = Regex.Match(OutMatch.Value, pattern2);

                    if (match.Success)
                    {
                        string color = match.Groups["color"].Value; // 提取颜色值
                        string text2 = match.Groups["text"].Value; // 提取文本

                        LDStringClas stringClas = new LDStringClas();
                        stringListlist.Add(stringClas);
                        stringClas.Content = text2;
                        stringClas.ColorVal = color;
                    }
                    else
                    {
                        LDStringClas stringClas = new LDStringClas();
                        stringListlist.Add(stringClas);
                        stringClas.Content = OutMatch.Value;
                    }
                }

                List<int> indexList = new List<int>();
                float currentLineWidth = 0f;

                foreach (LDStringClas stringClas in stringListlist)
                {
                    indexList.Clear();
                    FinalStringListlist.Add(stringClas);
                }

                RealFinalText += GetText(FinalStringListlist);
            }

            // 生成最终文本
            return RealFinalText;
        }

        private static bool CalcTextMeshPro(LDStringClas stringClas, TextMeshProUGUI textComponent, List<int> indexList, List<LDStringClas> FinalStringListlist,
            ref float currentLineWidth)
        {
            string content = stringClas.Content;
            // 富文本需要优化 

            float maxLineWidth = textComponent.preferredWidth;
            if (textComponent.rectTransform.rect.width > 0)
            {
                maxLineWidth = textComponent.rectTransform.rect.width;
            }

            int slpitIndex = -1;
            if (!stringClas.SpaceCalc)
            {
                foreach (char charContent in content)
                {
                    slpitIndex++;
                    float charWidth = GetTextMeshProCharWidth(charContent.ToString(), textComponent);

                    if (textComponent.enableAutoSizing)
                    {
                        // charWidth *= (1.0f * textComponent.fontSizeMax / textComponent.fontSize);
                    }

                    if (currentLineWidth + charWidth > maxLineWidth)
                    {
                        indexList.Add(slpitIndex - 1);
                        currentLineWidth = charWidth;
                    }
                    else
                    {
                        currentLineWidth += charWidth;
                    }
                }

                if (indexList.Count > 0)
                {
                    int subIndex = -1;
                    indexList.Add(content.Length - 1);
                    for (int i = 0; i < indexList.Count; i++)
                    {
                        LDStringClas newStringClas = new LDStringClas();
                        int subLength = indexList[i] - subIndex;
                        newStringClas.Content = stringClas.Content.Substring(subIndex + 1, subLength);
                        newStringClas.ColorVal = stringClas.ColorVal;
                        newStringClas.SpaceCalc = stringClas.SpaceCalc;
                        // if (i < indexList.Count - 1)
                        {
                            newStringClas.EnterCalc = true;
                        }
                        subIndex = indexList[i];
                        FinalStringListlist.Add(newStringClas);
                    }
                }
                else
                {
                    FinalStringListlist.Add(stringClas);
                }
            }
            else
            {
                float totalWidth = 0;


                // foreach (char charContent in content)
                {
                    totalWidth += GetTextMeshProCharWidth(content, textComponent);
                }
                if (textComponent.enableAutoSizing)
                {
                    // totalWidth *= (1.0f * textComponent.fontSizeMax / textComponent.fontSize);
                }

                if (currentLineWidth + totalWidth > maxLineWidth)
                {
                    currentLineWidth = totalWidth;
                    LDStringClas newStringClas = new LDStringClas();
                    newStringClas.Content = stringClas.Content;
                    newStringClas.ColorVal = stringClas.ColorVal;
                    newStringClas.PreEnterCalc = true;
                    FinalStringListlist.Add(newStringClas);
                    return true;
                }
                else
                {
                    currentLineWidth += totalWidth;
                    LDStringClas newStringClas = new LDStringClas();
                    newStringClas.Content = stringClas.Content;
                    newStringClas.ColorVal = stringClas.ColorVal;
                    newStringClas.SpaceCalc = stringClas.SpaceCalc;
                    FinalStringListlist.Add(newStringClas);
                }
            }

            return false;
        }

        private static bool CalcText(LDStringClas stringClas, Text textComponent, List<int> indexList, List<LDStringClas> FinalStringListlist,
            ref float currentLineWidth)
        {
            string content = stringClas.Content;
            // 富文本需要优化 

            float maxLineWidth = textComponent.preferredWidth;
            if (textComponent.rectTransform.rect.width > 0)
            {
                maxLineWidth = textComponent.rectTransform.rect.width;
            }

            int slpitIndex = -1;
            if (!stringClas.SpaceCalc)
            {
                foreach (char charContent in content)
                {
                    slpitIndex++;
                    float charWidth = GetCharWidth(charContent.ToString(), textComponent);

                    if (textComponent.resizeTextForBestFit)
                    {
                        // charWidth *= (1.0f * textComponent.resizeTextMaxSize / textComponent.fontSize);
                    }

                    if (currentLineWidth + charWidth > maxLineWidth)
                    {
                        indexList.Add(slpitIndex - 1);
                        currentLineWidth = charWidth;
                    }
                    else
                    {
                        currentLineWidth += charWidth;
                    }
                }

                if (indexList.Count > 0)
                {
                    int subIndex = -1;
                    indexList.Add(content.Length - 1);
                    for (int i = 0; i < indexList.Count; i++)
                    {
                        LDStringClas newStringClas = new LDStringClas();
                        int subLength = indexList[i] - subIndex;
                        newStringClas.Content = stringClas.Content.Substring(subIndex + 1, subLength);
                        newStringClas.ColorVal = stringClas.ColorVal;
                        newStringClas.SpaceCalc = stringClas.SpaceCalc;
                        if (i < indexList.Count - 1)
                        {
                            newStringClas.EnterCalc = true;
                        }

                        subIndex = indexList[i];
                        FinalStringListlist.Add(newStringClas);
                    }
                }
                else
                {
                    FinalStringListlist.Add(stringClas);
                }
            }
            else
            {
                float totalWidth = 0;
                // foreach (char charContent in content)
                {
                    totalWidth += GetCharWidth(content, textComponent);
                }
                if (textComponent.resizeTextForBestFit)
                {
                    // totalWidth *= (1.0f * textComponent.resizeTextMaxSize / textComponent.fontSize);
                }

                if (currentLineWidth + totalWidth > maxLineWidth)
                {
                    currentLineWidth = totalWidth;
                    LDStringClas newStringClas = new LDStringClas();
                    newStringClas.Content = stringClas.Content;
                    newStringClas.ColorVal = stringClas.ColorVal;
                    newStringClas.PreEnterCalc = true;
                    FinalStringListlist.Add(newStringClas);
                    return true;
                }
                else
                {
                    currentLineWidth += totalWidth;
                    LDStringClas newStringClas = new LDStringClas();
                    newStringClas.Content = stringClas.Content;
                    newStringClas.ColorVal = stringClas.ColorVal;
                    newStringClas.SpaceCalc = stringClas.SpaceCalc;
                    FinalStringListlist.Add(newStringClas);
                }
            }

            return false;
        }

        private static string GetText(List<LDStringClas> FinalStringListlist)
        {
            string RealFinalText = string.Empty;
            foreach (LDStringClas stringClas in FinalStringListlist)
            {
                string currentSegment = string.Empty;
                bool isInRTL = false;
                foreach (char c in stringClas.Content)
                {
                    if (IsRTL(c) != isInRTL)
                    {
                        isInRTL = IsRTL(c);
                        // 如果是数字或符号，直接添加到当前段
                        if (currentSegment.Length > 0)
                        {
                            stringClas.Segments.Add(currentSegment);
                            currentSegment = string.Empty;
                        }

                        currentSegment += c;
                    }
                    else
                    {
                        currentSegment += c;
                    }
                }

                // if (stringClas.Segments.Count > 0)
                {
                    stringClas.Segments.Add(currentSegment);
                }
            }

            foreach (LDStringClas stringClas in FinalStringListlist)
            {
                //   反转所有的RTL段 最后一个 字符是空格。能切除空字符
                for (int i = 0; i < stringClas.Segments.Count; i++)
                {
                    if (stringClas.Segments[i].Length > 0)
                    {
                        if (IsRTL(stringClas.Segments[i][0]))
                        {
                            stringClas.Segments[i] = ReverseString(stringClas.Segments[i]);
                        }
                    }
                }
            }

            FinalStringListlist.Reverse();
            string finalText = string.Empty;
            foreach (LDStringClas stringClas in FinalStringListlist)
            {
                finalText += stringClas.GetString();
            }

            RealFinalText += finalText;
            return RealFinalText;
        }

        private static float GetCharWidth(string character, Text text)
        {
            // CharacterInfo characterInfo;
            // if (font.GetCharacterInfo(c, out characterInfo, fontSize, fontStyle))
            // {
            //     return characterInfo.advance;
            // }
            // return 0f;

            // 计算字符的宽度
            TextGenerationSettings settings = text.GetGenerationSettings(text.rectTransform.rect.size);
            float width = TextGen.GetPreferredWidth(character, settings);
            return width;
        }

        private static TextGenerator TextGen = new TextGenerator();

        private static float GetTextMeshProCharWidth(string charContent, TextMeshProUGUI textMeshProUGUI)
        {
            // TMP_TextInfo textInfo = textMeshProUGUI.GetTextInfo(charContent);
            // return textInfo.characterInfo.xAdvance;

            textMeshProUGUI.text = charContent;
            float width = textMeshProUGUI.preferredWidth;
            return width;

            // textMeshProUGUI.font.sourceFontFile.GetCharacterInfo('a', out var charInfo, textMeshProUGUI.fontSize, textMeshProUGUI.fontStyle);
            // return charInfo.advance; // 返回字符的宽度
        }

        private static string ReverseString(string str)
        {
            char[] charArray = str.ToCharArray();
            Array.Reverse(charArray);
            return new string(charArray);
        }
        */


    }
}
