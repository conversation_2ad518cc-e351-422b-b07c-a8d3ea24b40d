using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using System;

/// <summary>
/// 国际化工具
/// </summary>
public class UIInternationUility
{
    public static readonly string __F = "Head";//Tag为(Flip)，图片顶点翻转
    public static readonly string __SF = "SingTower";//Tag为(ScaleFlip)，翻转ScaleX轴
    public static readonly string __D = "Dizz";//Tag为(DynamicUI)，不翻转自身以及子物体
    public static readonly string __U = "Untagged";


    /// <summary>
    /// 翻转UI对象
    /// </summary>
    /// <param name="rect"></param>
    public static void ReversalRect(RectTransform rect)
    {
        FilpRect(rect);
        FilpText(rect);
        FilpSlider(rect);
        FilpLoopHorizontalScrollRect(rect);
        FilpGridLayoutGroup(rect);
        FilpVerticalLayoutGroup(rect);
        FilpHorizontalLayoutGroup(rect);
    }

    public static void FilpRect(RectTransform rect)
    {
        RectTransformUtility.FlipLayoutOnAxis(rect, 0, false, false);
        Vector3 rotate = rect.localRotation.eulerAngles;
        rect.localRotation = Quaternion.Euler(rotate.x, rotate.y, 360 - rotate.z);//翻转旋转

        //特殊处理规则
        if (rect.CompareTag(__SF))
        {
           Vector3 scale = rect.localScale;
           scale.x *= -1;
           rect.localScale = scale;

           // Vector2 anchoreMin = rect.anchorMin;
           // Vector2 anchoreMax = rect.anchorMax;
           // anchoreMin.x = 1-anchoreMin.x;
           // anchoreMax.x = 1-anchoreMax.x;
           // rect.anchorMin = anchoreMin;
           // rect.anchorMax = anchoreMax;

           // 因为scale -1了，anchor正常翻转，pivot保持不变，位置保持不变
           if (rect.pivot.x != 0.5f)
           {
               Vector2 pivot = rect.pivot;
               pivot[0] = 1f - pivot[0];
               rect.pivot = pivot;

               // Vector2 pos = rect.anchoredPosition;
               // Vector2 size = rect.sizeDelta;
               // pos.x += scale.x*size.x;
               // rect.anchoredPosition = pos;
           }
        }
    }

    //翻转文本
    public static void FilpText(RectTransform rect)
    {
        Text txtUI = rect.GetComponent<Text>();
        if (txtUI)
        {
            switch (txtUI.alignment)
            {
                case TextAnchor.UpperLeft: txtUI.alignment = TextAnchor.UpperRight; break;
                case TextAnchor.UpperRight: txtUI.alignment = TextAnchor.UpperLeft; break;
                case TextAnchor.MiddleLeft: txtUI.alignment = TextAnchor.MiddleRight; break;
                case TextAnchor.MiddleRight: txtUI.alignment = TextAnchor.MiddleLeft; break;
                case TextAnchor.LowerLeft: txtUI.alignment = TextAnchor.LowerRight; break;
                case TextAnchor.LowerRight: txtUI.alignment = TextAnchor.LowerLeft; break;
            }
        }
    }

    //翻转滑动条
    public static void FilpSlider(RectTransform rect)
    {
        Slider slider = rect.GetComponent<Slider>();
        if (slider)
        {
            switch (slider.direction)
            {
                case Slider.Direction.LeftToRight:
                    slider.direction = Slider.Direction.RightToLeft; break;
                case Slider.Direction.RightToLeft:
                    slider.direction = Slider.Direction.LeftToRight; break;
            }
        }
    }

    //翻转网格布局
    public static void FilpGridLayoutGroup(RectTransform rect)
    {
        GridLayoutGroup gridLayout = rect.GetComponent<GridLayoutGroup>();
        if (gridLayout)
        {
            switch (gridLayout.startCorner)
            {
                case GridLayoutGroup.Corner.LowerLeft: gridLayout.startCorner = GridLayoutGroup.Corner.LowerRight; break;
                case GridLayoutGroup.Corner.LowerRight: gridLayout.startCorner = GridLayoutGroup.Corner.LowerLeft; break;
                case GridLayoutGroup.Corner.UpperLeft: gridLayout.startCorner = GridLayoutGroup.Corner.UpperRight; break;
                case GridLayoutGroup.Corner.UpperRight: gridLayout.startCorner = GridLayoutGroup.Corner.UpperLeft; break;
            }

            switch (gridLayout.childAlignment)
            {
                case TextAnchor.LowerLeft: gridLayout.childAlignment = TextAnchor.LowerRight; break;
                case TextAnchor.LowerRight: gridLayout.childAlignment = TextAnchor.LowerLeft; break;
                case TextAnchor.UpperLeft: gridLayout.childAlignment = TextAnchor.UpperRight; break;
                case TextAnchor.UpperRight: gridLayout.childAlignment = TextAnchor.UpperLeft; break;
                case TextAnchor.MiddleLeft: gridLayout.childAlignment = TextAnchor.MiddleRight; break;
                case TextAnchor.MiddleRight: gridLayout.childAlignment = TextAnchor.MiddleLeft; break;
            }

            RectOffset offest = gridLayout.padding;
            int left = offest.left;
            offest.left = offest.right;
            offest.right = left;
        }
    }

    //翻转垂直布局
    public static void FilpVerticalLayoutGroup(RectTransform rect)
    {
        VerticalLayoutGroup verticalLayout = rect.GetComponent<VerticalLayoutGroup>();
        if (verticalLayout)
        {
            switch (verticalLayout.childAlignment)
            {
                case TextAnchor.LowerLeft: verticalLayout.childAlignment = TextAnchor.LowerRight; break;
                case TextAnchor.LowerRight: verticalLayout.childAlignment = TextAnchor.LowerLeft; break;
                case TextAnchor.UpperLeft: verticalLayout.childAlignment = TextAnchor.UpperRight; break;
                case TextAnchor.UpperRight: verticalLayout.childAlignment = TextAnchor.UpperLeft; break;
                case TextAnchor.MiddleLeft: verticalLayout.childAlignment = TextAnchor.MiddleRight; break;
                case TextAnchor.MiddleRight: verticalLayout.childAlignment = TextAnchor.MiddleLeft; break;
            }
            //内边距
            RectOffset offset = verticalLayout.padding;
            int temp = offset.left;
            offset.left = offset.right;
            offset.right = temp;
            verticalLayout.padding = offset;
        }
    }

    public static void FilpLoopHorizontalScrollRect(RectTransform rect)
    {
        //todo 临时注释 wxb
        // LoopHorizontalScrollRect loopHorizontalScrollRect = rect.GetComponent<LoopHorizontalScrollRect>();
        // if(loopHorizontalScrollRect)
        // {
        //     loopHorizontalScrollRect.reverseDirection = !loopHorizontalScrollRect.reverseDirection;
        //     
        //     HorizontalLayoutGroup horizontalLayout = loopHorizontalScrollRect.content.GetComponent<HorizontalLayoutGroup>();
        //     if (horizontalLayout)
        //     {
        //         horizontalLayout.reverseArrangement = false;
        //     }
        // }
    }

    //翻转水平布局
    public static void FilpHorizontalLayoutGroup(RectTransform rect)
    {
        HorizontalLayoutGroup horizontalLayout = rect.GetComponent<HorizontalLayoutGroup>();
        if (horizontalLayout)
        {
            switch (horizontalLayout.childAlignment)
            {
                case TextAnchor.LowerLeft: horizontalLayout.childAlignment = TextAnchor.LowerRight; break;
                case TextAnchor.LowerRight: horizontalLayout.childAlignment = TextAnchor.LowerLeft; break;
                case TextAnchor.UpperLeft: horizontalLayout.childAlignment = TextAnchor.UpperRight; break;
                case TextAnchor.UpperRight: horizontalLayout.childAlignment = TextAnchor.UpperLeft; break;
                case TextAnchor.MiddleLeft: horizontalLayout.childAlignment = TextAnchor.MiddleRight; break;
                case TextAnchor.MiddleRight: horizontalLayout.childAlignment = TextAnchor.MiddleLeft; break;
            }

            
            //内边距
            if (!rect.CompareTag(__F))
            {
                RectOffset offset = horizontalLayout.padding;
                int temp = offset.left;
                offset.left = offset.right;
                offset.right = temp;
                horizontalLayout.padding = offset;
            }

            horizontalLayout.reverseArrangement = true;

            // //翻转索引位置
            // Transform mTran = horizontalLayout.transform;
            // if (mTran.childCount > 1)
            // {
            //     // 翻转起点层级偏移值
            //     int beginOffset = 0;
            //     List<Transform> trans = new List<Transform>();
            //     for (int i = 0, len = mTran.childCount; i < len; i++)
            //     {
            //         // 检测0号位置是否为不可翻转
            //         if(i == 0 && mTran.GetChild(i).CompareTag(__D))
            //             beginOffset = 1;
            //         // 检测末尾位置是否为不可翻转
            //         else if(i != len - 1 || !mTran.GetChild(i).CompareTag(__D))
            //             trans.Add(mTran.GetChild(i));
            //     }
            //     for (int i = 0, len = trans.Count; i < len; i++)
            //     {
            //         trans[i].SetSiblingIndex(len - i - 1 + beginOffset);
            //     }
            // }
        }
    }

    public static void FilpUILineFocusScroll(RectTransform rect)
    {
        //todo 临时注释 wxb
        // UILineFocusScroll scroll = rect.GetComponent<UILineFocusScroll>();
        // if (scroll)
        // {
        //     scroll.Clockwise = !scroll.Clockwise;
        // }
    }

    public static UIHierarchy GetExternals(Transform rectTran)
    {
        Func<Transform, UIHierarchy> func = null;
        func = rect =>
        {
            UIHierarchy uiHie = rect.GetComponent<UIHierarchy>();
            //todo:临时修改wxb
            // if (uiHie != null && uiHie.externals.Count > 0)
            if (uiHie != null && uiHie.externals != null && uiHie.externals.Count > 0)
                return uiHie;
            return rect.parent == null ? null : func(rect.parent);
        };
        return func(rectTran);
    }
}
