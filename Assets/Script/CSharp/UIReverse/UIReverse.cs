using System;
using System.Collections.Generic;
using LD;
using UnityEngine;
using UnityEngine.UI;

public class UIReverse : MonoBehaviour
{
    public bool ignore = false;
    public bool includeChild = true;
    public ReverseComponent RevComponent;
    private RectTransform rect;

    void Awake()
    {
        // Debug.Log("ReverUI:"+name);
        if (!ignore && Extension.RTL) FilpUI();
    }

    void FilpUI()
    {
        rect = GetComponent<RectTransform>();
        if (rect == null) return;
        if (includeChild)
        {
            ReversalChildrenUI(rect);
        }
        else
        {
            UIInternationUility.FilpRect(rect);
            FilpComponent(ReverseComponent.Text, UIInternationUility.FilpText);
            FilpComponent(ReverseComponent.Slider, UIInternationUility.FilpSlider);
            FilpComponent(ReverseComponent.LoopHorizontalScrollRect, UIInternationUility.FilpLoopHorizontalScrollRect);
            FilpComponent(ReverseComponent.GridLayoutGroup, UIInternationUility.FilpGridLayoutGroup);
            FilpComponent(ReverseComponent.VerticalLayoutGroup, UIInternationUility.FilpVerticalLayoutGroup);
            FilpComponent(ReverseComponent.HorizontalLayoutGroup, UIInternationUility.FilpHorizontalLayoutGroup);
            FilpComponent(ReverseComponent.HorizontalLayoutGroup, UIInternationUility.FilpUILineFocusScroll);
        }

        LayoutRebuilder.ForceRebuildLayoutImmediate(rect);
    }

    private void ReversalChildrenUI(RectTransform rect)
    {
        if (!IsFlip(rect)) return;
        // Debug.Log("ReversalChildrenUI:"+rect.name);
        //递归翻转
        for (int i = 0, len = rect.childCount; i < len; i++)
        {
            var child = rect.GetChild(i);
            if (child.GetComponent<UIReverse>() == null && child.GetComponent<UIHierarchy>() == null)
            {
                RectTransform childRt = rect.GetChild(i).GetComponent<RectTransform>();
                if (IsFlip(childRt)) ReversalChildrenUI(childRt);
            }
        }

        //执行翻转操作
        if (IsFlip(rect))
        {
            ReversalRect(rect);
        }
    }

    private void ReversalRect(RectTransform rect)
    {
        UIInternationUility.FilpRect(rect);
        UIInternationUility.FilpText(rect);
        UIInternationUility.FilpSlider(rect);
        UIInternationUility.FilpGridLayoutGroup(rect);
        UIInternationUility.FilpVerticalLayoutGroup(rect);
        UIInternationUility.FilpHorizontalLayoutGroup(rect);
        UIInternationUility.FilpLoopHorizontalScrollRect(rect);
        UIInternationUility.FilpUILineFocusScroll(rect);
    }

    private bool IsFlip(RectTransform rect)
    {
        return rect != null && !rect.CompareTag(UIInternationUility.__D);
    }

    //翻转组件
    private void FilpComponent(ReverseComponent cmp, Action<RectTransform> action)
    {
        if ((RevComponent & cmp) == cmp) action(rect);
    }

#if UNITY_EDITOR
    static Dictionary<Type, ReverseComponent> TypesDic;

    public void Init()
    {
        if (TypesDic == null)
        {
            TypesDic = new Dictionary<Type, ReverseComponent>();
            TypesDic.Add(typeof(Text), ReverseComponent.Text);
            TypesDic.Add(typeof(Image), ReverseComponent.Image);
            TypesDic.Add(typeof(Slider), ReverseComponent.Slider);
            TypesDic.Add(typeof(GridLayoutGroup), ReverseComponent.GridLayoutGroup);
            TypesDic.Add(typeof(VerticalLayoutGroup), ReverseComponent.VerticalLayoutGroup);
            TypesDic.Add(typeof(HorizontalLayoutGroup), ReverseComponent.HorizontalLayoutGroup);
        }

        //选中对应组件
        rect = GetComponent<RectTransform>();
        RevComponent = 0;
        RevComponent = RevComponent | HasComponent<Text>();
        RevComponent = RevComponent | HasComponent<Image>();
        RevComponent = RevComponent | HasComponent<Slider>();
        RevComponent = RevComponent | HasComponent<GridLayoutGroup>();
        RevComponent = RevComponent | HasComponent<VerticalLayoutGroup>();
        RevComponent = RevComponent | HasComponent<HorizontalLayoutGroup>();
    }

    private ReverseComponent HasComponent<T>()
    {
        T component = rect.GetComponent<T>();
        return component == null ? 0 : TypesDic[typeof(T)];
    }
#endif
}

public enum ReverseComponent
{
    OnlyRectTransform = 0,
    Text = 1,
    Image = 1 << 1,
    Slider = 1 << 2,
    GridLayoutGroup = 1 << 3,
    VerticalLayoutGroup = 1 << 4,
    HorizontalLayoutGroup = 1 << 5,
    LoopHorizontalScrollRect = 1 << 6,
}