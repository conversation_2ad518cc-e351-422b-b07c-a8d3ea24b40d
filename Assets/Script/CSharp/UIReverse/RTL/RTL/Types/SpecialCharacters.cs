namespace RTLTMPro
{
    /// <summary>
    /// Special characters used in Arabis / Persian / Hebrew
    /// </summary>
    public enum SpecialCharacters
    {   
        ZeroWidthNoJoiner = 0x200C,
        
        LRE = 0x202A, // LRE	U+202A	LEFT-TO-RIGHT EMBEDDING	Treat the following text as embedded left-to-right.
        RLE = 0x202B, // RLE	U+202B	RIGHT-TO-LEFT EMBEDDING	Treat the following text as embedded right-to-left.
        PDF = 0x202C, // PDF	U+202C	POP DIRECTIONAL FORMATTING	End the scope of the last LRE, RLE, RLO, or LRO.
        LRO = 0x202D, // LRO	U+202D	LEFT-TO-RIGHT OVERRIDE	Force following characters to be treated as strong left-to-right characters.
        RLO = 0x202E, // RLO	U+202E	RIGHT-TO-LEFT OVERRIDE	Force following characters to be treated as strong right-to-left characters.
        LRI = 0x2066, // LRI	U+2066	LEFT‑TO‑RIGHT ISOLATE	Treat the following text as isolated and left-to-right.
        RLI = 0x2067, // RLI	U+2067	RIGHT‑TO‑LEFT ISOLATE	Treat the following text as isolated and right-to-left.
        FSI = 0x2068, // FSI	U+2068	FIRST STRONG ISOLATE	Treat the following text as isolated and in the direction of its first strong directional character that is not inside a nested isolate.
        PDI = 0x2069, // PDI	U+2069	POP DIRECTIONAL ISOLATE	End the scope of the last LRI, RLI, or FSI.
        LRM = 0x200E, // LRM	U+200E	LEFT-TO-RIGHT MARK	Left-to-right zero-width character
        RLM = 0x200F, // RLM	U+200F	RIGHT-TO-LEFT MARK	Right-to-left zero-width non-Arabic character
        ALM = 0x061C, // ALM	U+061C	ARABIC LETTER MARK	Right-to-left zero-width Arabic character
    }
}