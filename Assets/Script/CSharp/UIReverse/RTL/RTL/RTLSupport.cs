using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using UnityEngine;

namespace RTLTMPro
{
    public static class RTLSupport
    {
        public const int DefaultBufferSize = 2048;

        private static FastStringBuilder inputBuilder;
        private static FastStringBuilder glyphFixerOutput;
        
        // protected readonly FastStringBuilder finalText = new FastStringBuilder(RTLSupport.DefaultBufferSize);

        static RTLSupport()
        {
            inputBuilder = new FastStringBuilder(DefaultBufferSize);
            glyphFixerOutput = new FastStringBuilder(DefaultBufferSize);
        }

        /// <summary>
        ///     Fixes the provided string
        /// </summary>
        /// <param name="input">Text to fix</param>
        /// <param name="output">Fixed text</param>
        /// <param name="fixTextTags"></param>
        /// <param name="preserveNumbers"></param>
        /// <param name="farsi"></param>
        /// <returns>Fixed text</returns>
        public static void FixRTL(
            string input,
            FastStringBuilder output,
            bool farsi = true,
            bool fixTextTags = true,
            bool preserveNumbers = false)
        {
            inputBuilder.SetValue(input);
            TashkeelFixer.RemoveTashkeel(inputBuilder);
            // The shape of the letters in shapeFixedLetters is fixed according to their position in word. But the flow of the text is not fixed.
            GlyphFixer.Fix(inputBuilder, glyphFixerOutput, preserveNumbers, farsi, fixTextTags);
            //Restore tashkeel to their places.
            TashkeelFixer.RestoreTashkeel(glyphFixerOutput);

            TashkeelFixer.FixShaddaCombinations(glyphFixerOutput);
            // Fix flow of the text and put the result in FinalLetters field
            LigatureFixer.Fix(glyphFixerOutput, output, farsi, fixTextTags, preserveNumbers);
            if (fixTextTags)
            {
                RichTextFixer.Fix(output);
            }

            inputBuilder.Clear();
        }

        private static void DumpString(string title, string input)
        {
            var sb = new StringBuilder();
            for (int i = 0; i < input.Length; i++)
            {
                int unicode32CodePoint = char.ConvertToUtf32(input, i);
                sb.Append(char.ConvertFromUtf32(unicode32CodePoint))
                    // .Append($" [{unicode32CodePoint:X4}] ")
                    ;
            }

            UnityEngine.Debug.Log($"{title}: {sb.ToString()}");
        }

        /// <summary>
        ///     Fixes the provided string
        /// </summary>
        /// <param name="input">Text to fix</param>
        /// <param name="output">Fixed text</param>
        /// <param name="fixTextTags"></param>
        /// <param name="preserveNumbers"></param>
        /// <param name="farsi"></param>
        /// <returns>Fixed text</returns>
        public static string ToVisualString(
            string input,
            bool farsi = true,
            bool fixTextTags = true,
            bool preserveNumbers = false,
            bool isReverse = false)
        {
            inputBuilder.SetValue(input);
            if (fixTextTags)
            {
                RichTextFixer.FixWithExplicitDirectionalIsolatesEx(inputBuilder);
                // else
                // RichTextFixer.FixWithExplicitDirectionalIsolates(inputBuilder);
            }
            Bidi.LogicalToVisual(inputBuilder);
            if (fixTextTags)
            {
                RichTextFixer.InsertTags(inputBuilder);
            }
#if UNITY_EDITOR
            string visualOutput = inputBuilder.ToString();
#endif
            inputBuilder.Reverse();
            // Debug.Log(inputBuilder.ToString());
            TashkeelFixer.RemoveTashkeel(inputBuilder);
            // The shape of the letters in shapeFixedLetters is fixed according to their position in word. But the flow of the text is not fixed.
            GlyphFixer.Fix(inputBuilder, glyphFixerOutput, preserveNumbers, farsi, fixTextTags);
            //Restore tashkeel to their places.
            TashkeelFixer.RestoreTashkeel(glyphFixerOutput);

            TashkeelFixer.FixShaddaCombinations(glyphFixerOutput);
            var sb = new StringBuilder();
            if (isReverse)
            {
                for (int i = glyphFixerOutput.Length -1; i >= 0; i--)
                {
                    int unicode32CodePoint = glyphFixerOutput.Get(i);
                    if (unicode32CodePoint != 0xFFFF &&
                        unicode32CodePoint != (int)SpecialCharacters.ZeroWidthNoJoiner)
                    {
                        sb.Append(char.ConvertFromUtf32(unicode32CodePoint));
                    }
                }
            }
            else
            {
                for (int i = 0; i <= glyphFixerOutput.Length -1; i++)
                {
                    int unicode32CodePoint = glyphFixerOutput.Get(i);
                    if (unicode32CodePoint != 0xFFFF &&
                        unicode32CodePoint != (int)SpecialCharacters.ZeroWidthNoJoiner)
                    {
                        sb.Append(char.ConvertFromUtf32(unicode32CodePoint));
                    }
                }
            }
// #if UNITY_EDITOR
//             string glyphFixerOutputText = glyphFixerOutput.ToString();
//             DumpString("input", input);
//             DumpString("visualOutput", visualOutput);
//             DumpString("glyphFixerOutputText", glyphFixerOutputText);
// #endif
            return sb.ToString();
        }

        //是否有阿语特殊标记
        public static bool IsHaveArabicSpecialCharacters(string text)
        {
            for (int i = 0; i < text.Length; i++)
            {
                if (RichTextFixer.VisualIgnoreSet.Contains(text[i]))
                {
                    return true;
                }
            }

            return false;
        }

        public static string getFixTextTagsText(string text)
        {
            if (string.IsNullOrEmpty(text))
            {
                return "";
            }
            inputBuilder.SetValue(text);
            RichTextFixer.FixWithExplicitDirectionalIsolatesEx(inputBuilder);
            return inputBuilder.ToString();
        }
    }
}