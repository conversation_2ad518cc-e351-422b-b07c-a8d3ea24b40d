using UnityEngine;
using System.Collections;
using System.Collections.Generic;

public class UIHierarchy : MonoBehaviour
{
    public bool NeedReverse = true;  // 是否需要翻转  Spine 不翻转
    [System.Serializable]
    public class ItemInfo
    {
        public string name;
        public Object item;

        public ItemInfo() { }
        public ItemInfo(string _name, Object _item) { name = _name; item = _item; }
    }

    [System.Serializable]
    public class EffectItemInfo
    {
        public string name;
        public Object item;
        public Object parent;
        public EffectItemInfo() { }
        public EffectItemInfo(string _name, Object _item, Object _parent)
        {
            name = _name;
            item = _item;
            parent = _parent;
        }
    }

    // 控件
    public List<ItemInfo> widgets;
    public void SetWidgets(List<ItemInfo> data)
    {
        if (data.Count == 0) return;
        if (widgets == null)
        {
            widgets = new List<ItemInfo>();
        }

        widgets.Clear();
        widgets.AddRange(data);
    }

    // 特效控件
    public List<EffectItemInfo> effects;

    public void SetEffects(List<EffectItemInfo> data)
    {
        if (data.Count == 0) return;
        if (effects == null)
        {
            effects = new List<EffectItemInfo>();
        }
        effects.Clear();
        effects.AddRange(data);
    }

    // 外部引用
    public List<ItemInfo> externals;

#if UNITY_EDITOR
    void SavePrefabs()
    {
        var prefab = UnityEditor.PrefabUtility.GetPrefabParent(gameObject);
        if(prefab)
        {
            UnityEditor.PrefabUtility.ReplacePrefab(gameObject, prefab, UnityEditor.ReplacePrefabOptions.ConnectToPrefab);
        }
        else
        {
            Debug.LogError("没有找到指定预制" + gameObject.name);
        }
    }

    [ContextMenu("设置effects的特效Name")]
    void SetEffectItemName()
    {
        foreach(EffectItemInfo item in effects)
        {
            item.name = item.item.name;
            Debug.Log(item.name);
        }
        Debug.Log("设置特效名字完成！！");

        foreach(ItemInfo item in externals)
        {
            item.name = item.item.name;
            Debug.Log(item.name);
        }
        Debug.Log("设置externals名字完成！！");

        SavePrefabs();
    }
#endif
    void Awake()
    {
        if(NeedReverse)
        {
            UIReverse uir = GetComponent<UIReverse>();
            if(uir == null)
            {
                uir = gameObject.AddComponent<UIReverse>();
                uir.includeChild = true;
            }
        }
    }

}