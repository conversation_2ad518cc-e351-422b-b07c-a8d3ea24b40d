using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class AircraftReforgeListUI_CurrentAttr : LDBaseResUI {

		[SerializeField]
		private RectTransform_Image_Container m_quality_bg;
		public RectTransform_Image_Container quality_bg { get { return m_quality_bg; } }

		[SerializeField]
		private RectTransform_Text_Container m_quality_name;
		public RectTransform_Text_Container quality_name { get { return m_quality_name; } }

		[SerializeField]
		private RectTransform_Image_Container m_quality_Dec;
		public RectTransform_Image_Container quality_Dec { get { return m_quality_Dec; } }

		[SerializeField]
		private RectTransform_Text_Container m_Num;
		public RectTransform_Text_Container Num { get { return m_Num; } }

		[SerializeField]
		private RectTransform_Container m_nex;
		public RectTransform_Container nex { get { return m_nex; } }

		[SerializeField]
		private RectTransform_Text_Container m_nextNum;
		public RectTransform_Text_Container nextNum { get { return m_nextNum; } }

		[SerializeField]
		private RectTransform_Image_Container m_up;
		public RectTransform_Image_Container up { get { return m_up; } }

	}

}
