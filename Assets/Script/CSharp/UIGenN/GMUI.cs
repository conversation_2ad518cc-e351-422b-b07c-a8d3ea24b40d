using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class GMUI : LDBaseUI {

		[SerializeField]
		private RectTransform_Text_Container m_ServerName;
		public RectTransform_Text_Container ServerName { get { return m_ServerName; } }

		[SerializeField]
		private RectTransform_Text_Container m_ServerTime;
		public RectTransform_Text_Container ServerTime { get { return m_ServerTime; } }

		[SerializeField]
		private RectTransform_Text_Container m_ServerOpenTimeDesc;
		public RectTransform_Text_Container ServerOpenTimeDesc { get { return m_ServerOpenTimeDesc; } }

		[SerializeField]
		private RectTransform_Text_Container m_ServerOpenTime;
		public RectTransform_Text_Container ServerOpenTime { get { return m_ServerOpenTime; } }

		[SerializeField]
		private RectTransform_InputField_Image_Container m_InputField;
		public RectTransform_InputField_Image_Container InputField { get { return m_InputField; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_SendBtn;
		public RectTransform_Button_Image_Container SendBtn { get { return m_SendBtn; } }

		[SerializeField]
		private RectTransform_Container m_CheckClose;
		public RectTransform_Container CheckClose { get { return m_CheckClose; } }

		[SerializeField]
		private RectTransform_Toggle_Container m_SendCloseToggle;
		public RectTransform_Toggle_Container SendCloseToggle { get { return m_SendCloseToggle; } }

		[SerializeField]
		private RectTransform_InputField_Image_Container m_InputFieldSearch;
		public RectTransform_InputField_Image_Container InputFieldSearch { get { return m_InputFieldSearch; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_HelpBtn;
		public RectTransform_Button_Image_Container HelpBtn { get { return m_HelpBtn; } }

		[SerializeField]
		private RectTransform_GMUI_Item_Container m_Item;
		public RectTransform_GMUI_Item_Container Item { get { return m_Item; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Close;
		public RectTransform_Button_Image_Container Close { get { return m_Close; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_TestBtn;
		public RectTransform_Button_Image_Container TestBtn { get { return m_TestBtn; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_AddItemBtn;
		public RectTransform_Button_Image_Container AddItemBtn { get { return m_AddItemBtn; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_ExcelCheck;
		public RectTransform_Button_Image_Container ExcelCheck { get { return m_ExcelCheck; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_pveTest;
		public RectTransform_Button_Image_Container pveTest { get { return m_pveTest; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_pveTestDisplay;
		public RectTransform_Button_Image_Container pveTestDisplay { get { return m_pveTestDisplay; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_testWindow1;
		public RectTransform_Button_Image_Container testWindow1 { get { return m_testWindow1; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_testWindow2;
		public RectTransform_Button_Image_Container testWindow2 { get { return m_testWindow2; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_LanguageBtn;
		public RectTransform_Button_Image_Container LanguageBtn { get { return m_LanguageBtn; } }

		[SerializeField]
		private RectTransform_InputField_Image_Container m_InputFieldLanguage;
		public RectTransform_InputField_Image_Container InputFieldLanguage { get { return m_InputFieldLanguage; } }

		[SerializeField]
		private RectTransform_Image_Container m_DropdownLanguage;
		public RectTransform_Image_Container DropdownLanguage { get { return m_DropdownLanguage; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_EnterGame;
		public RectTransform_Button_Image_Container EnterGame { get { return m_EnterGame; } }

		[SerializeField]
		private RectTransform_InputField_Image_Container m_InputFieldPass;
		public RectTransform_InputField_Image_Container InputFieldPass { get { return m_InputFieldPass; } }

		[System.Serializable]
		public class RectTransform_GMUI_Item_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private GMUI_Item m_Item;
			public GMUI_Item Item { get { return m_Item; } }

			[System.NonSerialized] public List<GMUI_Item> mCachedList = new List<GMUI_Item>();
			private Queue<GMUI_Item> mCachedInstances;
			public GMUI_Item GetInstance(bool ignoreSibling = false) {
				GMUI_Item instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<GMUI_Item>(m_Item);
					instance.ItemInit();
				}
				Transform t0 = m_Item.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(GMUI_Item instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<GMUI_Item>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<GMUI_Item> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
