using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class AircraftPowerDown_MessageBoxUI : LDBaseUI {

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_close;
		public RectTransform_Button_Image_Container btn_close { get { return m_btn_close; } }

		[SerializeField]
		private RectTransform_Text_Container m_ContentTxt;
		public RectTransform_Text_Container ContentTxt { get { return m_ContentTxt; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Green_btn_Cancel;
		public RectTransform_Button_Image_Container Green_btn_Cancel { get { return m_Green_btn_Cancel; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Orange_btn_Confirm;
		public RectTransform_Button_Image_Container Orange_btn_Confirm { get { return m_Orange_btn_Confirm; } }

	}

}
