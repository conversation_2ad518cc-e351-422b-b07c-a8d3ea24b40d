using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD
{
    public partial class AircraftMainUI_XiLian_UI
    {
        private int m_CurShowIndex = -1;
        private LDNetAircraftInfo m_CurShowAircraft;
        private bool m_ShowOriginalAttr = false;

        private Action<int> m_OnAircraftSelectCallback;

        private List<int> m_AircraftLockIndexs = new();
        private List<AircraftMainUI_XiLian_UI_Effect> m_EffectItems = new();

        private CommonModelUI m_CommonModel;

        private List<AircraftMainUI_XiLian_UI_XiLianAircraftItem> m_ItemTabs = new();

        public void Init(GameObject commonModelUI)
        {
            m_CurrentBtn.AddListener(ChangeToCurrentAttr);
            m_OriginallyBtn.AddListener(ChangeToOriginalAttr);
            m_Help_BTN.AddListener(OnHelpClick);
            m_Reforge.AddListener(OnXilianClick);
            m_ReforgeLock.AddListener(OnXilianClick);
            m_Orange_btn_Confirm.AddListener(OnChangeClick);

            m_CommonModel = commonModelUI.gameObject.GetComponent<CommonModelUI>();
            m_CommonModel.SetCameraPos(new Vector3(0, 17.45f, -27.8f));
            m_CommonModel.SetCameraFOV(60);
            m_CommonModel.SetCameraRot(new UnityEngine.Vector3(30, 0, 0));
        }

        public void RefreshView(Action<int> selectSlot, int slotIndex)
        {
            m_CurShowIndex = slotIndex;
            m_OnAircraftSelectCallback = selectSlot;
            RefreshTab();
            ChangeTab();
            ShowAircraftView();

            TryGuide();
        }

        private void TryGuide()
        {
            if (Global.gApp.gSystemMgr.gGuideMgr.HasGuide())
            {
                if (m_CurShowAircraft.AircraftRefinementInfo.RefinementEffects.Count <= 0 && m_ShowOriginalAttr)
                {
                    if (Global.gApp.gSystemMgr.gGuideMgr.AddGuide(LDGuideType.Aircraft, m_ReforgeBtnNode.rectTransform, 3))
                    {
                        return;
                    }
                }
                else
                {
                    Global.gApp.gSystemMgr.gGuideMgr.JumpCurStep(LDGuideType.Aircraft, 3);
                }
            }

            if (Global.gApp.gSystemMgr.gGuideMgr.AddGuide(LDGuideType.Aircraft, m_Orange_btn_Confirm.rectTransform, 4))
            {
                return;
            }
        }

        public void RefreshCallBack(int val)
        {
            if (val == 201)
            {
                RefreshLevelInfo();
                RefreshEffect();
                if (m_ShowOriginalAttr)
                {
                    ChangeToCurrentAttr();
                }

                RefreshXiLianBtn();
                ShowXiLianEffect();
            }
            else if (val == 202)
            {
                RefreshLevelInfo();
                RefreshEffect();
                ChangeToOriginalAttr();
                RefreshXiLianBtn();
            }
            else if (val == 101)
            {
                RefreshTab();
            }
            else if (val == 103)
            {
                RefreshTab();
                ShowAircraftView();
            }
        }

        private void ShowAircraftView()
        {
            m_CurShowAircraft = Global.gApp.gSystemMgr.gAircraftMgr.GetAircraftByIndex(m_CurShowIndex);
            if (m_CurShowAircraft == null)
            {
                return;
            }

            if (m_CurShowAircraft.AircraftRefinementInfo.RefinementEffects.Count > 0)
            {
                m_ShowOriginalAttr = false;
            }
            else
            {
                m_ShowOriginalAttr = true;
            }


            LDUIPrefabTools.InitNameNodeForAircraft(m_NameNode.gameObject, m_CurShowAircraft.AircraftId, m_CurShowAircraft.Quality);
            ChangeBtnStatus();
            RefreshEffect();
            RefreshLevelInfo();
            RefreshModelNode();
        }

        // 模型
        private void RefreshModelNode()
        {
            AircraftCfgItem aircraftCfg = AircraftCfg.Data.Get(m_CurShowAircraft.AircraftId);
            GameObject model = m_CommonModel.InitModel(aircraftCfg.prefab, m_ModelImage.rawImage, 1);
            m_CommonModel.SetRootPosX(-2100);
            model.transform.localEulerAngles = new Vector3(aircraftCfg.mechaXYZ[0], aircraftCfg.mechaXYZ[1], aircraftCfg.mechaXYZ[2]);
            // model.transform.localEulerAngles = new Vector3(aircraftCfg.mechaXYZ[0], m_MechaCfg.mechaXYZ[1], m_MechaCfg.mechaXYZ[2]);
            model.transform.localScale = Vector3.one * 1f; //m_MechaCfg.mechaScale;
        }

        private void RefreshEffect()
        {
            List<LDNetAircraftEffect> effects;
            if (m_ShowOriginalAttr)
            {
                effects = m_CurShowAircraft.AircraftRefinementInfo.Effects;
            }
            else
            {
                effects = m_CurShowAircraft.AircraftRefinementInfo.RefinementEffects;
            }

            m_Effect.CacheInstanceList();
            m_EffectItems.Clear();
            string lockEffect = Global.gApp.gSystemMgr.gNetClientDataMgr.GetGeneralClientDataValue(GeneralClientDataKey.AircraftLockEffect + "_" + m_CurShowAircraft.AircraftId);
            List<int> lockEffectList = new List<int>();
            if (!string.IsNullOrEmpty(lockEffect))
            {
                string[] lockEffects = lockEffect.Split(',');
                foreach (string effectId in lockEffects)
                {
                    if (LDParseTools.TryIntParse(effectId, out int id))
                    {
                        lockEffectList.Add(id);
                    }
                }
            }

            m_AircraftLockIndexs = lockEffectList;
            AircraftReforgeBaseItem cfg = AircraftReforgeBase.Data.Get(Global.gApp.gSystemMgr.gAircraftMgr.Data.RefinementLevel);
            int maxLockCount = cfg.lockNum;

            for (int i = 0; i < 5; i++)
            {
                AircraftMainUI_XiLian_UI_Effect itemUI = m_Effect.GetInstance(true);
                if (effects.Count > i)
                {
                    LDNetAircraftEffect effect = effects[i];
                    itemUI.RefreshEffect(effect, maxLockCount > 0, i, m_ShowOriginalAttr, m_ShowOriginalAttr ? lockEffectList.Contains(i) : effect.IsLock, LockEffect);
                }
                else
                {
                    itemUI.RefreshLockEffect(i, m_ShowOriginalAttr);
                }

                m_EffectItems.Add(itemUI);
            }

            RefreshXiLianBtn();
            RefreshXiLianPower();
        }

        private void RefreshXiLianBtn()
        {
            m_ReforgeBtnNode.gameObject.SetActive(m_CurShowAircraft != null && m_AircraftLockIndexs.Count <= 0);
            m_ReforgeLockNode.gameObject.SetActive(m_CurShowAircraft != null && m_AircraftLockIndexs.Count > 0);
            m_Button_ConfirmNode.gameObject.SetActive(m_CurShowAircraft != null && m_CurShowAircraft.AircraftRefinementInfo.RefinementEffects.Count > 0);

            AircraftReforgeBaseItem cfg = AircraftReforgeBase.Data.Get(Global.gApp.gSystemMgr.gAircraftMgr.Data.RefinementLevel);
            LDCommonItem reforgeCost = new LDCommonItem(cfg.reforgeItem);
            if (m_AircraftLockIndexs.Count > 0)
            {
                int lockCount = m_AircraftLockIndexs.Count;
                AircraftReforgeLockItem lockCfg = AircraftReforgeLock.Data.Get(lockCount);
                LDCommonItem lockItem = new LDCommonItem(lockCfg.lockItem);

                // m_LockNum_01.text.SetText(lockItem.Num);
                m_LockNum_01.text.SetCostText(lockItem.CurNum, lockItem.Num);
                // m_ReforgeNum_01.text.SetText(reforgeCost.Num);
                m_ReforgeNum_01.text.SetCostText(reforgeCost.CurNum, reforgeCost.Num);
                LoadSprite(m_Currency_Dec1.image, reforgeCost.Icon);
                LoadSprite(m_Currency_Dec2.image, lockItem.Icon);
            }
            else
            {
                // m_ReforgeNum.text.SetText(reforgeCost.Num);
                m_ReforgeNum.text.SetCostText(reforgeCost.CurNum, reforgeCost.Num);
                LoadSprite(m_Currency_Dec.image, reforgeCost.Icon);
            }
        }

        private void ShowXiLianEffect()
        {
            foreach (AircraftMainUI_XiLian_UI_Effect effect in m_EffectItems)
            {
                effect.OnShowEffect();
            }
        }

        private void RefreshXiLianPower()
        {
            m_Power.gameObject.SetActive(!m_ShowOriginalAttr);
            if (!m_ShowOriginalAttr)
            {
                List<LDNetAircraftEffect> otherAircraftEffects = Global.gApp.gSystemMgr.gAircraftMgr.GetOtherSlotAircraftEffects(m_CurShowIndex);
                double defaultPower = m_CurShowAircraft.AircraftRefinementInfo.GetDefaultPower(otherAircraftEffects);
                double refinementPower = m_CurShowAircraft.AircraftRefinementInfo.GetRefinementPower(otherAircraftEffects);
                if (refinementPower > defaultPower)
                {
                    m_Power_Up.gameObject.SetActive(true);
                    m_Power_Down.gameObject.SetActive(false);
                    m_Power_Up_Txt.text.SetText(UiTools.FormateMoney(Mathf.Floor((float)(refinementPower - defaultPower))).ToString());
                    LayoutRebuilder.ForceRebuildLayoutImmediate(m_Power_Up.rectTransform);
                }
                else
                {
                    m_Power_Up.gameObject.SetActive(false);
                    m_Power_Down.gameObject.SetActive(true);
                    m_Power_Down_Txt.text.SetText("-" + UiTools.FormateMoney(Mathf.Floor((float)(defaultPower - refinementPower))).ToString());
                    LayoutRebuilder.ForceRebuildLayoutImmediate(m_Power_Down.rectTransform);
                }
            }
        }

        private bool LockEffect(int index, bool isAdd)
        {
            if (isAdd)
            {
                AircraftReforgeBaseItem cfg = AircraftReforgeBase.Data.Get(Global.gApp.gSystemMgr.gAircraftMgr.Data.RefinementLevel);
                int maxLockCount = cfg.lockNum;
                if (m_AircraftLockIndexs.Count >= maxLockCount)
                {
                    Global.gApp.gToastMgr.ShowGameTips(40530, maxLockCount.ToString());
                    return false;
                }


                m_AircraftLockIndexs.Add(index);
            }
            else
            {
                if (m_AircraftLockIndexs.Contains(index))
                {
                    m_AircraftLockIndexs.Remove(index);
                }
            }

            string saveStr = "";
            foreach (int id in m_AircraftLockIndexs)
            {
                saveStr += id + ",";
            }

            Global.gApp.gSystemMgr.gNetClientDataMgr.ChangeGenerateClientData(GeneralClientDataKey.AircraftLockEffect + "_" + m_CurShowAircraft.AircraftId, saveStr);

            RefreshXiLianBtn();
            return true;
        }

        private void RefreshLevelInfo()
        {
            int level = Global.gApp.gSystemMgr.gAircraftMgr.Data.RefinementLevel;
            m_curLv.text.SetTips(40524, level);
            AircraftReforgeBase.Data.TryGet(level + 1, out AircraftReforgeBaseItem nextCfg, false);
            if (nextCfg == null)
            {
                m_lvmax.gameObject.SetActive(true);
                m_lv.gameObject.SetActive(false);
                m_progress.slider.value = 1.0f;
            }
            else
            {
                m_lvmax.gameObject.SetActive(false);
                m_lv.gameObject.SetActive(true);
                m_lv.text.SetText($"{Global.gApp.gSystemMgr.gAircraftMgr.Data.RefinementExp}/{nextCfg.manufactureExp}");
                m_progress.slider.value = Global.gApp.gSystemMgr.gAircraftMgr.Data.RefinementExp / 1.0f / nextCfg.manufactureExp;
            }
        }

        private void RefreshTab()
        {
            m_XiLianAircraftItem.CacheInstanceList();
            for (int i = 1; i < 3; i++)
            {
                AircraftMainUI_XiLian_UI_XiLianAircraftItem itemUI = m_XiLianAircraftItem.GetInstance(true);
                itemUI.OnRefresh(i, OnTabClick);
                m_ItemTabs.Add(itemUI);
            }
        }

        private void RefrshTabSelect()
        {
            foreach (AircraftMainUI_XiLian_UI_XiLianAircraftItem itemTab in m_ItemTabs)
            {
                itemTab.SetTabSelect(m_CurShowIndex);
            }
        }

        private void OnTabClick(int slotId)
        {
            if (!CheckHaveAircraft(slotId))
            {
                Global.gApp.gUiMgr.OpenUIAsync<AircraftListMainUI>(LDUICfg.AircraftListMainUI).SetLoadedCall(ui => { ui.InitView((slotId)); });
                return;
            }

            m_CurShowIndex = slotId;
            ChangeTab();
            ShowAircraftView();
        }

        private void ChangeTab()
        {
            RefrshTabSelect();
            m_OnAircraftSelectCallback?.Invoke(m_CurShowIndex);
        }

        private void OnTab1Click()
        {
            OnTabClick(1);
        }

        private void OnTab2Click()
        {
            OnTabClick(2);
        }

        private bool CheckHaveAircraft(int slotId)
        {
            return Global.gApp.gSystemMgr.gAircraftMgr.IsAircraftInUseBySlotIndex(slotId);
        }

        private void ChangeToOriginalAttr()
        {
            m_ShowOriginalAttr = true;
            ChangeBtnStatus();
            RefreshEffect();
        }

        private void ChangeToCurrentAttr()
        {
            m_ShowOriginalAttr = false;
            ChangeBtnStatus();
            RefreshEffect();
        }

        private void ChangeBtnStatus()
        {
            if (m_CurShowAircraft.AircraftRefinementInfo.RefinementEffects.Count <= 0 && m_ShowOriginalAttr)
            {
                m_EffectBtnNode.gameObject.SetActive(false);
            }
            else
            {
                m_EffectBtnNode.gameObject.SetActive(true);
            }

            m_button_current.gameObject.SetActive(m_ShowOriginalAttr);
            m_button_Originally.gameObject.SetActive(!m_ShowOriginalAttr);
            TryGuide();
        }

        private void OnHelpClick()
        {
            Global.gApp.gUiMgr.OpenUIAsync<AircraftReforgeListUI>(LDUICfg.AircraftReforgeListUI).SetLoadedCall(ui => { ui.InitView(); });
        }

        private void OnXilianClick()
        {
            Global.gApp.gSystemMgr.gGuideMgr.RemoveGuide(LDGuideType.Aircraft, 3);

            List<LDNetAircraftEffect> otherAircraftEffects = Global.gApp.gSystemMgr.gAircraftMgr.GetOtherSlotAircraftEffects(m_CurShowIndex);
            double defaultPower = m_CurShowAircraft.AircraftRefinementInfo.GetDefaultPower(otherAircraftEffects);
            double refinementPower = m_CurShowAircraft.AircraftRefinementInfo.GetRefinementPower(otherAircraftEffects);
            if (refinementPower - defaultPower >= 1)
            {
                Global.gApp.gUiMgr.OpenUIAsync<AircraftPowerUp_MessageBoxUI>(LDUICfg.AircraftPowerUp_MessageBoxUI)
                    .SetLoadedCall(ui => { ui.RefreshUI(defaultPower, refinementPower, SendXiLian, OnChangeClick); });
            }
            else
            {
                SendXiLian();
            }
        }

        private void SendXiLian()
        {
            Global.gApp.gSystemMgr.gAircraftMgr.SendAircraftRefinementRequest(m_CurShowAircraft.AircraftId, m_AircraftLockIndexs);
        }

        private void OnChangeClick()
        {
            Global.gApp.gSystemMgr.gGuideMgr.RemoveGuide(LDGuideType.Aircraft, 4);
            List<LDNetAircraftEffect> otherAircraftEffects = Global.gApp.gSystemMgr.gAircraftMgr.GetOtherSlotAircraftEffects(m_CurShowIndex);
            double defaultPower = m_CurShowAircraft.AircraftRefinementInfo.GetDefaultPower(otherAircraftEffects);
            double refinementPower = m_CurShowAircraft.AircraftRefinementInfo.GetRefinementPower(otherAircraftEffects);
            if (refinementPower < defaultPower)
            {
                Global.gApp.gUiMgr.OpenUIAsync<AircraftPowerDown_MessageBoxUI>(LDUICfg.AircraftPowerDown_MessageBoxUI)
                    .SetLoadedCall(ui => { ui.RefreshUI(SendXiLian, SendChange); });
            }
            else
            {
                SendChange();
            }
        }

        private void SendChange()
        {
            Global.gApp.gSystemMgr.gAircraftMgr.SendAircraftSelectRefinementRequest(m_CurShowAircraft.AircraftId);
        }
    }
}