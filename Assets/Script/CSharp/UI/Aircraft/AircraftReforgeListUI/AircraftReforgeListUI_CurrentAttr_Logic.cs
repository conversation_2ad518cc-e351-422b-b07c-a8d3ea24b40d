namespace LD
{
    public partial class AircraftReforgeListUI_CurrentAttr
    {
        public void RefreshAttr(LDAircraftQualityProbability probability, bool haveNext)
        {
            var quality = probability.Quality;
            var pro = probability.CurrentProbability;

            m_Num.text.SetText($"{pro / 100f}%");
            if (haveNext)
            {
                m_nex.gameObject.SetActive(true);
                m_up.gameObject.SetActive(probability.NextProbability > probability.CurrentProbability);
                m_nextNum.text.SetText($"{probability.NextProbability / 100f}%");
            }
            else
            {
                m_nex.gameObject.SetActive(false);
                m_up.gameObject.SetActive(false);
            }

            m_quality_name.text.SetText(LDCommonTipsTools.GetQualityTips(quality));
            LoadSprite(m_quality_bg.image, LDCommonTipsTools.GetEquipQualityBg(quality));
            
            AircraftReforgeAttrItem cfg = AircraftReforgeAttr.Data.Get(quality);
            LoadSprite(m_quality_Dec.image, cfg.itemDec);
        }
    }
}