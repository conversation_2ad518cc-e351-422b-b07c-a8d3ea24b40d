using System;
using UnityEngine;

namespace LD
{
    public partial class AircraftPowerDown_MessageBoxUI
    {
        private Action OnXiLian;
        private Action OnTihuan;

        protected override void OnInitImp()
        {
            m_Orange_btn_Confirm.AddListener(OnTihuanClick);
            m_Green_btn_Cancel.AddListener(OnXilianClick);
        }

        protected override void OnCloseImp()
        {
        }

        public override void OnFreshUI()
        {
        }


        public void RefreshUI(Action xilian, Action tihuan)
        {
            OnXiLian = xilian;
            OnTihuan = tihuan;
            // m_ContentTxt.text.SetTips(40551, Mathf.Floor((float)(remaindPower - defaultPower)));
        }

        private void OnXilianClick()
        {
            OnXiLian?.Invoke();
            TouchClose();
        }

        private void OnTihuanClick()
        {
            OnTihuan?.Invoke();
            TouchClose();
        }
    }
}