using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using UnityEngine;
using UnityEngine.UI;


namespace LD
{
    public partial class GMUI
    {
        private LDNetGMDataMgr m_GmMgr;
        private readonly List<string> m_LanguageShowList = new List<string>() {"选择语言", "English", "中文" };
        private readonly List<string> m_LanguageStr = new List<string>() {"", "en", "zh-TW" };
        private static readonly FieldInfo[] m_UIFields = typeof(LDUICfg)
            .GetFields(BindingFlags.Public | BindingFlags.Static)
            .Where(f => f.FieldType == typeof(string))
            .ToArray();

        protected override void OnInitImp()
        {
            if (RuntimeSettings.Release)
            {
                TouchClose();
                return;
            }

            m_GmMgr = Global.gApp.gSystemMgr.gGmMgr;
            m_Close.button.AddListener(TouchClose);
            m_SendBtn.button.AddListener(OnSend);
            m_InputFieldSearch.inputField.AddListener(OnSearch);
            m_HelpBtn.button.AddListener(OnHelp);
            m_TestBtn.button.AddListener(OnTest);
            m_AddItemBtn.button.AddListener(OnAddItemBtn);
            m_ExcelCheck.button.AddListener(OnExcelCheck);
            m_pveTest.button.AddListener(OnPveTest);
            m_pveTestDisplay.button.AddListener(OnPveTestDisPlay);
            m_LanguageBtn.button.AddListener(OnChangeLangeuage);
            m_SendCloseToggle.toggle.onValueChanged.AddListener(OnSendCloseToggle);
            m_testWindow1.AddListener(OnTestWin1);
            m_testWindow2.AddListener(OnTestWin2);
            m_EnterGame.AddListener(OnEnterGame);

            InitLanguageDropdown();

            OnFreshUI();
        }

        private void InitLanguageDropdown()
        {
            Dropdown dropdown = m_DropdownLanguage.gameObject.GetComponent<Dropdown>();
            dropdown.ClearOptions();
            dropdown.AddOptions(m_LanguageShowList);
            dropdown.value = 0;
        }

        public override void OnFreshUI()
        {
            RefreshList();
            RefreshInfo();
            RefreshSendClose();
        }

        public override void OnFreshUI(string val)
        {
            if (string.IsNullOrEmpty(val))
            {
                m_InputField.inputField.text = "missionendto 149";
                return;
            }
            m_InputField.inputField.text = val.Replace("，", ",").Trim();
        }

        private void RefreshList()
        {
            m_Item.CacheInstanceList();
            for (int i = 0; i < m_GmMgr.GMHelps.Count; i++)
            {
                string str = m_GmMgr.GMHelps[i];
                GMUI_Item itemUI = m_Item.GetInstance(true);
                itemUI.RefreshUI(str, i);
            }
            SearchCMD();
        }

        private void RefreshInfo()
        {
            LDServerListItem ServerItem = Global.gApp.gSystemMgr.gLoginAccountMgr.GetCurServerItem();
            m_ServerName.text.SetText(ServerItem.name);

            long time = DateTimeUtil.GetServerTime();
            DateTime dateTime = DateTimeUtil.GetDate(time);
            m_ServerTime.text.SetText($"{dateTime.Year}-{dateTime.Month}-{dateTime.Day} {dateTime.Hour}:{dateTime.Minute}:{dateTime.Second}");

            long openServerTime = DateTimeUtil.OpenServerTime;
            DateTime openServerDateTime = DateTimeUtil.GetDate(openServerTime * 1000);
            m_ServerOpenTime.text.SetText(
                $"{openServerDateTime.Year}-{openServerDateTime.Month}-{openServerDateTime.Day} {openServerDateTime.Hour}:{openServerDateTime.Minute}:{openServerDateTime.Second}");
        }

        private void RefreshSendClose()
        {
            m_SendCloseToggle.toggle.isOn = m_GmMgr.GetSendClose();
        }

        private void Update()
        {
            RefreshInfo();
            if (Input.GetKeyUp(KeyCode.Escape))
            {
                TouchClose();
            }
        }


        private void OnSend()
        {
            string command = m_InputField.inputField.text.Replace("，", ",").Trim();

            if (TryGetUIPath(command, out string uiPath))
            {
                Global.gApp.gUiMgr.OpenUIAsync(uiPath);
                TouchClose();
                return;
            }

            m_GmMgr.SendGMCommand(command);

            if (m_GmMgr.GetSendClose())
            {
                TouchClose();
            }
        }

        private bool TryGetUIPath(string command, out string path)
        {
            // 匹配字段名
            var fi = m_UIFields.FirstOrDefault(f =>
                f.Name.Equals(command, StringComparison.OrdinalIgnoreCase));

            if (fi == null)
            {
                // 再匹配字段值
                fi = m_UIFields.FirstOrDefault(f =>
                    ((string)f.GetValue(null)).Equals(command, StringComparison.OrdinalIgnoreCase));
            }

            if (fi != null)
            {
                path = (string)fi.GetValue(null);
                return true;
            }

            path = null;
            return false;
        }

        private void OnHelp()
        {
            m_GmMgr.SendGMHelpCommand();
        }
        
        private void OnSearch(string val)
        {
            SearchCMD();
        }

        private void SearchCMD()
        {
            string txt = m_InputFieldSearch.inputField.text;
            foreach (GMUI_Item item in m_Item.mCachedList)
            {
                item.gameObject.SetActive(item.txt.text.text.Contains(txt));
            }
        }

        private void OnTest()
        {
            if (!RuntimeSettings.Release)
            {
                Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.TestUI);
            }
        }

        private void OnAddItemBtn()
        {
            if (!RuntimeSettings.Release)
            {
                Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.GMItemUI);
            }
        }

        private void OnPveTest()
        {
            //Global.gApp.gGameCtrl.RecordFight = false;
            //Global.gApp.gGameCtrl.ChangeToPveTeamFightScene(160,true);

            Global.gApp.gGameCtrl.ChangeToCrossArenaFightScene(96);

        }

        private void OnChangeLangeuage()
        {
            string language = m_InputFieldLanguage.inputField.text;
            if (string.IsNullOrEmpty(language))
            {
                Dropdown dropdown = m_DropdownLanguage.gameObject.GetComponent<Dropdown>();
                language = m_LanguageStr[dropdown.value];
            }

            if (string.IsNullOrEmpty(language))
            {
                return;
            }

            Global.gApp.gSystemMgr.gLocalDataMgr.SetVal(false, LDLocalDataKeys.Language, language);
            Global.gApp.gMsgDispatcher.Broadcast(MsgIds.Language);
        }

        private void OnPveTestDisPlay()
        {
            Global.gApp.gGameCtrl.RecordFight = true;
            Global.gApp.gGameCtrl.ChangeToPveTeamFightScene(160, true);
        }


        private void OnExcelCheck()
        {
            ExcelDataCheck.ExcelDataCheckFun();
        }

        private void OnSendCloseToggle(bool isOn)
        {
            m_GmMgr.SetSendClose(isOn);
        }

        protected override void OnCloseImp()
        {
        }

        private void OnTestWin1()
        {
            Global.gApp.gUiMgr.OpenUIAsync<MasterChallengeUI>(LDUICfg.MasterChallengeUI).SetLoadedCall(
                ui => { ui.RefreshUI(null); }
            );
            AddTimer(5f, 1, (a, b) => { Global.gApp.gUiMgr.CloseUI(LDUICfg.MasterChallengeUI); });
        }

        private void OnEnterGame()
        {
            string passIdStr = m_InputFieldPass.inputField.text;
            int passId = LDParseTools.IntParse(passIdStr);
            foreach (MainMissionItem missionItem in MainMission.Data.items)
            {
                if (missionItem.passId == passId)
                {
                    Global.gApp.gGameCtrl.ChangeToMainFightScene(missionItem.id, 0);
                    break;
                }
            }
        }
        private void OnTestWin2()
        {
            Global.gApp.gUiMgr.OpenUIAsync<OreMainUI>(LDUICfg.OreMainUI);
        }
    }

    public partial class GMUI_Item
    {
        public void RefreshUI(string str, int index)
        {
            m_txt.text.SetText(str);

            if (index % 2 == 0)
            {
                m_Bg.image.color = LDCommonColorTools.Color8A8A8A;
                m_txt.text.color = Color.yellow;
            }
            else
            {
                m_Bg.image.color = LDCommonColorTools.Color323232;
                m_txt.text.color = LDCommonColorTools.ColorFFC300;
            }
        }
    }
}