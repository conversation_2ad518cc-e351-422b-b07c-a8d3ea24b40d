using System.Collections.Generic;
using TMPro;
using UnityEngine;

namespace LD
{
    public class LDCircleMarqueeRecord
    {
        public long playerId;
        public string name;
        public LDCommonItem item;
        public List<string> param = new List<string>();
    }

    public class MessageBar : MonoBehaviour
    {
        #region GameObject
        [SerializeField]
        private TextMeshProUGUI m_Chat_Txt1;

        [SerializeField]
        private TextMeshProUGUI m_Chat_Txt2;

        private TextMeshProUGUI m_TopText;
        private TextMeshProUGUI m_DownText;
        #endregion

        private Vector3 m_TopDefaultPos;
        private Vector3 m_DownDefaultPos;

        private Vector3 m_TopTargetPos;
        private int m_CurMsgIdx;


        private bool m_UpdateState = false;
        private readonly float m_MoveSpeed = 20f;  //记录消息 滚屏速率，越大越快

        private List<LDCircleMarqueeRecord> m_ShowChatList = new();

        void Start()
        {
            m_TopText = m_Chat_Txt1;
            m_DownText = m_Chat_Txt2;

            m_TopDefaultPos = m_Chat_Txt1.gameObject.transform.localPosition;
            m_DownDefaultPos = m_Chat_Txt2.gameObject.transform.localPosition;
            m_TopTargetPos = m_TopDefaultPos + new UnityEngine.Vector3(0, 80, 0);
        }

        public void ShowMessage(List<LDCircleMarqueeRecord> msgList)
        {
            m_ShowChatList = msgList;
            if (m_ShowChatList.Count != 0)
            {
                SetTextInfo(m_Chat_Txt1, m_ShowChatList[m_CurMsgIdx]);
                m_CurMsgIdx++;
            }

            CheckMoveNext();
        }

        public int GetMsgCount()
        {
            return m_ShowChatList.Count;
        }

        public void AddNewMsgAtBegin(LDCircleMarqueeRecord msgData)
        {
            if (m_ShowChatList.Count == 0)
            {
                m_ShowChatList.Add(msgData);
                SetTextInfo(m_Chat_Txt1, m_ShowChatList[m_CurMsgIdx]);
                m_CurMsgIdx++;
            }
            else
            {
                int insertIdx = 0;
                if (m_ShowChatList.Count >= 1)
                {
                    insertIdx = 1;
                }
                m_ShowChatList.Insert(insertIdx, msgData);

                CheckMoveNext();
            }
        }

        public void AddNewMsgAtEnd(LDCircleMarqueeRecord msgData)
        {
            m_ShowChatList.RemoveAt(m_ShowChatList.Count - 1);
            m_ShowChatList.Add(msgData);
            CheckMoveNext();
        }

        private void CheckMoveNext()
        {
            if (m_UpdateState)
                return;
            if (m_ShowChatList.Count > 1)
            {
                SetTextInfo(m_Chat_Txt2, m_ShowChatList[m_CurMsgIdx]);
                m_CurMsgIdx++;
                if (m_CurMsgIdx >= m_ShowChatList.Count)
                {
                    m_CurMsgIdx = 0;
                }
                m_UpdateState = true;
            }
        }

        private void SetTextInfo(TextMeshProUGUI text, LDCircleMarqueeRecord msgData)
        {
            text.SetTips(99211, msgData.name, msgData.item.ColorName());//tipId目前写死，有需求可以传入
        }

        void Update()
        {
            UpdateChatPartial();
        }

        private void UpdateChatPartial()
        {
            if (!m_UpdateState)
                return;

            m_Chat_Txt1.gameObject.transform.localPosition = Vector3.MoveTowards(m_Chat_Txt1.gameObject.transform.localPosition, m_TopTargetPos, m_MoveSpeed * Time.deltaTime);
            m_Chat_Txt2.gameObject.transform.localPosition = Vector3.MoveTowards(m_Chat_Txt2.gameObject.transform.localPosition, m_TopDefaultPos, m_MoveSpeed * Time.deltaTime);

            if (m_Chat_Txt1.gameObject.transform.localPosition == m_TopTargetPos && m_Chat_Txt2.gameObject.transform.localPosition == m_TopDefaultPos)
            {
                m_UpdateState = false;
                (m_Chat_Txt1, m_Chat_Txt2) = (m_Chat_Txt2, m_Chat_Txt1);
                m_Chat_Txt2.gameObject.transform.localPosition = m_DownDefaultPos;
                CheckMoveNext();
            }
        }
    }
}