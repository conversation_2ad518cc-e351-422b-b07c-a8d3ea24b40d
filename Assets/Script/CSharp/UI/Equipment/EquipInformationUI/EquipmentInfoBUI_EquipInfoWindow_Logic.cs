using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD
{
    public partial class EquipmentInfoBUI_EquipInfoWindow
    {
        private LDNetEquipInfo m_CurEquipInfo;
        private bool m_IsUp;
        private bool m_IsEquip;
        private bool m_isLock;
        private string m_ChangeBreakDownDisposeKey;
        private Action m_CheckMoreAttrAction;

        public override void ItemInit()
        {
            m_Btn_BreakDown.AddListener(OnBreakDownClick);
            m_Btn_equip.AddListener(OnEquipClick);
            m_Btn_replace.AddListener(OnEquipClick);

            m_Btn_lock.AddListener(OnLockClick);
            m_ChangeDisposeBtn.AddListener(OnChangeDisposeBtnClick);
            m_CheckAttrlBtn.AddListener(OnCheckMoreAttrClick);

            m_reforge_btn.AddListener(OnClickRefinement);
        }


        /// <summary>
        /// 装备信息窗口逻辑
        /// </summary>
        /// <param name="equipInfo"></param>
        /// <param name="slotEquiping"></param>
        public void RefreshEquipInfo(LDNetEquipInfo equipInfo, bool isUp, Action checkMoreAttrAction)
        {
            m_CurEquipInfo = equipInfo;
            m_IsUp = isUp;
            m_CheckMoreAttrAction = checkMoreAttrAction;
            m_Btn_lock.gameObject.SetActive(m_CurEquipInfo.EquipCfg.type == 2);
            if (m_CurEquipInfo.EquipCfg.type == 2)
            {
                m_ChangeBreakDownDisposeKey = LDLocalDataKeys.EquipBreakDownAfterEquip_Weapon;
            }
            else
            {
                m_ChangeBreakDownDisposeKey = LDLocalDataKeys.EquipBreakDownAfterEquip_Equipment;
            }


            RefreshNodeActive();
            RefreshEquipInfo();
            RefreshEquipAttr();
            RefreshCitiaoList();
            RefreshLockStatus();
            RefreshChangeDisposeSelectStatus();
            RefreshSeeMoreAttrSelectStatus();
            LayoutRebuilder.ForceRebuildLayoutImmediate(m_bgParent.gameObject.transform.rectTransform());
            LayoutRebuilder.ForceRebuildLayoutImmediate(this.gameObject.transform.rectTransform());
        }

        private void RefreshNodeActive()
        {
            int slotId = m_CurEquipInfo.GetEquipSlotId();
            var isEquip = Global.gApp.gSystemMgr.gEquipmentMgr.CheckSlotIsEquip(slotId);

            m_inuse.gameObject.SetActive(m_IsUp);

            m_equipBtnNode.gameObject.SetActive(!isEquip);
            m_replaceBtnNode.gameObject.SetActive(isEquip);
            m_BreakDownBtnNode.gameObject.SetActive(isEquip);
            Transform controlNode = m_btnNode.gameObject.transform.Find("GameObject");
            controlNode.gameObject.SetActive(!m_IsUp);

            m_FenjieNode.gameObject.SetActive(!m_IsUp && m_CurEquipInfo.EquipCfg.type != 2);
            m_citiaoNode.gameObject.SetActive(m_CurEquipInfo.EquipEffects.Count > 0);
            m_CheckAttrlBtn.gameObject.SetActive(!m_IsUp);
            m_xinxi_node.gameObject.SetActive(!m_IsUp);
            m_reforgeBtnNode.gameObject.SetActive(m_CurEquipInfo.CanReforge());
        }

        private void RefreshChangeDisposeSelectStatus()
        {
            bool isSelect = Global.gApp.gSystemMgr.gLocalDataMgr.GetIntVal(true, m_ChangeBreakDownDisposeKey) == 1;

            if (m_CurEquipInfo.EquipCfg.type == 2)
            {
                if (isSelect)
                {
                    Global.gApp.gSystemMgr.gLocalDataMgr.SetIntVal(true, m_ChangeBreakDownDisposeKey, 0);
                }

                m_ChangeDisposeBtncheckMark.gameObject.SetActive(false);
            }
            else
            {
                m_ChangeDisposeBtncheckMark.gameObject.SetActive(isSelect);
            }
        }

        private void RefreshSeeMoreAttrSelectStatus()
        {
            bool isSelect = Global.gApp.gSystemMgr.gLocalDataMgr.GetIntVal(true, LDLocalDataKeys.EquipCanSeeMoreAttribution) == 1;
            m_checkMark.gameObject.SetActive(isSelect);
        }

        private void RefreshEquipInfo()
        {
            var equipCommonItem = m_CurEquipInfo.GetEquipCommonItem();
            var knapsackItem = LDUIPrefabTools.GetKnapsackItemUI(m_icon.rectTransform);
            LDUIPrefabTools.InitKnapsackItem(knapsackItem, equipCommonItem);
            knapsackItem.SetShowDetail(false);

            m_EquipName.text.SetTips(m_CurEquipInfo.EquipCfg.name);
            var slotCfg = Slot.Data.Get(m_CurEquipInfo.EquipCfg.slot);
            m_EquipType.text.SetTips(slotCfg.name);
            var power = m_CurEquipInfo.GetEquipPower();
            if (power > 0)
            {
                m_powerNum.text.SetText(UiTools.FormateMoney(power));
            }
            else
            {
                m_powerNum.text.SetText("-" + UiTools.FormateMoney(power * -1));
            }

            LoadSprite(m_QualityBG.image, m_CurEquipInfo.GetEquipQualityItem().equipmentInfoTopBG);
            if (!m_IsUp)
            {
                int slotId = m_CurEquipInfo.GetEquipSlotId();
                var equipUp = Global.gApp.gSystemMgr.gEquipmentMgr.GetEquipBySlotId(slotId);
                var upPower = equipUp.GetEquipPower();
                Global.Log($"equip power  left: {upPower}   right:{power}");
                m_up_arrow.gameObject.SetActive(power > upPower);
                m_down_arrow.gameObject.SetActive(power < upPower);
            }
            else
            {
                m_up_arrow.gameObject.SetActive(false);
                m_down_arrow.gameObject.SetActive(false);
                m_new_arrow.gameObject.SetActive(false);
            }
        }

        private void RefreshEquipAttr()
        {
            Dictionary<int, LDEquipAttrAddition> upAttrDict = new();
            if (!m_IsUp)
            {
                int slotId = m_CurEquipInfo.GetEquipSlotId();
                var equipUp = Global.gApp.gSystemMgr.gEquipmentMgr.GetEquipBySlotId(slotId);
                var equipUpAttr = equipUp.AttrAddition;
                foreach (LDEquipAttrAddition addition in equipUpAttr)
                {
                    upAttrDict[addition.Id] = addition;
                }
            }

            var equipAttr = m_CurEquipInfo.AttrAddition;
            m_attr.CacheInstanceList();
            foreach (var attr in equipAttr)
            {
                var attrItem = m_attr.GetInstance(true);
                bool isUp = false;
                bool isNew = false;
                bool isDown = false;
                if (!m_IsUp)
                {
                    if (upAttrDict.TryGetValue(attr.Id, out var upAttrAddition))
                    {
                        if (attr.Val > upAttrAddition.Val)
                        {
                            isUp = true;
                        }
                        else if (attr.Val < upAttrAddition.Val)
                        {
                            isDown = true;
                        }
                    }
                    else
                    {
                        isNew = true;
                    }
                }

                attrItem.RefreshAttr(attr, isUp, isDown, isNew);
            }
        }

        private void RefreshLockStatus()
        {
            m_isLock = Global.gApp.gSystemMgr.gNetClientDataMgr.CheckEquipLocked(m_CurEquipInfo.Id);
            m_lockNode.gameObject.SetActive(m_isLock);
            m_unlockNode.gameObject.SetActive(!m_isLock);
        }

        private void RefreshCitiaoList()
        {
            Dictionary<int, LDNetAttrEffect> upEffectDict = new();
            if (!m_IsUp)
            {
                int slotId = m_CurEquipInfo.GetEquipSlotId();
                var equipUp = Global.gApp.gSystemMgr.gEquipmentMgr.GetEquipBySlotId(slotId);
                var equipEffects = equipUp.EquipEffects;
                foreach (var effect in equipEffects)
                {
                    upEffectDict[effect.EffectId] = effect;
                }
            }

            var citiaoList = m_CurEquipInfo.EquipEffects;
            m_CitiaoItem.CacheInstanceList();
            foreach (LDNetAttrEffect effect in citiaoList)
            {
                var itemUI = m_CitiaoItem.GetInstance(true);
                bool isUp = false;
                bool isNew = false;
                bool isDown = false;
                if (!m_IsUp)
                {
                    if (upEffectDict.TryGetValue(effect.EffectId, out var upEffect))
                    {
                        if (effect.EffectValue > upEffect.EffectValue)
                        {
                            isUp = true;
                        }
                        else if (effect.EffectValue < upEffect.EffectValue)
                        {
                            isDown = true;
                        }
                    }
                    else
                    {
                        isNew = true;
                    }
                }

                itemUI.RefreshCitiaoItem(effect, isUp, isDown, isNew);
            }

            LayoutRebuilder.ForceRebuildLayoutImmediate(m_citiaoNode.rectTransform);
        }

        public void RefreshMoreAttr()
        {
            RefreshEquipAttr();
            RefreshCitiaoList();
        }

        #region click

        private void OnChangeDisposeBtnClick()
        {
            Global.gApp.gSystemMgr.gLocalDataMgr.SetIntVal(true, m_ChangeBreakDownDisposeKey, !m_ChangeDisposeBtncheckMark.gameObject.activeSelf ? 1 : 0);
            RefreshChangeDisposeSelectStatus();
        }

        private void OnCheckMoreAttrClick()
        {
            Global.gApp.gSystemMgr.gLocalDataMgr.SetIntVal(true, LDLocalDataKeys.EquipCanSeeMoreAttribution, !m_checkMark.gameObject.activeSelf ? 1 : 0);
            RefreshSeeMoreAttrSelectStatus();
            m_CheckMoreAttrAction?.Invoke();
        }

        private void OnClickRefinement()
        {
            Global.gApp.gUiMgr.OpenUIAsync<EquipmentReforgeUI>(LDUICfg.EquipmentReforgeUI).SetLoadedCall((ui) => { ui.InitEquip(m_CurEquipInfo); });
        }

        private void OnBreakDownClick()
        {
            int slotId = m_CurEquipInfo.GetEquipSlotId();
            var isEquiping = Global.gApp.gSystemMgr.gEquipmentMgr.GetEquipBySlotId(slotId);

            m_isLock = Global.gApp.gSystemMgr.gNetClientDataMgr.CheckEquipLocked(m_CurEquipInfo.Id);
            if (m_isLock)
            {
                Global.gApp.gToastMgr.ShowGameTips(65535);
                return;
            }

            if (m_CurEquipInfo.GetEquipPower() > isEquiping.GetEquipPower())
            {
                Global.gApp.gUiMgr.OpenUIAsync<ConfirmUI>(LDUICfg.NormalConfirmUI).SetLoadedCall(confirmUI =>
                        confirmUI.SetInfo(SendBreakDown, null, UiTools.Localize(65542)) // 
                );
            }
            else
            {
                SendBreakDown();
            }
        }

        private void SendBreakDown()
        {
            Global.gApp.gSystemMgr.gEquipmentMgr.SendEquipBreakDownRequest(new List<long>() { m_CurEquipInfo.Id });
        }

        private void OnEquipClick()
        {
            int slotId = m_CurEquipInfo.GetEquipSlotId();
            var isEquiping = Global.gApp.gSystemMgr.gEquipmentMgr.GetEquipBySlotId(slotId);

            if (isEquiping != null)
            {
                if (m_CurEquipInfo.GetEquipPower() < isEquiping.GetEquipPower())
                {
                    Global.gApp.gUiMgr.OpenUIAsync<ConfirmUI>(LDUICfg.NormalConfirmUI).SetLoadedCall(confirmUI =>
                            confirmUI.SetInfo(SendEquip, null, UiTools.Localize(65543)) // 
                    );
                }
                else
                {
                    SendEquip();
                }
            }
            else
            {
                SendEquip();
            }
        }

        private void SendEquip()
        {
            Global.gApp.gSystemMgr.gEquipmentMgr.SendEquipPutOnRequest(m_CurEquipInfo.Id, m_CurEquipInfo.GetEquipSlotId());
        }

        private void OnCloseClick()
        {
            Global.gApp.gUiMgr.CloseUI(LDUICfg.EquipmentInfoBUI);
        }

        private void OnLockClick()
        {
            Global.gApp.gSystemMgr.gNetClientDataMgr.SetEquipmentLock(m_CurEquipInfo.Id, !m_isLock);
            RefreshLockStatus();
        }

        #endregion
    }
}