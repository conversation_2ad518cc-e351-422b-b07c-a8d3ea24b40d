using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD
{
    public partial class BattlePassMain
    {
        private List<BattlePassMain_TabBtn> m_TabBtns = new();
        private int m_CurShowBattlePassId;

        protected override void OnInitImp()
        {
            m_btn_close.AddListener(TouchClose);
            InitToggleBtns();
        }

        protected override void OnCloseImp()
        {
        }

        public override void OnFreshUI()
        {
        }

        public override void OnFreshUI(int val)
        {
            GamePlayBattlePass.Data.TryGet(m_CurShowBattlePassId, out var cfg, false);
            if (cfg != null)
            {
                if (cfg.battlePassType == 1)
                {
                    RefreshBattlePass1CallBack();
                }
                else
                {
                    RefreshBattlePassPublicizeCallBack();
                }
            }
            else
            {
                RefreshBattlePass1CallBack();
            }

            RefreshToggleBtnsRed();
            if (!RefreshBattleNeedShow())
            {
                Global.gApp.gMsgDispatcher.Broadcast(MsgIds.BattlePassRefresh);
                // InitToggleBtns();
                // InitData(LDSystemEnum.Custom_BattlePass_Level);
            }
        }

        private bool RefreshBattleNeedShow()
        {
            bool showBtn = Global.gApp.gSystemMgr.gBattlePassMgr.CheckBattlePassBtnShow(m_CurShowBattlePassId);
            return showBtn;
        }

        public void InitData(LDSystemEnum sys)
        {
            int battlePassId = 1;
            switch (sys)
            {
                case LDSystemEnum.Custom_BattlePass_CoinPass:
                    battlePassId = 5;
                    break;
                case LDSystemEnum.Custom_BattlePass_Team:
                    battlePassId = 8;
                    break;
                case LDSystemEnum.Custom_BattlePass_PveTower:
                    battlePassId = 6;
                    break;
                case LDSystemEnum.Custom_BattlePass_PveTower2:
                    battlePassId = 9;
                    bool show = Global.gApp.gSystemMgr.gBattlePassMgr.CheckGPCanShowBtn(battlePassId);
                    if (!show)
                    {
                        battlePassId = Global.gApp.gSystemMgr.gBattlePassMgr.GetTowerBattlePassId(battlePassId);
                    }

                    break;
                case LDSystemEnum.Custom_BattlePass_Expedition:
                    battlePassId = 7;
                    break;
                default:
                    battlePassId = 1;
                    for (int i = m_TabBtns.Count - 1; i >= 0; i--)
                    {
                        var id = m_TabBtns[i].m_BattlePassId;
                        BattlePass.Data.TryGet(id, out var cfg, false);
                        if (cfg != null)
                        {
                            bool showBtn = Global.gApp.gSystemMgr.gBattlePassMgr.CheckCanShowBtn();
                            if (showBtn)
                            {
                                battlePassId = id;
                                break;
                            }
                        }
                        else
                        {
                            bool showBtn = Global.gApp.gSystemMgr.gBattlePassMgr.CheckGPCanShowBtn(id);
                            if (showBtn)
                            {
                                battlePassId = id;
                                break;
                            }
                        }
                    }

                    break;
            }

            ChangeTab(battlePassId);
            RefreshToggleBtnsRed();

            int jumpIndex = m_TabBtns.Count - 1;
            for (int i = m_TabBtns.Count - 1; i >= 0; i--)
            {
                var tabItem = m_TabBtns[i];
                if (tabItem.m_BattlePassId == battlePassId)
                {
                    jumpIndex = i;
                    break;
                }
            }

            float itemWidth = 254;
            int rightCount = m_TabBtns.Count - jumpIndex - 1;
            float moveOffset = 0;
            if (rightCount > 2)
            {
                moveOffset = rightCount * itemWidth;
            }

            AddTimer(.2f, 1, (a, b) =>
            {
                Vector3 pos = m_TabContent.gameObject.transform.localPosition;
                m_TabContent.gameObject.transform.localPosition = new Vector3(pos.x + moveOffset, pos.y, pos.z); // viewport比list 小一点
            });
        }

       
        // 页签
        private void InitToggleBtns()
        {
            m_TabBtn.CacheInstanceList();
            m_TabBtns.Clear();


            var gpBattlePassInfos = Global.gApp.gSystemMgr.gBattlePassMgr.GPData.m_BattlePassInfo;
            foreach (var info in gpBattlePassInfos)
            {
                var cfg = GamePlayBattlePass.Data.Get(info.Key);
                if (cfg == null)
                    continue;

                if (cfg.preconditionsID > 0)
                {
                    if (Global.gApp.gSystemMgr.gBattlePassMgr.CheckBattlePassBtnShow(cfg.preconditionsID))
                    {
                        continue;
                    }
                }

                if (!Global.gApp.gSystemMgr.gFilterMgr.Filter(cfg.unlockCondition))
                    continue;
                if (!Global.gApp.gSystemMgr.gBattlePassMgr.CheckBattlePassBtnShow(cfg.id))
                    continue;

                BattlePassMain_TabBtn btn = m_TabBtn.GetInstance(true);
                btn.RefreshUI(info.Key, ChangeTab);
                m_TabBtns.Add(btn);
            }

            BattlePassItem[] items = BattlePass.Data.items;
            for (int i = 0; i < items.Length; i++)
            {
                BattlePassItem cfg = items[i];
                if (!Global.gApp.gSystemMgr.gFilterMgr.Filter(cfg.unlockCondition))
                    continue;

                if (!Global.gApp.gSystemMgr.gBattlePassMgr.CheckCanShowBtn())
                    continue;
                BattlePassMain_TabBtn btn = m_TabBtn.GetInstance(true);
                btn.RefreshUI(cfg.id, ChangeTab);
                m_TabBtns.Add(btn);
            }

            LayoutRebuilder.ForceRebuildLayoutImmediate(m_TabContent.rectTransform);
            LayoutRebuilder.ForceRebuildLayoutImmediate(m_TabScroll.rectTransform);
        }

        //页签
        private void RefreshToggleBtns()
        {
            foreach (var funcBtn in m_TabBtns)
            {
                funcBtn.SetSelectState(funcBtn.m_BattlePassId == m_CurShowBattlePassId);
            }
        }

        private void RefreshToggleBtnsRed()
        {
            foreach (var funcBtn in m_TabBtns)
            {
                funcBtn.RefreshRedStatus();
            }
        }

        //切换页签
        private void ChangeTab(int Id)
        {
            if (m_CurShowBattlePassId == Id)
                return;
            m_CurShowBattlePassId = Id;
            RefreshToggleBtns();
            RefreshViewActive();
            GamePlayBattlePass.Data.TryGet(m_CurShowBattlePassId, out var battlePassItem, false);
            m_BattlePass1.gameObject.SetActive(battlePassItem == null || battlePassItem.battlePassType == 1);
            m_BattlePassPublicize.gameObject.SetActive(battlePassItem != null && battlePassItem.battlePassType == 2);
            if (battlePassItem != null)
            {
                switch (battlePassItem.battlePassType)
                {
                    case 1:
                        RefreshBattlePass1();
                        break;
                    case 2:
                        RefreshBattlePassPublicize();
                        break;
                }
            }
            else
            {
                RefreshBattlePass1();
            }
        }

        private void RefreshBattlePass1()
        {
            BattlePassMain_BattlePass1 itemUI = m_BattlePass1.gameObject.GetComponent<BattlePassMain_BattlePass1>();
            itemUI.RefreshView(m_CurShowBattlePassId);
        }

        private void RefreshBattlePassPublicize()
        {
            BattlePassMain_BattlePassPublicize itemUI = m_BattlePassPublicize.gameObject.GetComponent<BattlePassMain_BattlePassPublicize>();
            itemUI.RefreshView(m_CurShowBattlePassId);
        }

        private void RefreshBattlePass1CallBack()
        {
            BattlePassMain_BattlePass1 itemUI = m_BattlePass1.gameObject.GetComponent<BattlePassMain_BattlePass1>();
            itemUI.RefreshCallBack();
        }

        private void RefreshBattlePassPublicizeCallBack()
        {
            BattlePassMain_BattlePassPublicize itemUI = m_BattlePassPublicize.gameObject.GetComponent<BattlePassMain_BattlePassPublicize>();
            itemUI.RefreshCallBack();
        }

        private void RefreshViewActive()
        {
            m_BattlePass1.gameObject.SetActive(m_CurShowBattlePassId == 1);
        }
    }
}