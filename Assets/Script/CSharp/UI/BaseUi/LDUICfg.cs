using System.Collections.Generic;

namespace LD
{

    public class UIOrderInfo
    {
        public static string MainCanvas = "RootCanvas";
        public static string Canvas1 = "Canvas1";
        public static string Canvas2 = "Canvas2";
        public static string Canvas3 = "Canvas3";
        public static string Canvas4 = "Canvas4";
        public static string Canvas5 = "Canvas5";
        public static List<string> CanvasList = new List<string>() { Canvas5, Canvas4, Canvas3, Canvas2, Canvas1, MainCanvas };
        public int PlaneDistance = -1;
        public int OrderInLayer = -1;
    }

    public class WndUICfg
    {
        //资源路径
        public string ResPath;
        // 点击 特效。暂时没用
        public bool NoTouchEffect = false;
        // 是否创建 屏蔽层。战斗ui 就没有屏蔽层
        public bool NoTouchMask = false;
        // 半透明遮罩
        public bool MaskBlack = false;
        // 告诉模糊
        public bool GaussMask = false;
        // 废弃 用UILV 更准确
        public bool HalfUi = false;
        // 响应空白区域 关闭 与NoTouchMask 搭配使用
        public bool TouchEmptyClose = false;
        public float TouchEmptyCloseDelayTime = 0;
        //ui 层级关系 一般都是2级界面
        public int UILevel = 2;
        // 是否不响应 返回键 事件
        public bool UnRespondEscape = true;
        //
        public bool UnLoadRes = false;
        public ResSceneType ResSceneType = ResSceneType.NormalUI;
        public bool CloseRelease = true;
        public bool AutoOrder = true; // 自动层级
        public int ShowCurrencyTab = 0; // 展示顶部资源栏类型
        public bool ShowMenuTab = false; // 展示底部大页签
        public string AudioClip = string.Empty;// 打开ui 的时候播放的声音
        public float AudiuClipDelayTime = 0;// 打开ui 的时候播放的声音
        public Dictionary<string, UIOrderInfo> OrderInfo = new Dictionary<string, UIOrderInfo>();
        public List<string> OnUICloseListener = null;
        public bool AutoUnload = true; // 每隔3秒尝试卸载为打开的UI
        public WndUICfg()
        {
            OrderInfo[UIOrderInfo.MainCanvas] = new UIOrderInfo() { PlaneDistance = 100, OrderInLayer = 30 };
        }
        public int GeMaxOrder()
        {
            foreach (string canvasName in UIOrderInfo.CanvasList)
            {
                if (OrderInfo.ContainsKey(canvasName))
                {
                    return OrderInfo[canvasName].OrderInLayer;
                }
            }
            return -1;
        }
    }

    public class LDUICfg
    {
        public static string GMUI = "GMUI";
        public static string GMItemUI = "GMItemUI";
        public static string TestUI = "TestUI";
        public static string CommonUI = "CommonPanel";
        public static string PoorNetworkUI = "PoorNetworkUI";
        public static string TouchMaskUi = "PrefabsN/UI/Resident/TouchMask";
        public static string CanvasPath = "PrefabsN/UI/Resident/Canvas";
        public static string GameTipsUi = "PrefabsN/UI/Resident/ToastTips";
        public static string ItemIconNode = "PrefabsN/UI/Item/ItemIconNode";
        public static string PropProgressUI = "PrefabsN/UI/Fight/PropProgressUI";
        public static string UIGuidTipsNode = "PrefabsN/UI/Fight/UIGuidTipsNode";
        public static string Fx_renwushibai = "PrefabsN/UI/Fight/fx_renwushibai";
        public static string Fx_renwujinru = "PrefabsN/UI/Fight/fx_renwujinru";
        public static string MarqueeTipsUi = "PrefabsN/UI/Resident/MarqueeTips";

        public static string EliteHp = "PrefabsN/UI/Fight/NormalAIHP";
        public static string NormalTrapHP = "PrefabsN/UI/Fight/NormalTrapHP";
        public static string NormalCarHP = "PrefabsN/UI/Fight/NormalCarHP";
        public static string FangXianHp = "PrefabsN/UI/Fight/FangXianHp";
        public static string TriggerProgressBar = "PrefabsN/UI/Fight/TriggerProgressBar";
        public static string GoldProgressHP = "PrefabsN/UI/FightEndUI/CoinPass/GoldProgress";
        public static string HeroHP = "PrefabsN/UI/Fight/HeroHP";
        public static string UIBattleQuickPhrases = "PrefabsN/UI/Fight/UIBattleQuickPhrases";
        public static string TowerProgress = "PrefabsN/UI/Fight/TowerProgress";
        public static string SwitchElement = "PrefabsN/UI/Fight/SwitchElement";
        public static string BulletHp = "PrefabsN/UI/Fight/BulletHP";
        public static string NormalCitiao = "PrefabsN/UI/Fight/NormalCitiao";

        public static string AtlasPath = "SpriteAtlas/BattleAtlas";
        public static string MainUI = "MainUI";
        public static string ShopUI = "ShopUI";
        public static string BuJian_Roll_UI = "BuJian_Roll_UI";
        public static string UAV_Roll_UI = "UAV_Roll_UI";
        public static string UAVRollTips = "UAVRollTips";
        public static string DriverRollTips = "DriverRollTips";
        public static string BuJianRollTipsUI = "BuJianRollTipsUI";
        public static string MechaDiyUI = "MechaDiyUI";
        public static string LogoUI = "LogoUI";
        public static string LoginUI = "LoginUI";
        public static string ServerChooseUI = "ServerChooseUI";
        public static string LoadingUI = "LoadingUI";
        public static string AppPingJia = "AppPingJia";
        public static string ContinueFighting_MessageBox = "ContinueFighting_MessageBox";

        // 机甲
        public static string MechaMainUI = "MechaMainUI";
        public static string MechaUpgradeUI = "MechaUpgradeUI";
        public static string MechaUpgradeOverviewUI = "MechaUpgradeOverviewUI";
        public static string MechaGainedUI = "MechaGainedUI";
        public static string MechaListMainUI = "MechaListMainUI";
        public static string MechaUpgradeResultUI = "MechaUpgradeResultUI";
        public static string MechaSkinMainUI = "MechaSkinMainUI";
        public static string MechaSkinUpgradeResultUI = "MechaSkinUpgradeResultUI";
        public static string MechaStoryUI = "MechaStoryUI";
        public static string MechaSkinGainedUI = "MechaSkinGainedUI";
        public static string MechaSkinTipsUI = "MechaSkinTips";

        // PopUI - 通用道具获得
        public static string GetRewardUI = "GetRewardUI";
        // PopUI? - 通用道具飞行
        public static string FlyItem = "FlyItem";
        // PopUI - 功能解锁
        public static string SystemUnlockUI = "SystemUnlockUI";
        // PopUI - 角色升级
        public static string RoleLevelUpUI = "RoleLevelUpUI";

        // AFK
        public static string AFKModeUI = "AFKModeUI";
        public static string AFKSweepUI = "AFKSweepUI";

        // 体力
        public static string BuyEnergyUI = "BuyEnergyUI";
        public static string BuyLeftEnergyUI = "BuyLeftEnergyUI";

        // 邮件
        public static string MailList = "MailList";
        public static string MailContent = "MailContent";

        public static string DiyPartListUI = "DIYPartUI";
        public static string PartUpgradeUI = "PartUpgradeUI";
        public static string PartRankUpResultUI = "PartRankUpResultUI";
        public static string ResetWeaponUI = "ResetWeaponUI";
        public static string UAVrollback = "UAVrollback";
        public static string GetNewWeaponUI = "GetNewWeaponUI";
        public static string PartSkinGainedUI = "PartSkinGainedUI";
        public static string PartSkinUpgradeResultUI = "PartSkinUpgradeResultUI";
        public static string PartSkinTipsUI = "PartSkinTipsUI";

        // UAV
        public static string GetNewUAVUI = "GetNewUAVUI";
        public static string UAVMainUI = "UAVMainUI";
        public static string UAVEquipUI = "UAVEquipUI";
        public static string UAVShowSkill = "UAVShowSkill";
        public static string UAVUpgradeResultUI = "UAVUpgradeResultUI";
        public static string UAVsynthesis = "UAVsynthesis";
        public static string UAV_Tips = "UAV_tips";
        public static string Equipment_Tips = "Equipment_Tips";
        // 背包相关
        public static string KnapsackUI = "KnapsackUI";
        public static string Box_Tips = "Box_Tips";
        public static string Box_Tips2 = "Box_Tips2";
        public static string ChooseBox_Use = "ChooseBox_Use";
        public static string ChooseBox_Use2 = "ChooseBox_Use2";
        public static string Item_Tips = "Item_Tips";
        public static string Compose_Tips = "Compose_Tips";
        public static string Mech_Tips = "Mech_Tips";
        public static string DIYPart_Tips = "DIYPart_Tips";
        public static string MessageBox_KuaiSuHuoQu = "MessageBox_KuaiSuHuoQu";
        public static string Top_Item_Tips = "Top_Item_Tips";
        public static string CiTiaoListView = "CiTiaoListView";
        public static string Aircraft_Tips = "Aircraft_tips";

        // 副本玩法
        public static string GamePlayRootUI = "GamePlayRootUI";
        public static string PVETowerMainUI = "PVETowerMainUI";
        public static string CommonExplainUI = "CommonExplainUI";
        public static string FightTowerTipsUI = "PVETowerTipsUI";
        public static string PVETowerRankTipsUI = "PVETowerRankTipsUI";
        public static string PVETowerPartUI = "PVETowerPartUI";

        // 战斗结算
        public static string MainFightLoseUI = "MainFightLoseUI";
        public static string MainFightWinUI = "MainFightWinUI";
        public static string PveTowerFightLoseUI = "PveTowerFightLoseUI";
        public static string PveTowerFightWinUI = "PveTowerFightWinUI";
        public static string CoinPassFightLoseUI = "CoinPassFightLoseUI";
        public static string CoinPassFightWinUI = "CoinPassFightWinUI";
        public static string ExpeditionFightLoseUI = "ExpeditionFightLoseUI";
        public static string ExpeditionFightWinUI = "ExpeditionFightWindUI";
        public static string SeasonFightLoseUI = "SeasonFightLoseUI";
        public static string SeasonFightWinUI = "SeasonFightWinUI";
        public static string GuildFightLoseUI = "GuildFightLoseUI";
        public static string GuildFightWinUI = "GuildFightWinUI";
        public static string PveFightLoseUI = "PveFightLoseUI";
        public static string PveFightWinUI = "PveFightWinUI";
        public static string ArenaFightLoseUI = "ArenaFightLoseUI";
        public static string ArenaFightWinUI = "ArenaFightWinUI";
        public static string GuildLeagueFightLoseUI = "GuildLeagueFightLoseUI";
        public static string GuildLeagueFightWinUI = "GuildLeagueFightWinUI";
        public static string CSArenaFightLoseUI = "CSArenaFightLoseUI";
        public static string CSArenaFightWinUI = "CSArenaFightWinUI";
        public static string TacticFightLoseUI = "TacticFightLoseUI";
        public static string TacticFightWinUI = "TacticFightWinUI";

        // 战力
        public static string PowerUI = "PowerUI";

        //战力对比
        public static string PowerCompareUI = "PowerCompareUI";

        // 战力
        public static string OptionsUI = "OptionsUI";
        public static string LangSettingUI = "LangSettingUI";
        public static string ADTips = "ADTips";

        // 排行榜
        public static string RankUI = "RankUI";
        public static string MainMissionRankUI = "MainMissionRankUI";
        public static string MainMissionTop5UI = "MainMissionTop5UI";
        public static string PVETowerRankUI = "PVETowerRankUI";
        public static string ExpeditionRankUI = "ExpeditionRankUI";
        public static string SeasonRankUI = "SeasonRankUI";
        public static string SeasonEndlessRankUI = "SeasonEndlessRankUI";
        public static string SeasonMissionTop5UI = "SeasonMissionTop5UI";

        // 战斗loading
        public static string MasterChallengeUI = "MasterChallengeUI";
        public static string BossChallengeUI = "BossChallengeUI";
        public static string ArenaLoadingUI = "ArenaLoadingUI";
        public static string LeagueLoadingUI = "LeagueLoadingUI";

        // 金币本
        public static string CoinPassMainUI = "CoinPassMainUI";

        // 快速购买钻石
        public static string QuickBuyDiamondUI = "QuickBuyDiamondUI";
        // 快速购买金币
        public static string QuickBuyGoldUI = "QuickBuyGoldUI";
        //月卡
        public static string MonthlyCardUI = "MonthlyCardUI";
        //首冲
        public static string FirstRechargeUI = "FirstRecharge";
        //部件礼包
        public static string DIYPackUI = "DIYPackUI";

        public static string BindAccountUI = "BindAccountUI";

        // 属性面板
        public static string AttrInfo = "AttrInfo";
        // 战令
        public static string BattlePassBuy = "BattlePassBuy";
        public static string BattlePassMain = "BattlePassMain";
        public static string BattlePassBuyPublicize = "BattlePassBuyPublicize";

        // 机甲抽奖
        public static string MechaSpinMain = "MechaSpinMain";
        public static string MechaSpinBuyItem = "MechaSpinBuyItem";
        public static string MechaSpinFinishUI = "MechaSpinFinishUI";
        public static string MechaSpin_Hint = "MechaSpin_Hint";
        public static string MechaSpinExchangeShopUI = "MechaSpinExchangeShopUI";
        public static string MechaSpinPopUI = "MechaSpinPopUI";
        public static string MechaSpinTipsUI = "MechaSpinTipsUI";

        // 主线礼包
        public static string MissionPackMain = "MissionPackMain";

        //部件累抽
        public static string DiyActivityMain = "DiyActivityMain";
        public static string DiyActivity_RankRewardUI = "DiyActivity_RankRewardUI";
        public static string DiyActivityHint = "DiyActivityHint";
        public static string UAVActivityMain = "UAVActivityMain";
        public static string UAVActivityHint = "UAVActivityHint";

        #region NewUI
        public static string UICitiaoGainTips = "UICitiaoGainTips";
        public static string FightUI = "FightUI";
        public static string UIFightCAmainView = "UIFightCAmainView";
        public static string FightCommonUI = "FightCommonUI";
        public static string BattleBuffNew = "BattleBuffNew";
        public static string BeHitted = "BeHitted";
        public static string UIFightPauseView = "UIFightPauseView";
        public static string ArenaKillUI = "ArenaKillUI";
        public static string KFArenaKillUI = "KFArenaKillUI";
        public static string UImagnet = "UImagnet";
        public static string MechaSamples = "MechaSamples";
        public static string UIFightGoldStone = "UIFightGoldStone";
        public static string UIBattleExpGuid = "BattleExpGuid";
        public static string UIFightReviveView = "UIFightReviveView";
        public static string UIFightCitiaoView = "UIFightCitiaoView";
        public static string UIFightCitiaoDebugView = "UIFightCitiaoDebugView";
        public static string UIFightCitiao4View = "UIFightCitiao4View";
        public static string UIFightEliteCitiaoView = "UIFightEliteCitiaoView";
        public static string UIFightRelateCitiaoView = "UIFightRelateCitiaoView";
        public static string UIFightRankCitiaoView = "UIFightRankCitiaoView";
        public static string WaveWarningView = "UIWaveWarningView";
        public static string WaveWarningGuidView = "UIWaveWarningGuidView";
        public static string UIWaveWarningtuwei = "UIWaveWarningtuwei";
        public static string BossWarningView = "UIBossWarningView";
        public static string UIStartTowerTaskModeUI = "UIStartTowerTaskModeUI";
        public static string UISucessTowerTaskModeUI = "UISucessTowerTaskModeUI";
        public static string UIHelpSearch = "UIHelpSearch";


        public static string ExpeditionSpecialBossUI = "ExpeditionSpecialBossUI";
        public static string UIStartTimeTaskModeUI = "UIStartTimeTaskModeUI";
        public static string UIStartTimeTaskTacticModeUI = "UIStartTimeTaskTacticModeUI";
        public static string UIStartGoldTaskModeUI = "UIStartGoldTaskModeUI";

        public static string UIFightTeamChat = "UIFightTeamChat";
        public static string UIFightTeamDamageDetail = "UIFightTeamDamageDetail";
        public static string FightBreakoutBossAniUI = "FightBreakoutBossAniUI";
        // 跨服竞技场 伤害详情
        public static string CSarenaStatisticsUI = "CSarenaStatisticsUI";

        #endregion

        public static string NormalUp = "NormalUp";
        #region confirm
        // 战斗confirm
        // 普通confirm
        public static string NormalConfirmUI = "ConfirmUI";
        // 登录失败 或者 服务器list 拉取失败的
        public static string LoginErrorConfirm = "LoginErrorConfirm";
        public static string LoginErrorConfirmTeam = "LoginErrorConfirmTeam";
        public static string KickOutConfirm = "KickOutConfirm";
        public static string UpConfirm = "UpConfirm";
        #endregion
        public static string PveWaitUI = "PveWaitUI";

        //每日任务
        public static string QuestUI = "QuestUI";
        //福利  28天签到
        public static string WelfareUI = "WelfareUI";
        // 7 14 签到
        public static string StartAwardUI = "StartAwardUI";
        //七天乐
        public static string ChallengeTaskUI = "ChallengeTaskUI";
        // UIDialog

        public static string UIDialog = "UIDialog";

        //机甲升级礼包
        public static string MechaLvPackMain = "MechaLvPackMain";

        // 新手引导
        public static string GuideUI = "GuideUI";

        #region Expedition

        public static string ExchangeShopUI = "ExchangeShopUI";
        public static string ConfirmBuyUI = "ConfirmBuyUI";
        public static string MessageBox_ExchangeShop_PiLiangGouMai = "MessageBox_ExchangeShop_PiLiangGouMai";
        public static string ExpeditionKnapsackUI = "ExpeditionKnapsackUI";
        public static string ExpeditionCollectUI = "ExpeditionCollectUI";
        public static string ExpeditionMainUI = "ExpeditionMainUI";
        public static string ExpeditionItemTips = "Expedition_Item_Tips";
        public static string ExpeditionBaoWuUI = "ExpeditionBaoWuUI";
        public static string ExpeditionBossDifficultyUI = "ExpeditionBossDifficultyUI";
        public static string ExpeditionBuJiUI = "ExpeditionBuJiUI";
        public static string ExpeditionBWTCQ = "ExpeditionBWTCQ";
        public static string ExpeditionCongratulationsUI = "ExpeditionCongratulationsUI";
        public static string ExpeditionRemindUI = "ExpeditionRemindUI";
        public static string ExpeditionShiJianUI = "ExpeditionShiJianUI";
        public static string ExpeditionZhanDouUI = "ExpeditionZhanDouUI";
        public static string ExpeditionTurntableUI = "ExpeditionTurntableUI";
        public static string ExpeditionCiTiaoView = "ExpeditionCiTiaoView";
        public static string ExpeditionEliteCitiaoView = "ExpeditionEliteCitiaoView";
        public static string ExpeditionGiveCiTiaoView = "ExpeditionGiveCiTiaoView";
        public static string ExpeditionViewUI = "ExpeditinViewUI";

        #endregion

        //特惠
        public static string SuperValueActUI = "SuperValueActUI";
        public static string VipPrivilegeUpgradeUI = "VipPrivilegeUpgradeUI";
        public static string QuickRechargeUI = "QuickRechargeUI";

        // 机甲强化
        public static string Enhancement_mainUI = "Enhancement_mainUI";
        public static string Enhancement_LevelUpUI = "Enhancement_LevelUpUI";
        public static string Enhancement_Overview = "Enhancement_Overview";
        public static string Enhancement_unlockUI = "Enhancement_unlockUI";

        public static string DoubleRewardUI = "DoubleRewardUI";
        public static string NotEnergyUI = "NotEnergyUI";

        // 好友
        public static string FriendUI = "FriendUI";
        public static string FriendUI_BlackList_MessageBox = "FriendUI_BlackList_MessageBox";
        public static string FriendUI_AddList_MessageBox = "FriendUI_AddList_MessageBox";
        public static string DeleteFriendUI = "DeleteFriendUI";


        //战力活动
        public static string CombatTarget_Main = "CombatTarget_Main";
        public static string CombatTarget_Hint = "CombatTarget_Hint";

        //聊天
        public static string ChatUI = "ChatUI";
        public static string ViewOthersUI = "ViewOthersUI";
        public static string ChatEmoji_MessageBoxUI = "ChatEmoji_MessageBoxUI";

        //赛季
        public static string SeasonUI = "SeasonUI";
        public static string SeasonBattlePassBuyUI = "SeasonBattlePassBuyUI";
        public static string Season_unlcok = "Season_unlcok";
        public static string Season_Turntable = "Season_Turntable";
        public static string SeasonDFPreViewUI = "SeasonDFPreViewUI";
        public static string Season_BoxUI = "Season_BoxUI";
        public static string SeasonBuffUI = "SeasonBuffUI";
        public static string Season_poltUI = "Season_poltUI";
        public static string Season_skipPassUI = "Season_skipPassUI";
        public static string SeasonPeakMatchFinishUI = "SeasonPeakMatchFinishUI";
        public static string Season_DianFengPL = "Season_DianFengPL";

        //触发礼包
        public static string TriggerGift_Main = "TriggerGift_Main";
        public static string TriggerGift_Hint = "TriggerGift_Hint";

        // 公会
        public static string GuildMainUI = "GuildMainUI";
        public static string AllGuildListUI = "AllGuildListUI";
        public static string AddGuildUI = "AddGuildUI";
        public static string GuildInfoUI = "GuildInfoUI";
        public static string GuildLogUI = "GuildLogUI";
        public static string LeaveGuild_MessageBoxUI = "LeaveGuild_MessageBoxUI";
        public static string ExitGuild_MessageBoxUI = "ExitGuild_MessageBoxUI";
        public static string GuildManagementUI = "GuildManagementUI";
        public static string GuildDonationUI = "GuildDonationUI";
        public static string GuildDonationLogUI = "GuildDonationLogUI";
        public static string GuildBargainUI = "GuildBargainUI";
        public static string Bargain_MessageBoxUI = "Bargain_MessageBoxUI";
        public static string GuildBossUI = "GuildBossUI";
        public static string GuildBossHurtRankUI = "GuildBossHurtRankUI";
        public static string GuildBossTaskUI = "GuildBossTaskUI";
        public static string GuildUpgradeUI = "GuildUpgradeUI";
        public static string ViewGuildInfoUI = "ViewGuildInfoUI";
        public static string GuildDeclaration_MessageBoxUI = "GuildDeclaration_MessageBoxUI";
        public static string GuildLeagueMainUI = "GuildLeagueMainUI";
        public static string GuildLeagueRankUI = "GuildLeagueRankUI";
        public static string GuildLeagueBattleListUI = "GuildLeagueBattleListUI";
        public static string GuildLeagueLineupUI = "GuildLeagueLineupUI";
        public static string GuildLeagueFormatUI = "GuildLeagueFormatUI";
        public static string GuildLeagueRescueUI = "GuildLeagueRescueUI";
        public static string GuildLeagueOffensiveLineupUI = "GuildLeagueOffensiveLineupUI";


        public static string UseCrystals_MessageBoxUI = "UseCrystals_MessageBoxUI";
        // 服务器
        public static string Privacy_MessageBox01 = "Privacy_MessageBox01";
        public static string Privacy_MessageBox02 = "Privacy_MessageBox02";
        #region 营地

        public static string ShieldFightPowerUI = "ShieldFightPowerUI";
        public static string PreventPerformanceUI = "PreventPerformanceUI";

        public static string CampsiteRootUI = "CampsiteRootUI";
        public static string CampsiteHouseMainUI = "CampsiteHouseMainUI";
        public static string CampsiteTowerMainUI = "CampsiteTowerMainUI";
        public static string DriverEquipUI = "DriverEquipUI";
        public static string SurvivorUpgradeUI = "SurvivorUpgradeUI";
        public static string SurvivorLevelUpResultUI = "SurvivorLevelUpResultUI";
        public static string SurvivorTips = "SurvivorTips";
        public static string DriverTips = "DriverTips";
        public static string DriverUpgradeUI = "DriverUpgradeUI";
        public static string DriverUpResultUI = "DriverUpResultUI";
        public static string NewDriverUI = "NewDriverUI";

        #endregion


        //周期活动
        public static string ActivityUI = "ActivityUI";
        public static string ActivityBattlePassUI = "ActivityBattlePassUI";
        public static string ActivityRankRewardUI = "ActivityRankRewardUI";
        public static string AeroplaneStartUI = "AeroplaneStartUI";
        public static string Slots_ProbabilityUI = "Slots_ProbabilityUI";
        public static string Slots_StartUI = "Slots_StartUI";
        public static string NinjaTrailStartUI = "NinjaTrailStartUI";
        public static string MechaReissueStartUI = "MechaReissueStartUI";
        public static string MechaTreasureStartUI = "MechaTreasureStartUI";
        public static string DeepExploreStartUI = "DeepExploreStartUI";
        public static string RouletteStartUI = "RouletteStartUI";
        public static string RouletteSpecialUI = "RouletteSpecialUI";
        public static string DeepExploreShopUI = "DeepExploreShopUI";
        public static string DeepExploreSellUI = "DeepExploreSellUI";
        public static string DeepExploreExchangeUI = "DeepExploreExchangeUI";
        public static string GameInfoUI = "GameInfoUI";

        //功能预告
        public static string PreviewReceiveUI = "PreviewReceiveUI";
        public static string PreviewBuyGiftUI = "PreviewBuyGiftUI";
        public static string PreviewCommonGiftUI = "PreviewCommonGiftUI";
        public static string PreviewStorageGiftUI = "PreviewStorageGiftUI";
        public static string PreviewBuy_MessageBoxUI = "PreviewBuy_MessageBoxUI";

        //通用兑换
        public static string ActivityExchangeUI = "ActivityExchangeUI";
        public static string CircleActivityExchangeUI = "CircleActivityExchangeUI";

        //集字活动
        public static string GiftMainUI = "GiftMainUI";
        public static string CollectActivityUI = "CollectActivityUI";
        public static string GiftLogUI = "GiftLogUI";
        public static string CollectAdUI = "CollectAdUI";
        public static string ReceivedGiftUI = "ReceivedGiftUI";

        public static string LongPressTipsUI = "LongPressTipsUI";

        // 机甲援助
        public static string AssistMainUI = "AssistMainUI";
        public static string AssistSkillPreviewUI = "AssistSkillPreviewUI";
        public static string AssistMechaListUI = "AssistMechaListUI";

        // 竞技场
        public static string ArenaMainUI = "ArenaMainUI";
        public static string ArenaRankUI = "ArenaRankUI";
        public static string ArenaLineupUI = "ArenaLineupUI";
        public static string OtherLineupUI = "OtherLineupUI";
        public static string ArenaBattleLogUI = "ArenaBattleLogUI";
        public static string ArenaMechaDiyUI = "ArenaMechaDiyUI";
        public static string MessageBox_ArenaNumUI = "MessageBox_ArenaNumUI";
        public static string ArenaRandomCiTiao = "ArenaRandomCiTiao";

        // 组队
        public static string TeamInstanceMainUI = "TeamInstanceMainUI";
        public static string TeamInviteUI = "TeamInviteUI";
        public static string TeamBeInviteListUI = "TeamBeInviteListUI";
        public static string TeamInviteListUI = "TeamInviteListUI";
        public static string TeamBeInviteTaskUI = "TeamBeInviteTaskUI";
        public static string TeamAssistUI = "TeamAssistUI";

        //装备
        public static string EquipmentMainUI = "EquipmentMainUI";
        public static string EquipmentAttrUI = "EquipmentAttrUI";
        public static string EquipmentInfoAUI = "EquipmentInfoAUI";
        public static string EquipmentInfoBUI = "EquipmentInfoBUI";
        public static string EquipmentBreakDownUI = "EquipmentBreakDownUI";
        public static string EquipmentCreateUI = "EquipmentCreateUI";
        public static string EquipmentWeaponUI = "EquipmentWeaponUI";
        public static string EquipmentSlotUI = "EquipmentSlotUI";
        public static string EquipmentLvUpUI = "EquipmentLvUpUI";
        public static string EquipmentAutoScrap = "EquipmentAutoScrap";
        public static string EquipmentReforgeUI = "EquipmentReforgeUI";

        //限时累充
        public static string TimeLimitedRechargeUI = "TimeLimitedRechargeUI";
        public static string TimeLimitedRechargeStartUI= "TimeLimitedRechargeStartUI";

        //战力周期活动
        public static string CombatPower_MainUI = "CombatPower_MainUI";
        public static string CombatPower_StartUI = "CombatPower_StartUI";
        public static string CombatPower_egg = "CombatPower_egg";
        // 跨服竞技场
        public static string CSarenaMainUI = "CSarenaMainUI";
        public static string CSarenaListUI = "CSarenaListUI";
        public static string CSarenaRankRewardUI = "CSarenaRankRewardUI";
        public static string CSarenaRecordUI = "CSarenaRecordUI";
        public static string CSarenaRankUpUI = "CSarenaRankUpUI";
        public static string MessageBox_CSarenaNumUI = "MessageBox_CSarenaNumUI";

        //跨服竞技场编队
        public static string CSarenaLineupUI = "CSarenaLineupUI";
        public static string CSarenaAircraftUI = "CSarenaAircraftUI";


        //机甲平台
        public static string AircraftMainUI = "AircraftMainUI";
        public static string AircraftReforgeListUI = "AircraftReforgeListUI";
        public static string AircraftListMainUI = "AircraftListMainUI";
        public static string AircraftRestoreUI = "AircraftRestoreUI";
        public static string AircraftReforgeLvUpUI = "AircraftReforgeLvUpUI";
        public static string AircraftUpgradeUI = "AircraftUpgradeUI";
        public static string AircraftUpgradeResultUI = "AircraftUpgradeResultUI";
        public static string AircraftGainedUI = "AircraftGainedUI";
        public static string AircraftLuckUpgradeResultUI = "AircraftLuckUpgradeResultUI";
        public static string AircraftPowerUp_MessageBoxUI = "AircraftPowerUp_MessageBoxUI";
        public static string AircraftPowerDown_MessageBoxUI = "AircraftPowerDown_MessageBoxUI";

        //战术特训
        public static string TacticStartUI = "TacticStartUI";
        public static string TacticMainUI = "TacticMainUI";
        public static string Tactic_MessageBoxUI = "Tactic_MessageBoxUI";
        public static string TacticRankUI = "TacticRankUI";
        public static string TacticBattleDetailsUI = "TacticBattleDetailsUI";
        
        
        public static string OreMainUI = "OreMainUI";
        
        // 用于各种表现的UI 各种获得UI  升级  解锁 平级  互斥
        public static HashSet<string> PerformanceUIs = new HashSet<string>
        {
            GetRewardUI,
            SystemUnlockUI,
            RoleLevelUpUI,
            MechaGainedUI,
            AircraftGainedUI,
            MechaSkinGainedUI,
            GetNewWeaponUI,
            GetNewUAVUI,
            VipPrivilegeUpgradeUI,
            BindAccountUI,
            DoubleRewardUI,
            NotEnergyUI,
            Season_unlcok,
            Season_poltUI,
            NewDriverUI,
            ContinueFighting_MessageBox,
            PartSkinGainedUI,
            EquipmentInfoAUI,
            EquipmentInfoBUI,
            PreventPerformanceUI
        };

        // 主动弹窗功能互斥
        public static HashSet<string> PerformaceFunctionUIs = new HashSet<string>()
        {
            StartAwardUI,
            WelfareUI,
            FirstRechargeUI,
            Season_unlcok,
            DIYPackUI,
            TriggerGift_Hint,
            AeroplaneStartUI,
            Slots_StartUI,
            NinjaTrailStartUI,
            MechaReissueStartUI,
            MechaTreasureStartUI,
            DeepExploreStartUI,
            RouletteStartUI,
            BattlePassBuyPublicize,
            TimeLimitedRechargeStartUI,
            CombatPower_StartUI,
            TacticStartUI
        };

        // 不算顶层UI
        public static HashSet<string> IgnoreTopUIs = new HashSet<string>
        {
            NormalConfirmUI,
            LoadingUI,
            ArenaLoadingUI,
            LeagueLoadingUI,
            LoginErrorConfirm,
            LoginErrorConfirmTeam,
            KickOutConfirm,
            UpConfirm,
            PoorNetworkUI,
            PowerUI,
            FlyItem,
            NormalUp,
            TeamInviteUI,
            ChatEmoji_MessageBoxUI,
            PreventPerformanceUI
        };

        // 不算新手引导顶层UI
        public static HashSet<string> IgnoreGuideTopUIs = new HashSet<string>
        {
            GuideUI,
            NormalConfirmUI,
            PoorNetworkUI,
            UpConfirm,
            LoginErrorConfirm,
            LoginErrorConfirmTeam,
            KickOutConfirm,
            PowerUI,
            RoleLevelUpUI,
            ChatEmoji_MessageBoxUI,
            PreventPerformanceUI
        };

        // 在这些UI打开的时候 阻止战力UI弹出
        public static HashSet<string> PreventPowerUIs = new HashSet<string>
        {
            GetRewardUI,
            MechaGainedUI,
            AircraftGainedUI,
            MechaUpgradeResultUI,
            AircraftUpgradeResultUI,
            MechaSkinGainedUI,
            MechaSkinUpgradeResultUI,
            PartRankUpResultUI,
            GetNewWeaponUI,
            GetNewUAVUI,
            BuJian_Roll_UI,
            UAV_Roll_UI,
            GuideUI,
            MechaSpinMain,
            UAVUpgradeResultUI,
            Enhancement_unlockUI,
            ShieldFightPowerUI,
            SurvivorLevelUpResultUI,
            Enhancement_LevelUpUI,
            PartSkinGainedUI,
            PartSkinUpgradeResultUI,
        };

        // 关闭时不广播UIClose
        public static HashSet<string> IgnoreCloseBroadcastUIs = new HashSet<string>
        {
            GMUI,
            GMItemUI,
            TestUI,
            UpConfirm,
            NormalConfirmUI,
            LoginErrorConfirm,
            LoginErrorConfirmTeam,
            KickOutConfirm,
            PoorNetworkUI,
            PveWaitUI,
            LoginUI,
            LoadingUI,
            ArenaLoadingUI,
            LeagueLoadingUI,
            Box_Tips,
            Box_Tips2,
            Item_Tips,
            Compose_Tips,
            Mech_Tips,
            DIYPart_Tips,
            Top_Item_Tips,
            PowerUI,
            ChatEmoji_MessageBoxUI,
            PreventPerformanceUI
        };

        public static Dictionary<string, WndUICfg> gUIInfo = new Dictionary<string, WndUICfg>()
        {
            {GMUI,new WndUICfg(){ResPath = "PrefabsN/UI/Common/GMUI",NoTouchMask = true,MaskBlack = true,TouchEmptyClose = true}},
            {GMItemUI,new WndUICfg(){ResPath = "PrefabsN/UI/Common/GMItemUI",NoTouchMask = true,MaskBlack = true,TouchEmptyClose = true}},
            {TestUI,new WndUICfg(){ResPath = "PrefabsN/UI/Common/TestUI",MaskBlack = true,TouchEmptyClose = true}},

            {CommonUI,new WndUICfg(){ResPath = "PrefabsN/UI/Main/CommonUI",NoTouchMask = true,UILevel = 1,AutoOrder = false,OnUICloseListener = new List<string>()}},
            {ShopUI,new WndUICfg(){ResPath = "PrefabsN/UI/Shop/ShopUI",NoTouchMask = true,UILevel = 1,AutoOrder = false,ShowCurrencyTab = CurrencyTabUI.Shop,ShowMenuTab = true,OnUICloseListener = new List<string>{ QuickBuyDiamondUI,GetRewardUI} }},
            {BuJian_Roll_UI,new WndUICfg(){ResPath = "PrefabsN/UI/Shop/BuJian_Roll_UI", MaskBlack = true, TouchEmptyClose = true, OnUICloseListener = new List<string>(){LDUICfg.GetNewWeaponUI,LDUICfg.GetNewUAVUI,LDUICfg.MechaGainedUI,LDUICfg.MechaSkinGainedUI,LDUICfg.PartSkinGainedUI},ShowCurrencyTab = CurrencyTabUI.DiyPartRollShopShow}},
            {UAV_Roll_UI,new WndUICfg(){ResPath = "PrefabsN/UI/Shop/UAV_Roll_UI", MaskBlack = true, TouchEmptyClose = true, OnUICloseListener = new List<string>(){LDUICfg.GetNewWeaponUI,LDUICfg.GetNewUAVUI,LDUICfg.MechaGainedUI,LDUICfg.MechaSkinGainedUI,LDUICfg.PartSkinGainedUI},ShowCurrencyTab = CurrencyTabUI.UAVRollShopShow}},
            {UAVRollTips,new WndUICfg(){ResPath = "PrefabsN/UI/Shop/UAVRollTips", MaskBlack = true, TouchEmptyClose = true}},
            {DriverRollTips,new WndUICfg(){ResPath = "PrefabsN/UI/Shop/DriverRollTips", MaskBlack = true, TouchEmptyClose = true}},
            {BuJianRollTipsUI,new WndUICfg(){ResPath = "PrefabsN/UI/Shop/BuJianRollTips", MaskBlack = true, TouchEmptyClose = true}},
            {MechaMainUI,new WndUICfg(){ResPath = "PrefabsN/UI/Mecha/MechaMainUI",UILevel = 1,AutoOrder = false,ShowCurrencyTab = CurrencyTabUI.Mecha,ShowMenuTab = true,OnUICloseListener = new List<string>{ MessageBox_KuaiSuHuoQu, MechaListMainUI } }},
            {MainUI,new WndUICfg(){ResPath = "PrefabsN/UI/Main/NMainUi",NoTouchMask = true,UILevel = 1,AutoOrder = false,ShowCurrencyTab = CurrencyTabUI.Main,ShowMenuTab = true,OnUICloseListener = new List<string>{ LDUICfg.RoleLevelUpUI, LDUICfg.SystemUnlockUI,LDUICfg.Season_poltUI}}},
            // {GamePlayRootUI,new WndUICfg(){ResPath = "PrefabsN/UI/GamePlay/GamePlayRootUI",NoTouchMask = true,UILevel = 1,AutoOrder = false,ShowCurrencyTab = CurrencyTabUI.Home,ShowMenuTab = true}},
            {GamePlayRootUI,new WndUICfg(){ResPath = "PrefabsN/UI/GamePlay/GamePlayRootUI",NoTouchMask = true,AutoOrder = true,ShowCurrencyTab = CurrencyTabUI.Campsite}},
            {AppPingJia,new WndUICfg(){ResPath = "PrefabsN/UI/Main/AppPingJia", MaskBlack = true, TouchEmptyCloseDelayTime = 1f}},
            {ContinueFighting_MessageBox,new WndUICfg(){ResPath = "PrefabsN/UI/Main/ContinueFighting_MessageBox", MaskBlack = true,TouchEmptyClose = false}},

            {LogoUI,new WndUICfg(){ResPath = "PrefabsN/UI/Logo/LogoUi",UnRespondEscape = true}},
            // Confirm
            {NormalConfirmUI,new WndUICfg(){ResPath = "PrefabsN/UI/Common/ConfirmUI",MaskBlack  = true, AutoUnload = false}},
            {UpConfirm,new WndUICfg(){ResPath = "PrefabsN/UI/Common/UpConfirm",MaskBlack  = true,AutoOrder = false}},
            {KickOutConfirm,new WndUICfg(){ResPath = "PrefabsN/UI/Common/ConfirmUI",MaskBlack  = true,AutoOrder = false, AutoUnload = false}},
            {LoginErrorConfirm,new WndUICfg(){ResPath = "PrefabsN/UI/Common/NetErrorConfirm",MaskBlack  = true,AutoOrder = false}},
            {LoginErrorConfirmTeam,new WndUICfg(){ResPath = "PrefabsN/UI/Common/NetErrorConfirm",MaskBlack  = true,AutoOrder = false}},
            {PoorNetworkUI,new WndUICfg(){ResPath = "PrefabsN/UI/Resident/PoorNetworkUI",MaskBlack = false}},

            //pve
            {PveWaitUI,new WndUICfg(){ResPath = "PrefabsN/UI/Pve/PveWaitUI",MaskBlack  = true}},

            // 常驻
            {ServerChooseUI,new WndUICfg(){ResPath = "PrefabsN/UI/ServerChooseUI/ServerChooseUI",MaskBlack = true,ResSceneType = ResSceneType.NormalRes}},
            {LoadingUI,new WndUICfg(){ResPath = "PrefabsN/UI/Resident/LoadingUI",AutoOrder = false,ResSceneType = ResSceneType.Resident}},


            {LoginUI,new WndUICfg(){ResPath = "PrefabsN/UI/Login/LoginUI",ResSceneType = ResSceneType.NormalRes}},
            //mainaUI
            ///===========1111=battleUI====================
            {FightUI,new WndUICfg(){ResPath = "PrefabsN/UI/Fight/UIFightView",NoTouchMask = true,ResSceneType = ResSceneType.FightUI }},
            {UIFightCAmainView,new WndUICfg(){ResPath = "PrefabsN/UI/Fight/UIFightCAmainView",NoTouchMask = true,ResSceneType = ResSceneType.FightUI }},
            {FightCommonUI,new WndUICfg(){ResPath = "PrefabsN/UI/Fight/FightCommonUI",NoTouchMask = true,ResSceneType = ResSceneType.FightUI }},
            {BattleBuffNew,new WndUICfg(){ResPath = "PrefabsN/UI/Fight/BattleBuffNew",ResSceneType = ResSceneType.FightUI }},
            {BeHitted,new WndUICfg(){ResPath = "PrefabsN/UI/Fight/UIBeHittedView",NoTouchMask = true,ResSceneType = ResSceneType.FightUI}},
            {WaveWarningView,new WndUICfg(){ResPath = "PrefabsN/UI/Fight/UIWaveWarningView",NoTouchMask = true,ResSceneType = ResSceneType.FightUI,AudioClip = AudioConfig.Warning_Clip}},
            {WaveWarningGuidView,new WndUICfg(){ResPath = "PrefabsN/UI/Fight/UIWaveWarningGuidView",NoTouchMask = true,ResSceneType = ResSceneType.FightUI,AudioClip = AudioConfig.Warning_Clip,AudiuClipDelayTime = 2}},
            {UIWaveWarningtuwei,new WndUICfg(){ResPath = "PrefabsN/UI/Fight/UIWaveWarningtuwei",NoTouchMask = true,ResSceneType = ResSceneType.FightUI,AudioClip = AudioConfig.Warning_Clip,AudiuClipDelayTime = 2}},
            {BossWarningView,new WndUICfg(){ResPath = "PrefabsN/UI/Fight/UIBossWarningView",NoTouchMask = true,ResSceneType = ResSceneType.FightUI,AudioClip = AudioConfig.Warning_Clip2}},
            {UIFightPauseView,new WndUICfg(){ResPath = "PrefabsN/UI/Fight/UIFightPauseView",MaskBlack = true,ResSceneType = ResSceneType.FightUI}},
            {ArenaKillUI,new WndUICfg(){ResPath = "PrefabsN/UI/Fight/ArenaKillUI",ResSceneType = ResSceneType.FightUI}},
            {KFArenaKillUI,new WndUICfg(){ResPath = "PrefabsN/UI/Fight/KFArenaKillUI",ResSceneType = ResSceneType.FightUI,NoTouchMask = true}},
            {UImagnet,new WndUICfg(){ResPath = "PrefabsN/UI/Fight/UImagnet",MaskBlack = true,ResSceneType = ResSceneType.FightUI,TouchEmptyClose = true,OnUICloseListener = new List<string>(){ LDUICfg.MonthlyCardUI } }},
            {MechaSamples,new WndUICfg(){ResPath = "PrefabsN/UI/Fight/MechaSamples",MaskBlack = true,ResSceneType = ResSceneType.FightUI,TouchEmptyClose = true}},
            {UIBattleExpGuid,new WndUICfg(){ResPath = "PrefabsN/UI/Tutorial/BattleExpGuid",NoTouchMask = true,ResSceneType = ResSceneType.FightUI}},
            {UIFightReviveView,new WndUICfg(){ResPath = "PrefabsN/UI/Fight/UIFightReviveView",MaskBlack = true,OnUICloseListener = new List<string>(){ LDUICfg.QuickBuyDiamondUI},ResSceneType = ResSceneType.FightUI}},
            {UIFightGoldStone,new WndUICfg(){ResPath = "PrefabsN/UI/FightEndUI/CoinPass/UIFightGoldStone",NoTouchMask = true,OnUICloseListener = new List<string>(){ LDUICfg.GetRewardUI},ResSceneType = ResSceneType.FightUI}},
            {UIFightCitiaoView,new WndUICfg(){ResPath = "PrefabsN/UI/Fight/UIFightCitiaoView",MaskBlack = true,ResSceneType = ResSceneType.FightUI}},
            {UIFightCitiaoDebugView,new WndUICfg(){ResPath = "PrefabsN/UI/Fight/UIFightCitiaoDebugView",MaskBlack = true,TouchEmptyClose = true,ResSceneType = ResSceneType.FightUI}},
            {UIFightCitiao4View,new WndUICfg(){ResPath = "PrefabsN/UI/FightEndUI/CoinPass/UIFightCitiao4View",MaskBlack = true,ResSceneType = ResSceneType.FightUI}},
            {UIFightEliteCitiaoView,new WndUICfg(){ResPath = "PrefabsN/UI/Fight/UIFightEliteCitiaoView",MaskBlack = true,ResSceneType = ResSceneType.FightUI,AudioClip = AudioConfig.Ui_ecitiao}},
            {UIFightRelateCitiaoView,new WndUICfg(){ResPath = "PrefabsN/UI/Fight/UIFightRelateCitiaoView",MaskBlack = true,ResSceneType = ResSceneType.FightUI}},
            {UIFightRankCitiaoView,new WndUICfg(){ResPath = "PrefabsN/UI/Fight/UIFightRankCitiaoView",MaskBlack = true,ResSceneType = ResSceneType.FightUI}},
            {UIStartTowerTaskModeUI,new WndUICfg(){ResPath = "PrefabsN/UI/Fight/UIStartTowerTaskModeUI",TouchEmptyClose = true,TouchEmptyCloseDelayTime = 1.5f,ResSceneType = ResSceneType.FightUI}},
            {UISucessTowerTaskModeUI,new WndUICfg(){ResPath = "PrefabsN/UI/Fight/UISucessTowerTaskModeUI",NoTouchMask = true,ResSceneType = ResSceneType.FightUI}},
            {UIHelpSearch,new WndUICfg(){ResPath = "PrefabsN/UI/Fight/UIHelpSearch",NoTouchMask = true,ResSceneType = ResSceneType.FightUI}},

            {ExpeditionSpecialBossUI,new WndUICfg(){ResPath = "PrefabsN/UI/GamePlay/Expedition/ExpeditionSpecialBossUI",NoTouchMask = true,ResSceneType = ResSceneType.FightUI}},
            {UIStartTimeTaskModeUI,new WndUICfg(){ResPath = "PrefabsN/UI/Fight/UIStartTimeTaskModeUI",NoTouchMask = true,ResSceneType = ResSceneType.FightUI}},

            {UIStartGoldTaskModeUI,new WndUICfg(){ResPath = "PrefabsN/UI/FightEndUI/CoinPass/UIStartGoldTaskModeUI",NoTouchMask = true,ResSceneType = ResSceneType.FightUI}},
            {UIStartTimeTaskTacticModeUI,new WndUICfg(){ResPath = "PrefabsN/UI/Fight/UIStartTimeTaskTacticModeUI",NoTouchMask = true,ResSceneType = ResSceneType.FightUI}},

            {NormalUp,new WndUICfg(){ResPath = "PrefabsN/UI/Common/NormalUp",NoTouchMask = true,ResSceneType = ResSceneType.NormalUI,AudioClip = AudioConfig.LvUp_Sucess}},
            {UICitiaoGainTips,new WndUICfg(){ResPath = "PrefabsN/UI/Fight/UICitiaoGainTips",MaskBlack = true,TouchEmptyClose = true}},

            {UIFightTeamChat,new WndUICfg(){ResPath = "PrefabsN/UI/Fight/UIFightTeamChat",MaskBlack = false,ResSceneType = ResSceneType.FightUI, NoTouchMask = true}},
            {UIFightTeamDamageDetail,new WndUICfg(){ResPath = "PrefabsN/UI/Fight/UIFightTeamDamageDetail",MaskBlack = false,ResSceneType = ResSceneType.FightUI, NoTouchMask = true}},
            {FightBreakoutBossAniUI,new WndUICfg(){ResPath = "PrefabsN/UI/Fight/FightBreakoutBossAniUI",MaskBlack = false,ResSceneType = ResSceneType.FightUI, NoTouchMask = true}},
            {CSarenaStatisticsUI,new WndUICfg(){ResPath = "PrefabsN/UI/Fight/CSarenaStatisticsUI",MaskBlack = true,ResSceneType = ResSceneType.FightUI, TouchEmptyClose = true}},

            ///=============22222 ===================

            // 战斗结算
            {MainFightLoseUI,new WndUICfg(){ResPath = "PrefabsN/UI/FightEndUI/MainPass/MainFightLoseUI",ResSceneType = ResSceneType.FightUI,AudioClip = AudioConfig.Common_fail}},
            {MainFightWinUI,new WndUICfg(){ResPath = "PrefabsN/UI/FightEndUI/MainPass/MainFightWinUI",ResSceneType = ResSceneType.FightUI,AudioClip = AudioConfig.Common_win }},

            {PveTowerFightLoseUI,new WndUICfg(){ResPath = "PrefabsN/UI/FightEndUI/PveTower/PveTowerFightLoseUI",ResSceneType = ResSceneType.FightUI,AudioClip = AudioConfig.Common_fail}},
            {PveTowerFightWinUI,new WndUICfg(){ResPath = "PrefabsN/UI/FightEndUI/PveTower/PveTowerFightWinUI",ResSceneType = ResSceneType.FightUI,AudioClip = AudioConfig.Common_win }},
            {CoinPassFightLoseUI,new WndUICfg(){ResPath = "PrefabsN/UI/FightEndUI/CoinPass/CoinPassFightLoseUI",ResSceneType = ResSceneType.FightUI,AudioClip = AudioConfig.Common_fail}},
            {CoinPassFightWinUI,new WndUICfg(){ResPath = "PrefabsN/UI/FightEndUI/CoinPass/CoinPassFightWinUI",ResSceneType = ResSceneType.FightUI ,AudioClip = AudioConfig.Common_win}},
            {ExpeditionFightLoseUI,new WndUICfg(){ResPath = "PrefabsN/UI/FightEndUI/Expedition/ExpeditionFightLoseUI",ResSceneType = ResSceneType.FightUI,AudioClip = AudioConfig.Common_fail}},
            {ExpeditionFightWinUI,new WndUICfg(){ResPath = "PrefabsN/UI/FightEndUI/Expedition/ExpeditionFightWinUI",ResSceneType = ResSceneType.FightUI,AudioClip = AudioConfig.Common_win }},
            {PveFightLoseUI,new WndUICfg(){ResPath = "PrefabsN/UI/FightEndUI/PveTeam/PveFightLoseUI",ResSceneType = ResSceneType.FightUI,AudioClip = AudioConfig.Common_fail}},
            {PveFightWinUI,new WndUICfg(){ResPath = "PrefabsN/UI/FightEndUI/PveTeam/PveFightWinUI",ResSceneType = ResSceneType.FightUI,AudioClip = AudioConfig.Common_win }},
            {SeasonFightLoseUI,new WndUICfg(){ResPath = "PrefabsN/UI/FightEndUI/Season/SeasonFightLoseUI",ResSceneType = ResSceneType.FightUI,AudioClip = AudioConfig.Common_fail }},
            {SeasonFightWinUI,new WndUICfg(){ResPath = "PrefabsN/UI/FightEndUI/Season/SeasonFightWinUI",ResSceneType = ResSceneType.FightUI,AudioClip = AudioConfig.Common_win }},
            {GuildFightLoseUI,new WndUICfg(){ResPath = "PrefabsN/UI/FightEndUI/Guild/GuildFightLoseUI",ResSceneType = ResSceneType.FightUI,AudioClip = AudioConfig.Common_fail }},
            {GuildFightWinUI,new WndUICfg(){ResPath = "PrefabsN/UI/FightEndUI/Guild/GuildFightWinUI",ResSceneType = ResSceneType.FightUI,AudioClip = AudioConfig.Common_win }},
            {ArenaFightLoseUI,new WndUICfg(){ResPath = "PrefabsN/UI/FightEndUI/Arena/ArenaFightLoseUI",ResSceneType = ResSceneType.FightUI,AudioClip = AudioConfig.Common_fail }},
            {ArenaFightWinUI,new WndUICfg(){ResPath = "PrefabsN/UI/FightEndUI/Arena/ArenaFightWinUI",ResSceneType = ResSceneType.FightUI,AudioClip = AudioConfig.Common_win }},
            {GuildLeagueFightLoseUI,new WndUICfg(){ResPath = "PrefabsN/UI/FightEndUI/GuildLeague/GuildLeagueFightLoseUI",ResSceneType = ResSceneType.FightUI,AudioClip = AudioConfig.Common_fail }},
            {GuildLeagueFightWinUI,new WndUICfg(){ResPath = "PrefabsN/UI/FightEndUI/GuildLeague/GuildLeagueFightWinUI",ResSceneType = ResSceneType.FightUI,AudioClip = AudioConfig.Common_win }},
            {CSArenaFightLoseUI,new WndUICfg(){ResPath = "PrefabsN/UI/FightEndUI/CSArena/CSArenaFightLoseUI",ResSceneType = ResSceneType.FightUI,AudioClip = AudioConfig.Common_win }},
            {CSArenaFightWinUI,new WndUICfg(){ResPath = "PrefabsN/UI/FightEndUI/CSArena/CSArenaFightWinUI",ResSceneType = ResSceneType.FightUI,AudioClip = AudioConfig.Common_win }},
            {TacticFightLoseUI,new WndUICfg(){ResPath = "PrefabsN/UI/FightEndUI/Tactic/TacticFightLoseUI",ResSceneType = ResSceneType.FightUI,AudioClip = AudioConfig.Common_fail }},
            {TacticFightWinUI,new WndUICfg(){ResPath = "PrefabsN/UI/FightEndUI/Tactic/TacticFightWinUI",ResSceneType = ResSceneType.FightUI,AudioClip = AudioConfig.Common_win }},

            // PopUI - 通用物品获得
            {GetRewardUI,new WndUICfg(){ResPath = "PrefabsN/UI/Common/GetRewardUI",MaskBlack = true,TouchEmptyClose = true,TouchEmptyCloseDelayTime =0.5f,AudioClip = AudioConfig.Common_get}},
            {FlyItem,new WndUICfg(){ResPath = "PrefabsN/UI/Common/FlyItem",NoTouchMask = true,AutoOrder = false}},
            // PopUI - 功能解锁
            {SystemUnlockUI,new WndUICfg(){ResPath = "PrefabsN/UI/Common/SystemUnlockUI",MaskBlack = true,TouchEmptyClose = true,AudioClip = AudioConfig.UI_SystemUnlock}},
            // PopUI - 角色升级
            {RoleLevelUpUI,new WndUICfg(){ResPath = "PrefabsN/UI/Common/RoleLevelUpUI",MaskBlack = true,TouchEmptyClose = true,AudioClip = AudioConfig.CommonLvUp,TouchEmptyCloseDelayTime = 1f}},

            // 机甲
            {MechaUpgradeUI,new WndUICfg(){ResPath = "PrefabsN/UI/Mecha/MechaUpgradeUI",MaskBlack = true,TouchEmptyClose = true,OnUICloseListener = new List<string>{GetRewardUI,MechaUpgradeResultUI}}},
            {MechaUpgradeOverviewUI,new WndUICfg(){ResPath = "PrefabsN/UI/Mecha/MechaUpgradeOverviewUI",MaskBlack = true,TouchEmptyClose = true}},
            {MechaGainedUI,new WndUICfg(){ResPath = "PrefabsN/UI/Mecha/MechaGainedUI",MaskBlack = true,TouchEmptyClose = true,TouchEmptyCloseDelayTime = 1,AudioClip = AudioConfig.CommonNew}},
            {MechaUpgradeResultUI,new WndUICfg(){ResPath = "PrefabsN/UI/Mecha/MechaUpgradeResultUI",MaskBlack = true,TouchEmptyClose = true,TouchEmptyCloseDelayTime = 2f,AudioClip = AudioConfig.RankUp_Sucess}},
            {MechaListMainUI,new WndUICfg(){ResPath = "PrefabsN/UI/Mecha/MechaListMainUI"}},
            {MechaSkinMainUI,new WndUICfg(){ResPath = "PrefabsN/UI/Mecha/Skin/MechaSkinMainUI",MaskBlack = true,TouchEmptyClose = true}},
            {MechaSkinUpgradeResultUI,new WndUICfg(){ResPath = "PrefabsN/UI/Mecha/Skin/MechaSkinUpgradeResultUI",MaskBlack = true,TouchEmptyClose = true,AudioClip = AudioConfig.RankUp_Sucess}},
            {MechaStoryUI,new WndUICfg(){ResPath = "PrefabsN/UI/Mecha/MechaStoryUI",MaskBlack = true,TouchEmptyClose = true}},
            {MechaSkinGainedUI,new WndUICfg(){ResPath = "PrefabsN/UI/Mecha/Skin/MechaSkinGainedUI",MaskBlack = true,TouchEmptyClose = true,TouchEmptyCloseDelayTime = 1,AudioClip = AudioConfig.CommonNew}},
            {MechaSkinTipsUI,new WndUICfg(){ResPath = "PrefabsN/UI/Mecha/Skin/MechaSkinTipsUI",MaskBlack = true,TouchEmptyClose = true}},
            // 机甲DIY Part
            {MechaDiyUI,new WndUICfg(){ResPath = "PrefabsN/UI/DIYPart/MechaDiyUI",NoTouchMask = true}},
            {DiyPartListUI,new WndUICfg(){ResPath = "PrefabsN/UI/DIYPart/DiyPartListUI",UnRespondEscape = true}},
            {PartUpgradeUI,new WndUICfg(){ResPath = "PrefabsN/UI/DIYPart/PartUpgradeUI",TouchEmptyClose = true,MaskBlack = true,ShowCurrencyTab = CurrencyTabUI.PartUpgrade,OnUICloseListener = new List<string>{GetRewardUI,PartRankUpResultUI}}},
            {PartRankUpResultUI,new WndUICfg(){ResPath = "PrefabsN/UI/DIYPart/PartRankUpResultUI",MaskBlack = true,TouchEmptyClose = true,TouchEmptyCloseDelayTime = 2f,AudioClip = AudioConfig.RankUp_Sucess}},
            {ResetWeaponUI,new WndUICfg(){ResPath = "PrefabsN/UI/DIYPart/ResetWeaponUI",MaskBlack = true,TouchEmptyClose = true}},
            {GetNewWeaponUI,new WndUICfg(){ResPath = "PrefabsN/UI/DIYPart/GetNewWeaponUI",TouchEmptyClose = true,TouchEmptyCloseDelayTime = 1.5f,AudioClip = AudioConfig.CommonNew}},
            {PartSkinGainedUI,new WndUICfg(){ResPath = "PrefabsN/UI/DIYPart/PartSkinGainedUI",TouchEmptyClose = true,MaskBlack = true,TouchEmptyCloseDelayTime = 1f,AudioClip = AudioConfig.CommonNew}},
            {PartSkinUpgradeResultUI,new WndUICfg(){ResPath = "PrefabsN/UI/DIYPart/PartSkinUpgradeResultUI",TouchEmptyClose = true,MaskBlack = true,AudioClip = AudioConfig.RankUp_Sucess}},
            {PartSkinTipsUI,new WndUICfg(){ResPath = "PrefabsN/UI/DIYPart/PartSkinTipsUI",TouchEmptyClose = true,MaskBlack = true,}},
            // uav
            {UAVrollback,new WndUICfg(){ResPath = "PrefabsN/UI/UAV/UAVrollback",MaskBlack = true,TouchEmptyClose = true}},
            {GetNewUAVUI,new WndUICfg(){ResPath = "PrefabsN/UI/UAV/GetNewUAVUI",TouchEmptyClose = true,AudioClip = AudioConfig.CommonNew}},
            {UAVMainUI,new WndUICfg(){ResPath = "PrefabsN/UI/UAV/UAVMainUI",UnRespondEscape = true}},
            {UAVEquipUI,new WndUICfg(){ResPath = "PrefabsN/UI/UAV/UAVEquipUI",TouchEmptyClose = true,MaskBlack = true}},
            {UAVShowSkill,new WndUICfg(){ResPath = "PrefabsN/UI/UAV/UAVShowSkill",TouchEmptyClose = true,MaskBlack = true}},
            {UAVUpgradeResultUI,new WndUICfg(){ResPath = "PrefabsN/UI/UAV/UAVUpgradeResultUI",TouchEmptyClose = true,MaskBlack = true,TouchEmptyCloseDelayTime = 1.5f,AudioClip = AudioConfig.UI_CommonUpgrade}},
            {UAVsynthesis,new WndUICfg(){ResPath = "PrefabsN/UI/UAV/UAVsynthesis",MaskBlack = true}},
            // 背包
            {KnapsackUI,new WndUICfg(){ResPath = "PrefabsN/UI/Knapsack/KnapsackRoot",TouchEmptyClose = true,ShowCurrencyTab = CurrencyTabUI.Bag}},
            {Box_Tips,new WndUICfg(){ResPath = "PrefabsN/UI/Knapsack/Box_Tips",MaskBlack = true,TouchEmptyClose = true,AutoUnload = false}},
            {Box_Tips2,new WndUICfg(){ResPath = "PrefabsN/UI/Knapsack/Box_Tips",MaskBlack = true,TouchEmptyClose = true,AutoUnload = false}},
            {ChooseBox_Use,new WndUICfg(){ResPath = "PrefabsN/UI/Knapsack/ChooseBox_Use",MaskBlack = true,TouchEmptyClose = true,AutoUnload = false}},
            {ChooseBox_Use2,new WndUICfg(){ResPath = "PrefabsN/UI/Knapsack/ChooseBox_Use",MaskBlack = true,TouchEmptyClose = true,AutoUnload = false}},
            {Item_Tips,new WndUICfg(){ResPath = "PrefabsN/UI/Knapsack/Item_Tips",MaskBlack = true,TouchEmptyClose = true}},
            {Compose_Tips,new WndUICfg(){ResPath = "PrefabsN/UI/Knapsack/Compose_Tips",MaskBlack = true,TouchEmptyClose = true}},
            {Mech_Tips,new WndUICfg(){ResPath = "PrefabsN/UI/Knapsack/Mech_Tips",MaskBlack = true,TouchEmptyClose = true}},
            {DIYPart_Tips,new WndUICfg(){ResPath = "PrefabsN/UI/Knapsack/DIYPart_Tips",MaskBlack = true,TouchEmptyClose = true}},
            {MessageBox_KuaiSuHuoQu,new WndUICfg(){ResPath = "PrefabsN/UI/Knapsack/MessageBox_KuaiSuHuoQu",MaskBlack = true,TouchEmptyClose = true,OnUICloseListener = new List<string>()}},
            {Top_Item_Tips,new WndUICfg(){ResPath = "PrefabsN/UI/Knapsack/Top_Item_Tips",MaskBlack = true,TouchEmptyClose = true}},
            {CiTiaoListView,new WndUICfg(){ResPath = "PrefabsN/UI/DIYPart/CiTiaoListView",MaskBlack = true,TouchEmptyClose = true}},
            {UAV_Tips,new WndUICfg(){ResPath = "PrefabsN/UI/Knapsack/UAV_tips",MaskBlack = true,TouchEmptyClose = true}},
            {Equipment_Tips,new WndUICfg(){ResPath = "PrefabsN/UI/Knapsack/Equipment_Tips",MaskBlack = true,TouchEmptyClose = true}},
            {Aircraft_Tips,new WndUICfg(){ResPath = "PrefabsN/UI/Knapsack/Aircraft_tips",MaskBlack = true,TouchEmptyClose = true}},

            // 体力
            {BuyEnergyUI,new WndUICfg(){ResPath = "PrefabsN/UI/BuyEnergy/BuyEnergyUI",MaskBlack = true,TouchEmptyClose = true}},
            {BuyLeftEnergyUI,new WndUICfg(){ResPath = "PrefabsN/UI/BuyEnergy/BuyLeftEnergyUI",MaskBlack = true,TouchEmptyClose = true}},

            // AFK
            {AFKModeUI,new WndUICfg(){ResPath = "PrefabsN/UI/AFK/AFKModeUI",MaskBlack = true,TouchEmptyClose = true,OnUICloseListener = new List<string>{BuyEnergyUI,RoleLevelUpUI,AFKSweepUI}}},
            {AFKSweepUI,new WndUICfg(){ResPath = "PrefabsN/UI/AFK/AFKSweepUI",MaskBlack = true,TouchEmptyClose = true,OnUICloseListener = new List<string>{BuyEnergyUI,RoleLevelUpUI,DoubleRewardUI,NotEnergyUI}}},

            // 邮件
            {MailList,new WndUICfg(){ResPath = "PrefabsN/UI/Mail/MailList",MaskBlack = true,TouchEmptyClose = true}},
            {MailContent,new WndUICfg(){ResPath = "PrefabsN/UI/Mail/MailContent",MaskBlack = true,TouchEmptyClose = true}},

            // 副本玩法
            {PVETowerMainUI,new WndUICfg(){ResPath = "PrefabsN/UI/GamePlay/PVETower/PVETowerMainUI",MaskBlack = true}},
            {CommonExplainUI,new WndUICfg(){ResPath = "PrefabsN/UI/Common/CommonExplainUI",MaskBlack = true,TouchEmptyClose = true}},

            {ADTips,new WndUICfg(){ResPath = "PrefabsN/UI/Common/ADtips",MaskBlack = true,TouchEmptyClose = true}},
            {FightTowerTipsUI,new WndUICfg(){ResPath = "PrefabsN/UI/Common/FightTowerTipsUI",MaskBlack = true,TouchEmptyClose = true}},
            {PVETowerRankTipsUI,new WndUICfg(){ResPath = "PrefabsN/UI/GamePlay/PVETower/PVETowerRankTipsUI",MaskBlack = true}},
            {PVETowerPartUI,new WndUICfg(){ResPath = "PrefabsN/UI/GamePlay/PVETower/PVETowerPartUI",MaskBlack = true,TouchEmptyClose = true}},

            // 战力
            {PowerUI,new WndUICfg(){ResPath = "PrefabsN/UI/Main/PowerUI",NoTouchMask = true}},
            //战力对比
            {PowerCompareUI,new WndUICfg(){ResPath = "PrefabsN/UI/Main/PowerCompareUI",MaskBlack = true,TouchEmptyClose = true}},

            // 设置
            {OptionsUI,new WndUICfg(){ResPath = "PrefabsN/UI/Main/OptionsUI",TouchEmptyClose = true,MaskBlack = true,OnUICloseListener = new List<string>{ QuickBuyDiamondUI,GetRewardUI }}},
            {LangSettingUI,new WndUICfg(){ResPath = "PrefabsN/UI/Main/LangSettingUI",TouchEmptyClose = true,MaskBlack = true } },

            {Privacy_MessageBox01,new WndUICfg(){ResPath = "PrefabsN/UI/Main/Privacy_MessageBox01",TouchEmptyClose = false,MaskBlack = true}},
            {Privacy_MessageBox02,new WndUICfg(){ResPath = "PrefabsN/UI/Main/Privacy_MessageBox02",TouchEmptyClose = false,MaskBlack = true}},
            // 排行榜
            {RankUI,new WndUICfg(){ResPath = "PrefabsN/UI/Rank/RankUI",MaskBlack = true}},
            {MainMissionRankUI,new WndUICfg(){ResPath = "PrefabsN/UI/Rank/MainMissionRankUI",MaskBlack = true,TouchEmptyClose = true}},
            {MainMissionTop5UI,new WndUICfg(){ResPath = "PrefabsN/UI/Rank/MainMissionTop5UI",MaskBlack = true,TouchEmptyClose = true}},
            {PVETowerRankUI,new WndUICfg(){ResPath = "PrefabsN/UI/Rank/PVETowerRankUI",MaskBlack = true,TouchEmptyClose = true}},
            {ExpeditionRankUI,new WndUICfg(){ResPath = "PrefabsN/UI/Rank/ExpeditionRankUI",MaskBlack = true,TouchEmptyClose = true}},
            {SeasonRankUI,new WndUICfg(){ResPath = "PrefabsN/UI/Rank/SeasonRankUI",MaskBlack = true,TouchEmptyClose = true}},
            {SeasonEndlessRankUI,new WndUICfg(){ResPath = "PrefabsN/UI/Rank/SeasonEndlessRankUI",MaskBlack = true,TouchEmptyClose = true}},
            {SeasonMissionTop5UI,new WndUICfg(){ResPath = "PrefabsN/UI/Rank/SeasonMissionTop5UI",MaskBlack = true,TouchEmptyClose = true}},

            //每日 每周任务
            {QuestUI,new WndUICfg(){ResPath = "PrefabsN/UI/Quest/QuestUI",MaskBlack = true, TouchEmptyClose = true, OnUICloseListener = new List<string>(){LDUICfg.GetRewardUI}}},
            //福利
            // 28天签到
            {WelfareUI,new WndUICfg(){ResPath = "PrefabsN/UI/Welfare/WelfareUI",MaskBlack = true}},
            //7/14签到
            {StartAwardUI, new WndUICfg(){ResPath = "PrefabsN/UI/Welfare/StartAwardUI",MaskBlack = true, TouchEmptyClose = true,AudioClip = AudioConfig.UI_SystemHint,OnUICloseListener = new List<string>(){LDUICfg.GetRewardUI, LDUICfg.MechaGainedUI, LDUICfg.PartSkinGainedUI}}},

            // boss关弹板
            {MasterChallengeUI,new WndUICfg(){ResPath = "PrefabsN/UI/Fight/MasterChallengeUI",MaskBlack = true,ResSceneType = ResSceneType.Resident}},
            {BossChallengeUI,new WndUICfg(){ResPath = "PrefabsN/UI/Fight/BossChallengeUI",MaskBlack = true,ResSceneType = ResSceneType.Resident}},
            {ArenaLoadingUI,new WndUICfg(){ResPath = "PrefabsN/UI/Fight/ArenaLoadingUI",AutoOrder = false,MaskBlack = false,ResSceneType = ResSceneType.Resident,AudioClip = AudioConfig.UI_CommonBattle}},
            {LeagueLoadingUI,new WndUICfg(){ResPath = "PrefabsN/UI/Fight/LeagueLoadingUI",AutoOrder = false,MaskBlack = false,ResSceneType = ResSceneType.Resident,AudioClip = AudioConfig.UI_CommonBattle}},

            // 金币本
            {CoinPassMainUI,new WndUICfg(){ResPath = "PrefabsN/UI/GamePlay/CoinPass/CoinPassMainUI",MaskBlack = true}},

            // 快速购买钻石
            {QuickBuyDiamondUI,new WndUICfg(){ResPath = "PrefabsN/UI/Shop/QuickBuyDiamondUI",MaskBlack = true, TouchEmptyClose = true, OnUICloseListener = new List<string>(){LDUICfg.GetRewardUI}}},
            {QuickBuyGoldUI,new WndUICfg(){ResPath = "PrefabsN/UI/Shop/QuickBuyGoldUI",MaskBlack = true, TouchEmptyClose = true, OnUICloseListener = new List<string>(){LDUICfg.GetRewardUI}}},


            // 兑换商店
            {ExchangeShopUI, new WndUICfg(){ResPath = "PrefabsN/UI/ExchangeShop/ExchangeShop_UI", MaskBlack = true, TouchEmptyClose = true}},
            {MessageBox_ExchangeShop_PiLiangGouMai, new WndUICfg(){ResPath = "PrefabsN/UI/ExchangeShop/MessageBox_ExchangeShop_PiLiangGouMai", MaskBlack = true, TouchEmptyClose = true}},
            {ConfirmBuyUI, new WndUICfg(){ResPath = "PrefabsN/UI/ExchangeShop/ConfirmBuyUI", MaskBlack = true, TouchEmptyClose = true}},

            //月卡
            {MonthlyCardUI, new WndUICfg(){ResPath = "PrefabsN/UI/MonthlyCard/MonthlyCardUI", MaskBlack = true, TouchEmptyClose = true}},
            //首冲
            {FirstRechargeUI, new WndUICfg(){ResPath = "PrefabsN/UI/Welfare/FirstRechargeUI", MaskBlack = true, TouchEmptyClose = true, ShowCurrencyTab = CurrencyTabUI.CommonMoney}},
            //部件礼包
            {DIYPackUI, new WndUICfg(){ResPath = "PrefabsN/UI/DIYPack/DIYPackUI", MaskBlack = true, TouchEmptyClose = true,AudioClip = AudioConfig.UI_SystemHint,ShowCurrencyTab = CurrencyTabUI.CommonMoney}},

            {BindAccountUI, new WndUICfg(){ResPath = "PrefabsN/UI/Main/BindAccountUI", MaskBlack = true, TouchEmptyClose = true,TouchEmptyCloseDelayTime = 0.5f}},
            //七天乐
            {ChallengeTaskUI, new WndUICfg(){ResPath = "PrefabsN/UI/Welfare/ChallengeTaskUI", MaskBlack = true, TouchEmptyClose = true, OnUICloseListener = new List<string>(){LDUICfg.GetRewardUI}}},

            // guid
            {UIDialog, new WndUICfg(){ResPath = "PrefabsN/UI/Tutorial/UIDialog"}},

            // 属性面板
            {AttrInfo, new WndUICfg(){ResPath = "PrefabsN/UI/Mecha/AttrInfo", MaskBlack = true, TouchEmptyClose = true}},

            // 机甲抽奖
            {MechaSpinMain, new WndUICfg(){ResPath = "PrefabsN/UI/MechaSpin/MechaSpinMain", MaskBlack = true, TouchEmptyClose = true,ShowCurrencyTab = CurrencyTabUI.MechaSpin, OnUICloseListener = new List<string>(){MechaSpinBuyItem}}},
            {MechaSpinBuyItem, new WndUICfg(){ResPath = "PrefabsN/UI/MechaSpin/MechaSpinBuyItem", MaskBlack = true, TouchEmptyClose = true,ShowCurrencyTab = CurrencyTabUI.MechaSpin}},
            {MechaSpinFinishUI, new WndUICfg(){ResPath = "PrefabsN/UI/MechaSpin/MechaSpinFinishUI", MaskBlack = true, TouchEmptyClose = true}},
            {MechaSpin_Hint, new WndUICfg(){ResPath = "PrefabsN/UI/MechaSpin/MechaSpin_Hint", MaskBlack = true, TouchEmptyClose = true,AudioClip = AudioConfig.UI_SystemHint}},
            {MechaSpinExchangeShopUI, new WndUICfg(){ResPath = "PrefabsN/UI/MechaSpin/MechaSpinExchangeShopUI", MaskBlack = true, TouchEmptyClose = true,ShowCurrencyTab = CurrencyTabUI.MechaSpin}},
            {MechaSpinPopUI, new WndUICfg(){ResPath = "PrefabsN/UI/MechaSpin/MechaSpinPopUI", MaskBlack = true, TouchEmptyClose = true}},
            {MechaSpinTipsUI, new WndUICfg(){ResPath = "PrefabsN/UI/MechaSpin/MechaSpinTipsUI", MaskBlack = true, TouchEmptyClose = true,TouchEmptyCloseDelayTime = 2f}},

            //战令
            {BattlePassBuy, new WndUICfg(){ResPath = "PrefabsN/UI/BattlePass/BattlePassBuy", MaskBlack = true, TouchEmptyClose = true,AudioClip = AudioConfig.UI_SystemHint, ShowCurrencyTab = CurrencyTabUI.CommonMoney}},
            {BattlePassMain, new WndUICfg(){ResPath = "PrefabsN/UI/BattlePass/BattlePassMain", MaskBlack = true, TouchEmptyClose = true}},
            {BattlePassBuyPublicize, new WndUICfg(){ResPath = "PrefabsN/UI/BattlePass/BattlePassBuyPublicize", MaskBlack = true, TouchEmptyClose = true, ShowCurrencyTab = CurrencyTabUI.CommonMoney}},
            //部件累抽
            {DiyActivityMain, new WndUICfg(){ResPath = "PrefabsN/UI/DiyActivity/DiyActivity_Main", MaskBlack = true, TouchEmptyClose = true, ShowCurrencyTab = CurrencyTabUI.CommonMoney, OnUICloseListener = new List<string>(){GetRewardUI}}},
            {DiyActivity_RankRewardUI, new WndUICfg(){ResPath = "PrefabsN/UI/DiyActivity/DiyActivity_RankRewardUI", MaskBlack = true, TouchEmptyClose = true}},
            {DiyActivityHint, new WndUICfg(){ResPath = "PrefabsN/UI/DiyActivity/DiyActivity_Hint", MaskBlack = true, TouchEmptyClose = true,AudioClip = AudioConfig.UI_SystemHint}},
            //无人机累抽
            {UAVActivityMain, new WndUICfg(){ResPath = "PrefabsN/UI/DiyActivity/UAVActivity_Main", MaskBlack = true, TouchEmptyClose = true}},
            {UAVActivityHint, new WndUICfg(){ResPath = "PrefabsN/UI/DiyActivity/UAVActivity_Hint", MaskBlack = true, TouchEmptyClose = true,AudioClip = AudioConfig.UI_SystemHint}},

            // 主线礼包
            {MissionPackMain, new WndUICfg(){ResPath = "PrefabsN/UI/MainMissionPack/MissionPackMain", MaskBlack = true, TouchEmptyClose = true, TouchEmptyCloseDelayTime = 1f,AudioClip = AudioConfig.UI_SystemHint, ShowCurrencyTab = CurrencyTabUI.CommonMoney}},

            //机甲升级礼包
            {MechaLvPackMain, new WndUICfg(){ResPath = "PrefabsN/UI/MechaLvPack/MechaLvPackMain", MaskBlack = true, TouchEmptyClose = true, TouchEmptyCloseDelayTime = 1f}},

            //新手引导
            {GuideUI, new WndUICfg(){ResPath = "PrefabsN/UI/Main/GuideUI",UILevel = 1,TouchEmptyClose = false, MaskBlack = false,NoTouchMask = true}},

            #region Expedition
            //远征
            {ExpeditionKnapsackUI, new WndUICfg(){ResPath = "PrefabsN/UI/GamePlay/Expedition/ExpeditionKnapsackUI", MaskBlack = true,TouchEmptyClose = true}},
            {ExpeditionCollectUI,new WndUICfg(){ResPath = "PrefabsN/UI/GamePlay/Expedition/ExpeditionCollectUI", MaskBlack = true, TouchEmptyClose = true}},
            {ExpeditionMainUI,new WndUICfg(){ResPath = "PrefabsN/UI/GamePlay/Expedition/ExpeditionMainUI", MaskBlack = true, TouchEmptyClose = true, OnUICloseListener = new List<string>(){LDUICfg.ExpeditionTurntableUI, LDUICfg.ExpeditionRemindUI, LDUICfg.ExpeditionCiTiaoView, LDUICfg.ExpeditionGiveCiTiaoView}}},
            {ExpeditionItemTips,new WndUICfg(){ResPath = "PrefabsN/UI/GamePlay/Expedition/Expedition_Item_Tips", MaskBlack = true, TouchEmptyClose = true}},
            {ExpeditionBaoWuUI,new WndUICfg(){ResPath = "PrefabsN/UI/GamePlay/Expedition/ExpeditionBaoWuUI", MaskBlack = true, TouchEmptyClose = false}},
            {ExpeditionBossDifficultyUI,new WndUICfg(){ResPath = "PrefabsN/UI/GamePlay/Expedition/ExpeditionBossDifficultyUI", MaskBlack = true, TouchEmptyClose = false}},
            {ExpeditionBWTCQ,new WndUICfg(){ResPath = "PrefabsN/UI/GamePlay/Expedition/ExpeditionBWTCQ", MaskBlack = true, TouchEmptyClose = true}},
            {ExpeditionBuJiUI,new WndUICfg(){ResPath = "PrefabsN/UI/GamePlay/Expedition/ExpeditionBuJiUI", MaskBlack = true, TouchEmptyClose = false}},
            {ExpeditionCongratulationsUI,new WndUICfg(){ResPath = "PrefabsN/UI/GamePlay/Expedition/ExpeditionCongratulationsUI", MaskBlack = true, TouchEmptyClose = true}},
            {ExpeditionRemindUI,new WndUICfg(){ResPath = "PrefabsN/UI/GamePlay/Expedition/ExpeditionRemindUI", MaskBlack = true, TouchEmptyClose = true}},
            {ExpeditionShiJianUI,new WndUICfg(){ResPath = "PrefabsN/UI/GamePlay/Expedition/ExpeditionShiJianUI", MaskBlack = true, TouchEmptyClose = false}},
            {ExpeditionZhanDouUI,new WndUICfg(){ResPath = "PrefabsN/UI/GamePlay/Expedition/ExpeditionZhanDouUI", MaskBlack = true, TouchEmptyClose = false}},
            {ExpeditionTurntableUI,new WndUICfg(){ResPath = "PrefabsN/UI/GamePlay/Expedition/ExpeditionTurntableUI", MaskBlack = true, TouchEmptyClose = true}},
            {ExpeditionCiTiaoView,new WndUICfg(){ResPath = "PrefabsN/UI/GamePlay/Expedition/ExpeditionCiTiaoView", MaskBlack = true, TouchEmptyClose = false}},
            {ExpeditionEliteCitiaoView,new WndUICfg(){ResPath = "PrefabsN/UI/GamePlay/Expedition/ExpeditionEliteCitiaoView", MaskBlack = true, TouchEmptyClose = false}},
            {ExpeditionGiveCiTiaoView,new WndUICfg(){ResPath = "PrefabsN/UI/GamePlay/Expedition/ExpeditionGiveCiTiaoView", MaskBlack = true, TouchEmptyClose = false}},
            {ExpeditionViewUI,new WndUICfg(){ResPath = "PrefabsN/UI/GamePlay/Expedition/ExpeditionViewUI", MaskBlack = true, TouchEmptyClose = true}},

            #endregion

            #region guild

            {GuildMainUI,new WndUICfg(){ResPath = "PrefabsN/UI/Guild/GuildMainUI", MaskBlack = true, UILevel = 1,AutoOrder = false,ShowMenuTab = true,ShowCurrencyTab = CurrencyTabUI.Guild,OnUICloseListener = new List<string>(){LDUICfg.AllGuildListUI}}},
            {AllGuildListUI,new WndUICfg(){ResPath = "PrefabsN/UI/Guild/AllGuildListUI", MaskBlack = true, TouchEmptyClose = true,ShowCurrencyTab = CurrencyTabUI.GuildList}},
            {AddGuildUI,new WndUICfg(){ResPath = "PrefabsN/UI/Guild/AddGuildUI", MaskBlack = true, TouchEmptyClose = true}},
            {GuildInfoUI,new WndUICfg(){ResPath = "PrefabsN/UI/Guild/GuildInfoUI", MaskBlack = true, TouchEmptyClose = true,ShowCurrencyTab = CurrencyTabUI.Guild}},
            {GuildLogUI,new WndUICfg(){ResPath = "PrefabsN/UI/Guild/GuildLogUI", MaskBlack = true, TouchEmptyClose = true}},
            {LeaveGuild_MessageBoxUI,new WndUICfg(){ResPath = "PrefabsN/UI/Guild/LeaveGuild_MessageBoxUI", MaskBlack = true, TouchEmptyClose = true}},
            {ExitGuild_MessageBoxUI,new WndUICfg(){ResPath = "PrefabsN/UI/Guild/ExitGuild_MessageBoxUI", MaskBlack = true, TouchEmptyClose = true}},
            {GuildManagementUI,new WndUICfg(){ResPath = "PrefabsN/UI/Guild/GuildManagementUI", MaskBlack = true, TouchEmptyClose = true}},
            {GuildDonationUI,new WndUICfg(){ResPath = "PrefabsN/UI/Guild/GuildDonationUI", MaskBlack = true, TouchEmptyClose = true,ShowCurrencyTab = CurrencyTabUI.GuildDonation}},
            {GuildDonationLogUI,new WndUICfg(){ResPath = "PrefabsN/UI/Guild/GuildDonationLogUI", MaskBlack = true, TouchEmptyClose = true}},
            {GuildBargainUI,new WndUICfg(){ResPath = "PrefabsN/UI/Guild/GuildBargainUI", MaskBlack = true, TouchEmptyClose = true,ShowCurrencyTab = CurrencyTabUI.GuildBargain}},
            {Bargain_MessageBoxUI,new WndUICfg(){ResPath = "PrefabsN/UI/Guild/Bargain_MessageBoxUI", MaskBlack = true, TouchEmptyClose = true}},
            {GuildBossUI,new WndUICfg(){ResPath = "PrefabsN/UI/Guild/GuildBossUI", MaskBlack = true, TouchEmptyClose = true}},
            {GuildBossHurtRankUI,new WndUICfg(){ResPath = "PrefabsN/UI/Guild/GuildBossHurtRankUI", MaskBlack = true, TouchEmptyClose = true}},
            {GuildBossTaskUI,new WndUICfg(){ResPath = "PrefabsN/UI/Guild/GuildBossTaskUI", MaskBlack = true, TouchEmptyClose = true}},
            {GuildUpgradeUI,new WndUICfg(){ResPath = "PrefabsN/UI/Guild/GuildUpgradeUI", MaskBlack = true, TouchEmptyClose = true}},
            {ViewGuildInfoUI,new WndUICfg(){ResPath = "PrefabsN/UI/Guild/ViewGuildInfoUI", MaskBlack = true, TouchEmptyClose = true}},
            {GuildDeclaration_MessageBoxUI,new WndUICfg(){ResPath = "PrefabsN/UI/Guild/GuildDeclaration_MessageBoxUI", MaskBlack = true, TouchEmptyClose = true}},
            {GuildLeagueMainUI,new WndUICfg(){ResPath = "PrefabsN/UI/GuildLeague/GuildLeagueMainUI", MaskBlack = true, TouchEmptyClose = true}},
            {GuildLeagueRankUI,new WndUICfg(){ResPath = "PrefabsN/UI/GuildLeague/GuildLeagueRankUI", MaskBlack = true, TouchEmptyClose = true}},
            {GuildLeagueBattleListUI,new WndUICfg(){ResPath = "PrefabsN/UI/GuildLeague/GuildLeagueBattleListUI", MaskBlack = true, TouchEmptyClose = true}},
            {GuildLeagueLineupUI,new WndUICfg(){ResPath = "PrefabsN/UI/GuildLeague/GuildLeagueLineupUI", MaskBlack = true, TouchEmptyClose = true}},
            {GuildLeagueFormatUI,new WndUICfg(){ResPath = "PrefabsN/UI/GuildLeague/GuildLeagueFormatUI", MaskBlack = true, TouchEmptyClose = true}},
            {GuildLeagueRescueUI,new WndUICfg(){ResPath = "PrefabsN/UI/GuildLeague/GuildLeagueRescueUI", MaskBlack = true, TouchEmptyClose = true,OnUICloseListener = new List<string>{ GuildLeagueOffensiveLineupUI }}},
            {GuildLeagueOffensiveLineupUI,new WndUICfg(){ResPath = "PrefabsN/UI/GuildLeague/GuildLeagueOffensiveLineupUI", MaskBlack = true, TouchEmptyClose = true,OnUICloseListener = new List<string>(){ArenaMechaDiyUI}}},

            #endregion

            //三方支付弹窗
            {UseCrystals_MessageBoxUI,new WndUICfg(){ResPath = "PrefabsN/UI/Guild/UseCrystals_MessageBoxUI", MaskBlack = true, TouchEmptyClose = true}},

            //特惠
            {SuperValueActUI, new WndUICfg(){ResPath = "PrefabsN/UI/SuperValue/SuperValueActUI", MaskBlack = true, TouchEmptyClose = true}},
            {VipPrivilegeUpgradeUI, new WndUICfg(){ResPath = "PrefabsN/UI/SuperValue/VipPrivilegeUpgradeUI", MaskBlack = true, TouchEmptyClose = true, TouchEmptyCloseDelayTime = 1f}},
            {QuickRechargeUI, new WndUICfg(){ResPath = "PrefabsN/UI/SuperValue/QuickRechargeUI", MaskBlack = true, TouchEmptyClose = true, TouchEmptyCloseDelayTime = 1f}},

            // 机甲强化
            {Enhancement_mainUI, new WndUICfg(){ResPath = "PrefabsN/UI/Enhancement/Enhancement_mainUI", MaskBlack = true, TouchEmptyClose = true,ShowCurrencyTab = CurrencyTabUI.MechaEnhancement, OnUICloseListener = new List<string>{ Enhancement_LevelUpUI,GetRewardUI,Enhancement_unlockUI } }},
            {Enhancement_LevelUpUI, new WndUICfg(){ResPath = "PrefabsN/UI/Enhancement/Enhancement_LevelUpUI", MaskBlack = true, TouchEmptyClose = true,TouchEmptyCloseDelayTime = 2f}},
            {Enhancement_Overview, new WndUICfg(){ResPath = "PrefabsN/UI/Enhancement/Enhancement_Overview", MaskBlack = true, TouchEmptyClose = true}},
            // {Enhancement_unlockUI, new WndUICfg(){ResPath = "PrefabsN/UI/Enhancement/Enhancement_unlockUI", MaskBlack = true, TouchEmptyClose = true}},

            {DoubleRewardUI, new WndUICfg(){ResPath = "PrefabsN/UI/AFK/DoubleEvents", MaskBlack = true, TouchEmptyClose = false}},
            {NotEnergyUI, new WndUICfg(){ResPath = "PrefabsN/UI/AFK/ZeroEnergy", MaskBlack = true, TouchEmptyClose = false}},

            // 好友
            {FriendUI, new WndUICfg(){ResPath = "PrefabsN/UI/Friend/FriendUI", MaskBlack = true, TouchEmptyClose = true}},
            {FriendUI_BlackList_MessageBox, new WndUICfg(){ResPath = "PrefabsN/UI/Friend/FriendUI_BlackList_MessageBox", MaskBlack = true, TouchEmptyClose = true}},
            {FriendUI_AddList_MessageBox, new WndUICfg(){ResPath = "PrefabsN/UI/Friend/FriendUI_AddList_MessageBox", MaskBlack = true, TouchEmptyClose = true}},
            {DeleteFriendUI, new WndUICfg(){ResPath = "PrefabsN/UI/Friend/DeleteFriendUI", MaskBlack = true, TouchEmptyClose = true}},

            //战力活动
            {CombatTarget_Main, new WndUICfg(){ResPath = "PrefabsN/UI/Welfare/CombatTarget/CombatTarget_Main", MaskBlack = true, TouchEmptyClose = true, ShowCurrencyTab = CurrencyTabUI.CommonMoney}},
            {CombatTarget_Hint, new WndUICfg(){ResPath = "PrefabsN/UI/Welfare/CombatTarget/CombatTarget_Hint", MaskBlack = true, TouchEmptyClose = true}},
            //聊天
            {ChatUI, new WndUICfg(){ResPath = "PrefabsN/UI/Main/Chat/ChatUI", MaskBlack = true, TouchEmptyClose = true}},
            {ViewOthersUI, new WndUICfg(){ResPath = "PrefabsN/UI/Main/ViewOthersUI", MaskBlack = true, TouchEmptyClose = true}},
            {ChatEmoji_MessageBoxUI, new WndUICfg(){ResPath = "PrefabsN/UI/Main/Chat/ChatEmoji_MessageBoxUI", MaskBlack = true, TouchEmptyClose = true}},

            //赛季
            {SeasonUI, new WndUICfg(){ResPath = "PrefabsN/UI/Season/SeasonUI", MaskBlack = true, TouchEmptyClose = true,ShowCurrencyTab = CurrencyTabUI.SeasonActivity}},
            {SeasonBattlePassBuyUI, new WndUICfg(){ResPath = "PrefabsN/UI/Season/SeasonBattlePassBuyUI", MaskBlack = true, TouchEmptyClose = true}},
            {Season_unlcok, new WndUICfg(){ResPath = "PrefabsN/UI/Season/Season_unlcok", MaskBlack = true, TouchEmptyClose = true,TouchEmptyCloseDelayTime = 0.5f}},
            {Season_Turntable, new WndUICfg(){ResPath = "PrefabsN/UI/Season/Season_Turntable", MaskBlack = true, TouchEmptyClose = false}},
            {SeasonDFPreViewUI, new WndUICfg(){ResPath = "PrefabsN/UI/Season/SeasonDFPreViewUI", MaskBlack = true, TouchEmptyClose = true}},
            {Season_BoxUI, new WndUICfg(){ResPath = "PrefabsN/UI/Season/Season_BoxUI", MaskBlack = true, TouchEmptyClose = true}},
            {SeasonBuffUI, new WndUICfg(){ResPath = "PrefabsN/UI/Season/SeasonBuffUI", MaskBlack = true, TouchEmptyClose = true}},
            {Season_poltUI, new WndUICfg(){ResPath = "PrefabsN/UI/Season/Season_poltUI", MaskBlack = true, TouchEmptyClose = true}},
            {Season_skipPassUI, new WndUICfg(){ResPath = "PrefabsN/UI/Season/Season_skipPassUI", MaskBlack = true, TouchEmptyClose = true,ShowCurrencyTab = CurrencyTabUI.Main}},
            {SeasonPeakMatchFinishUI, new WndUICfg(){ResPath = "PrefabsN/UI/Main/SeasonPeakMatchFinishUI", MaskBlack = true, TouchEmptyClose = true}},
            {Season_DianFengPL, new WndUICfg(){ResPath = "PrefabsN/UI/Season/Season_DianFengPL", MaskBlack = true, TouchEmptyClose = false}},

            //触发礼包
            {TriggerGift_Main, new WndUICfg(){ResPath = "PrefabsN/UI/TriggerGift/TriggerGift_Main", MaskBlack = true, TouchEmptyClose = true,ShowCurrencyTab = CurrencyTabUI.CommonMoney}},
            {TriggerGift_Hint, new WndUICfg(){ResPath = "PrefabsN/UI/TriggerGift/TriggerGift_Hint", MaskBlack = true, TouchEmptyClose = true, TouchEmptyCloseDelayTime = 0.5f,AudioClip = AudioConfig.UI_SystemHint, ShowCurrencyTab = CurrencyTabUI.CommonMoney}},

            #region 营地
            {CampsiteRootUI,new WndUICfg(){ResPath = "PrefabsN/UI/Campsite/CampsiteRootUI",NoTouchMask = true,UILevel = 1,AutoOrder = false,ShowCurrencyTab = CurrencyTabUI.Campsite,ShowMenuTab = true}},
            {CampsiteHouseMainUI, new WndUICfg(){ResPath = "PrefabsN/UI/Campsite/CampsiteHouseMainUI", MaskBlack = true}},
            {CampsiteTowerMainUI, new WndUICfg(){ResPath = "PrefabsN/UI/Campsite/CampsiteTowerMainUI", MaskBlack = true, OnUICloseListener = new List<string>{ LDUICfg.NewDriverUI},ShowCurrencyTab = CurrencyTabUI.CampsiteTower}},
            {DriverEquipUI, new WndUICfg(){ResPath = "PrefabsN/UI/Campsite/DriverEquipUI", MaskBlack = true, TouchEmptyClose = true}},
            {SurvivorUpgradeUI, new WndUICfg(){ResPath = "PrefabsN/UI/Campsite/SurvivorUpgradeUI", MaskBlack = true, TouchEmptyClose = true}},
            {SurvivorLevelUpResultUI, new WndUICfg(){ResPath = "PrefabsN/UI/Campsite/SurvivorLevelUpResultUI", MaskBlack = true, TouchEmptyClose = true,AudioClip = AudioConfig.RankUp_Sucess}},
            {SurvivorTips, new WndUICfg(){ResPath = "PrefabsN/UI/Campsite/SurvivorTips", MaskBlack = true, TouchEmptyClose = true}},
            {DriverTips, new WndUICfg(){ResPath = "PrefabsN/UI/Campsite/DriverTips", MaskBlack = true, TouchEmptyClose = true}},
            {DriverUpgradeUI, new WndUICfg(){ResPath = "PrefabsN/UI/Campsite/DriverUpgradeUI", MaskBlack = true, TouchEmptyClose = true}},
            {DriverUpResultUI, new WndUICfg(){ResPath = "PrefabsN/UI/Campsite/DriverUpResultUI", MaskBlack = true, TouchEmptyClose = true, TouchEmptyCloseDelayTime = 0.5f,AudioClip = AudioConfig.UI_CommonLvUp3}},
            {NewDriverUI, new WndUICfg(){ResPath = "PrefabsN/UI/Campsite/NewDriverUI", MaskBlack = true, TouchEmptyClose = true, TouchEmptyCloseDelayTime = 0.5f,AudioClip = AudioConfig.UI_CommonGet}},
            {ShieldFightPowerUI, new WndUICfg(){ResPath = "PrefabsN/UI/Common/ShieldFightPowerUI", NoTouchMask = true, MaskBlack = false, TouchEmptyClose = false}},
            #endregion

            {PreventPerformanceUI, new WndUICfg(){ResPath = "PrefabsN/UI/Common/PreventPerformanceUI", NoTouchMask = true, MaskBlack = false, TouchEmptyClose = false}},

            #region 周期活动

            {ActivityUI, new WndUICfg(){ResPath = "PrefabsN/UI/Activity/ActivityUI", MaskBlack = true, OnUICloseListener = new List<string>(){}}},
            {ActivityBattlePassUI, new WndUICfg(){ResPath = "PrefabsN/UI/Activity/ActivityBattlePassUI", MaskBlack = true, TouchEmptyClose = true,ShowCurrencyTab = CurrencyTabUI.CommonMoney}},
            {ActivityRankRewardUI, new WndUICfg(){ResPath = "PrefabsN/UI/Activity/ActivityRankRewardUI", MaskBlack = true, TouchEmptyClose = true}},
            {AeroplaneStartUI, new WndUICfg(){ResPath = "PrefabsN/UI/Activity/AeroplaneChess/AeroplaneStartUI", MaskBlack = true, TouchEmptyClose = true,AudioClip = AudioConfig.UI_SystemHint}},
            {Slots_ProbabilityUI, new WndUICfg(){ResPath = "PrefabsN/UI/Activity/Slots/Slots_ProbabilityUI", MaskBlack = true, TouchEmptyClose = true}},
            {Slots_StartUI, new WndUICfg(){ResPath = "PrefabsN/UI/Activity/Slots/Slots_StartUI", MaskBlack = true, TouchEmptyClose = true,AudioClip = AudioConfig.UI_SystemHint}},
            {NinjaTrailStartUI, new WndUICfg(){ResPath = "PrefabsN/UI/Activity/NinjaTrail/NinjaTrailStartUI", MaskBlack = true, TouchEmptyClose = true,AudioClip = AudioConfig.UI_SystemHint}},
            {MechaReissueStartUI, new WndUICfg(){ResPath = "PrefabsN/UI/Activity/MechReissue/MechaReissueStartUI", MaskBlack = true, TouchEmptyClose = true,AudioClip = AudioConfig.UI_SystemHint}},
            {MechaTreasureStartUI, new WndUICfg(){ResPath = "PrefabsN/UI/Activity/MechaTreasure/MechaTreasureStartUI", MaskBlack = true, TouchEmptyClose = true,AudioClip = AudioConfig.UI_SystemHint}},
            {DeepExploreStartUI, new WndUICfg(){ResPath = "PrefabsN/UI/Activity/DeepExplore/DeepExploreStartUI", MaskBlack = true, TouchEmptyClose = true,AudioClip = AudioConfig.UI_SystemHint}},
            {RouletteStartUI, new WndUICfg(){ResPath = "PrefabsN/UI/Activity/Roulette/RouletteStartUI", MaskBlack = true, TouchEmptyClose = true,AudioClip = AudioConfig.UI_SystemHint}},
            {RouletteSpecialUI, new WndUICfg(){ResPath = "PrefabsN/UI/Activity/Roulette/RouletteSpecialUI", TouchEmptyClose = true,AudioClip = AudioConfig.UI_SystemHint}},
            {DeepExploreShopUI, new WndUICfg(){ResPath = "PrefabsN/UI/Activity/DeepExplore/DeepExploreShopUI", MaskBlack = true, TouchEmptyClose = true,AudioClip = AudioConfig.UI_SystemHint}},
            {DeepExploreSellUI, new WndUICfg(){ResPath = "PrefabsN/UI/Activity/DeepExplore/DeepExploreSellUI", MaskBlack = true, TouchEmptyClose = true,AudioClip = AudioConfig.UI_SystemHint}},
            {DeepExploreExchangeUI, new WndUICfg(){ResPath = "PrefabsN/UI/Activity/DeepExplore/DeepExploreExchangeUI", MaskBlack = true, TouchEmptyClose = true,AudioClip = AudioConfig.UI_SystemHint}},
            {GameInfoUI,new WndUICfg(){ResPath = "PrefabsN/UI/Common/GameInfoUI", MaskBlack = true, TouchEmptyClose = true}},
            #endregion

            #region 功能预告
            {PreviewReceiveUI, new WndUICfg(){ResPath = "PrefabsN/UI/Preview/PreviewReceiveUI", MaskBlack = true, TouchEmptyClose = true,AudioClip = AudioConfig.UI_SystemHint}},
            {PreviewBuyGiftUI, new WndUICfg(){ResPath = "PrefabsN/UI/Preview/PreviewBuyGiftUI", MaskBlack = true, TouchEmptyClose = true,AudioClip = AudioConfig.UI_SystemHint}},
            {PreviewCommonGiftUI, new WndUICfg(){ResPath = "PrefabsN/UI/Preview/PreviewCommonGiftUI", MaskBlack = true, TouchEmptyClose = true,AudioClip = AudioConfig.UI_SystemHint}},
            {PreviewStorageGiftUI, new WndUICfg(){ResPath = "PrefabsN/UI/Preview/PreviewStorageGiftUI", MaskBlack = true, TouchEmptyClose = true,AudioClip = AudioConfig.UI_SystemHint}},
            {PreviewBuy_MessageBoxUI, new WndUICfg(){ResPath = "PrefabsN/UI/Preview/PreviewBuy_MessageBoxUI", MaskBlack = true, TouchEmptyClose = true,AudioClip = AudioConfig.UI_SystemHint}},
            #endregion

            {ActivityExchangeUI, new WndUICfg(){ResPath = "PrefabsN/UI/TimeLimitedRecharge/ActivityExchangeUI", MaskBlack = true, TouchEmptyClose = true,AudioClip = AudioConfig.UI_SystemHint}},
            {CircleActivityExchangeUI, new WndUICfg(){ResPath = "PrefabsN/UI/Activity/CircleActivityExchangeUI", MaskBlack = true, TouchEmptyClose = true,AudioClip = AudioConfig.UI_SystemHint}},

            #region 集字活动
            {GiftMainUI, new WndUICfg(){ResPath = "PrefabsN/UI/CollectActivity/GiftMainUI", MaskBlack = true, TouchEmptyClose = true}},
            {CollectActivityUI, new WndUICfg(){ResPath = "PrefabsN/UI/CollectActivity/CollectActivityUI", MaskBlack = true, TouchEmptyClose = true}},
            {GiftLogUI, new WndUICfg(){ResPath = "PrefabsN/UI/CollectActivity/GiftLogUI", MaskBlack = true, TouchEmptyClose = true}},
            {CollectAdUI, new WndUICfg(){ResPath = "PrefabsN/UI/CollectActivity/CollectAdUI", MaskBlack = true, TouchEmptyClose = true,AudioClip = AudioConfig.UI_SystemHint}},
            {ReceivedGiftUI, new WndUICfg(){ResPath = "PrefabsN/UI/CollectActivity/ReceivedGiftUI", MaskBlack = true, TouchEmptyClose = true}},

            #endregion

            {LongPressTipsUI, new WndUICfg(){ResPath = "PrefabsN/UI/Common/LongPressTipsUI", NoTouchMask = true, MaskBlack = false, TouchEmptyClose = false}},
            {AssistMainUI, new WndUICfg(){ResPath = "PrefabsN/UI/Assist/AssistMainUI", MaskBlack = true, TouchEmptyClose = true}},
            {AssistSkillPreviewUI, new WndUICfg(){ResPath = "PrefabsN/UI/Assist/AssistSkillPreviewUI", MaskBlack = true, TouchEmptyClose = true}},
            {AssistMechaListUI, new WndUICfg(){ResPath = "PrefabsN/UI/Assist/AssistMechaListUI", MaskBlack = true, TouchEmptyClose = true}},

            // 竞技场
            {ArenaMainUI, new WndUICfg(){ResPath = "PrefabsN/UI/Arena/ArenaMainUI", MaskBlack = true, ShowCurrencyTab = CurrencyTabUI.Arena}},
            {ArenaRankUI, new WndUICfg(){ResPath = "PrefabsN/UI/Arena/ArenaRankUI", MaskBlack = true, TouchEmptyClose = true}},
            {ArenaLineupUI, new WndUICfg(){ResPath = "PrefabsN/UI/Arena/ArenaLineupUI", MaskBlack = true}},
            {OtherLineupUI, new WndUICfg(){ResPath = "PrefabsN/UI/Arena/OtherLineupUI", MaskBlack = true, TouchEmptyClose = true}},
            {ArenaBattleLogUI, new WndUICfg(){ResPath = "PrefabsN/UI/Arena/ArenaBattleLogUI", MaskBlack = true,TouchEmptyClose = true}},
            {ArenaMechaDiyUI, new WndUICfg(){ResPath = "PrefabsN/UI/Arena/ArenaMechaDiyUI", MaskBlack = true}},
            {MessageBox_ArenaNumUI, new WndUICfg(){ResPath = "PrefabsN/UI/Arena/MessageBox_ArenaNumUI", MaskBlack = true,TouchEmptyClose = true}},
            {ArenaRandomCiTiao, new WndUICfg(){ResPath = "PrefabsN/UI/Arena/ArenaRandomCiTiao", MaskBlack = true}},

            //组队
            {TeamInstanceMainUI, new WndUICfg(){ResPath = "PrefabsN/UI/GamePlay/TeamInstance/TeamInstanceMainUI", MaskBlack = true, TouchEmptyClose = true}},
            {TeamInviteUI, new WndUICfg(){ResPath = "PrefabsN/UI/GamePlay/TeamInstance/TeamInviteUI", MaskBlack = false, TouchEmptyClose = false, NoTouchMask = true}},
            {TeamInviteListUI, new WndUICfg(){ResPath = "PrefabsN/UI/GamePlay/TeamInstance/TeamInviteListUI", MaskBlack = true, TouchEmptyClose = true}},
            {TeamBeInviteListUI, new WndUICfg(){ResPath = "PrefabsN/UI/GamePlay/TeamInstance/TeamBeInviteListUI", MaskBlack = true, TouchEmptyClose = true}},
            {TeamBeInviteTaskUI, new WndUICfg(){ResPath = "PrefabsN/UI/GamePlay/TeamInstance/TeamBeInviteTaskUI", MaskBlack = true, TouchEmptyClose = true}},
            {TeamAssistUI, new WndUICfg(){ResPath = "PrefabsN/UI/GamePlay/TeamInstance/TeamAssistUI", MaskBlack = true, TouchEmptyClose = true}},
            //装备
            {EquipmentMainUI, new WndUICfg(){ResPath = "PrefabsN/UI/Equipment/EquipmentMainUI", MaskBlack = true, TouchEmptyClose = true, OnUICloseListener = new (){EquipmentInfoAUI, EquipmentInfoBUI,GetRewardUI}}},
            {EquipmentAttrUI, new WndUICfg(){ResPath = "PrefabsN/UI/Equipment/EquipmentAttrUI", MaskBlack = true, TouchEmptyClose = true}},
            {EquipmentInfoAUI, new WndUICfg(){ResPath = "PrefabsN/UI/Equipment/EquipmentInfoAUI", MaskBlack = true, TouchEmptyClose = true, TouchEmptyCloseDelayTime = 0.2f}},
            {EquipmentInfoBUI, new WndUICfg(){ResPath = "PrefabsN/UI/Equipment/EquipmentInfoBUI", MaskBlack = true, TouchEmptyClose = true, TouchEmptyCloseDelayTime = 0.2f}},
            {EquipmentBreakDownUI, new WndUICfg(){ResPath = "PrefabsN/UI/Equipment/EquipmentBreakDownUI", MaskBlack = true, TouchEmptyClose = true}},
            {EquipmentCreateUI, new WndUICfg(){ResPath = "PrefabsN/UI/Equipment/EquipmentCreateUI", MaskBlack = true, TouchEmptyClose = true}},
            {EquipmentWeaponUI, new WndUICfg(){ResPath = "PrefabsN/UI/Equipment/EquipmentWeaponUI", MaskBlack = true, TouchEmptyClose = true}},
            {EquipmentSlotUI, new WndUICfg(){ResPath = "PrefabsN/UI/Equipment/EquipmentSlotUI", MaskBlack = true, TouchEmptyClose = true, ShowCurrencyTab = CurrencyTabUI.EquipmentSlot}},
            {EquipmentLvUpUI, new WndUICfg(){ResPath = "PrefabsN/UI/Equipment/EquipmentLvUpUI", MaskBlack = true, TouchEmptyClose = true, TouchEmptyCloseDelayTime = 0.2f}},
            {EquipmentAutoScrap, new WndUICfg(){ResPath = "PrefabsN/UI/Equipment/EquipmentAutoScrap", MaskBlack = true, TouchEmptyClose = true, TouchEmptyCloseDelayTime = 0.2f}},
            {EquipmentReforgeUI, new WndUICfg(){ResPath = "PrefabsN/UI/Equipment/EquipmentReforgeUI", MaskBlack = true, TouchEmptyClose = true, TouchEmptyCloseDelayTime = 0.2f}},
            //限时累抽
            {TimeLimitedRechargeUI, new WndUICfg(){ResPath = "PrefabsN/UI/TimeLimitedRecharge/TimeLimitedRechargeUI", MaskBlack = true, TouchEmptyClose = true, OnUICloseListener = new List<string>(){GetRewardUI, ActivityExchangeUI}}},
            {TimeLimitedRechargeStartUI, new WndUICfg(){ResPath = "PrefabsN/UI/TimeLimitedRecharge/TimeLimitedRechargeStartUI", MaskBlack = true, TouchEmptyClose = true, TouchEmptyCloseDelayTime = 0.2f}},
            //战力周期活动
            {CombatPower_MainUI, new WndUICfg(){ResPath = "PrefabsN/UI/Activity/CombatPower/CombatPower_MainUI", MaskBlack = true, TouchEmptyClose = true, TouchEmptyCloseDelayTime = 0.2f, OnUICloseListener = new (){GetRewardUI, ActivityExchangeUI}}},
            {CombatPower_StartUI, new WndUICfg(){ResPath = "PrefabsN/UI/Activity/CombatPower/CombatPower_StartUI", MaskBlack = true, TouchEmptyClose = true, TouchEmptyCloseDelayTime = 0.2f}},
            {CombatPower_egg, new WndUICfg(){ResPath = "PrefabsN/UI/Activity/CombatPower/CombatPower_egg", MaskBlack = true, TouchEmptyClose = true, TouchEmptyCloseDelayTime = 0.2f}},

            // 跨服竞技场
            {CSarenaMainUI, new WndUICfg(){ResPath = "PrefabsN/UI/GamePlay/CSarena/CSarenaMainUI", MaskBlack = true, TouchEmptyClose = true,OnUICloseListener = new (){CSarenaAircraftUI}}},
            {CSarenaListUI, new WndUICfg(){ResPath = "PrefabsN/UI/GamePlay/CSarena/CSarenaListUI", MaskBlack = true, TouchEmptyClose = true}},
            {CSarenaRankRewardUI, new WndUICfg(){ResPath = "PrefabsN/UI/GamePlay/CSarena/CSarenaRankRewardUI", MaskBlack = true, TouchEmptyClose = true}},
            {CSarenaRecordUI, new WndUICfg(){ResPath = "PrefabsN/UI/GamePlay/CSarena/CSarenaRecordUI", MaskBlack = true, TouchEmptyClose = true}},
            {CSarenaRankUpUI, new WndUICfg(){ResPath = "PrefabsN/UI/GamePlay/CSarena/CSarenaRankUpUI", MaskBlack = true, TouchEmptyClose = true}},
            {MessageBox_CSarenaNumUI, new WndUICfg(){ResPath = "PrefabsN/UI/GamePlay/CSarena/MessageBox_CSarenaNumUI", MaskBlack = true,TouchEmptyClose = true}},

            //跨服竞技场编队
            {CSarenaLineupUI, new WndUICfg(){ResPath = "PrefabsN/UI/GamePlay/CSarena/CSarenaLineupUI", MaskBlack = false, TouchEmptyClose = false, NoTouchMask = true}},
            {CSarenaAircraftUI, new WndUICfg(){ResPath = "PrefabsN/UI/GamePlay/CSarena/CSarenaAircraftUI", MaskBlack = false, TouchEmptyClose = false, NoTouchMask = true}},


            //机甲平台
            {AircraftMainUI, new WndUICfg(){ResPath = "PrefabsN/UI/Aircraft/AircraftMainUI", MaskBlack = false, TouchEmptyClose = false, NoTouchMask = true}},
            {AircraftListMainUI, new WndUICfg(){ResPath = "PrefabsN/UI/Aircraft/AircraftListMainUI", MaskBlack = false, TouchEmptyClose = false, NoTouchMask = true}},
            {AircraftRestoreUI, new WndUICfg(){ResPath = "PrefabsN/UI/Aircraft/AircraftRestoreUI", MaskBlack = true, TouchEmptyClose = true}},
            {AircraftReforgeLvUpUI, new WndUICfg(){ResPath = "PrefabsN/UI/Aircraft/AircraftReforgeLvUpUI", MaskBlack = true, TouchEmptyClose = true,TouchEmptyCloseDelayTime = 2f}},
            {AircraftReforgeListUI, new WndUICfg(){ResPath = "PrefabsN/UI/Aircraft/AircraftReforgeListUI", MaskBlack = true, TouchEmptyClose = true}},
            {AircraftUpgradeUI, new WndUICfg(){ResPath = "PrefabsN/UI/Aircraft/AircraftUpgradeUI", MaskBlack = true, TouchEmptyClose = true, OnUICloseListener = new (){}}},
            {AircraftUpgradeResultUI,new WndUICfg(){ResPath = "PrefabsN/UI/Aircraft/AircraftUpgradeResultUI",MaskBlack = true,TouchEmptyClose = true,TouchEmptyCloseDelayTime = 2f,AudioClip = AudioConfig.RankUp_Sucess,OnUICloseListener = new List<string>{GetRewardUI,MechaUpgradeResultUI}}},
            {AircraftGainedUI,new WndUICfg(){ResPath = "PrefabsN/UI/Aircraft/AircraftGainedUI",MaskBlack = true,TouchEmptyClose = true,TouchEmptyCloseDelayTime = 1,AudioClip = AudioConfig.CommonNew}},
            {AircraftLuckUpgradeResultUI,new WndUICfg(){ResPath = "PrefabsN/UI/Aircraft/AircraftLuckUpgradeResultUI",MaskBlack = true,TouchEmptyClose = false}},
            {AircraftPowerUp_MessageBoxUI,new WndUICfg(){ResPath = "PrefabsN/UI/Aircraft/AircraftPowerUp_MessageBoxUI",MaskBlack = true,TouchEmptyClose = true,TouchEmptyCloseDelayTime = 0.5f}},
            {AircraftPowerDown_MessageBoxUI,new WndUICfg(){ResPath = "PrefabsN/UI/Aircraft/AircraftPowerDown_MessageBoxUI",MaskBlack = true,TouchEmptyClose = true,TouchEmptyCloseDelayTime = 0.5f}},

            //战术特训
            {TacticStartUI, new WndUICfg(){ResPath = "PrefabsN/UI/Tactic/TacticStartUI", MaskBlack = true, TouchEmptyClose = true, TouchEmptyCloseDelayTime = 0.2f}},
            {TacticMainUI, new WndUICfg(){ResPath = "PrefabsN/UI/Tactic/TacticMainUI", MaskBlack = false,OnUICloseListener = new List<string>{GetRewardUI}}},
            {Tactic_MessageBoxUI, new WndUICfg(){ResPath = "PrefabsN/UI/Tactic/Tactic_MessageBoxUI", MaskBlack = true,TouchEmptyClose = true}},
            {TacticRankUI, new WndUICfg(){ResPath = "PrefabsN/UI/Tactic/TacticRankUI", MaskBlack = true,TouchEmptyClose = true}},
            {TacticBattleDetailsUI, new WndUICfg(){ResPath = "PrefabsN/UI/Tactic/TacticBattleDetailsUI", MaskBlack = true,TouchEmptyClose = true}},
            
            
            {OreMainUI, new WndUICfg(){ResPath = "PrefabsN/UI/Ore/OreMainUI", MaskBlack = true,TouchEmptyClose = true}},

        };
        static LDUICfg()
        {
            // CommonUI
            gUIInfo[CommonUI].OrderInfo[UIOrderInfo.Canvas1] = new UIOrderInfo() { PlaneDistance = 100, OrderInLayer = 30 };
            gUIInfo[CommonUI].OrderInfo[UIOrderInfo.Canvas2] = new UIOrderInfo() { PlaneDistance = 100, OrderInLayer = 35 };
            gUIInfo[CommonUI].OrderInfo[UIOrderInfo.Canvas3] = new UIOrderInfo() { PlaneDistance = 100, OrderInLayer = 40 };

            // 主界面
            gUIInfo[MainUI].OrderInfo[UIOrderInfo.Canvas1] = new UIOrderInfo() { PlaneDistance = 115, OrderInLayer = 30 };
            gUIInfo[MainUI].OrderInfo[UIOrderInfo.Canvas2] = new UIOrderInfo() { PlaneDistance = 110, OrderInLayer = 35 };
            gUIInfo[MainUI].OrderInfo[UIOrderInfo.Canvas3] = new UIOrderInfo() { PlaneDistance = 105, OrderInLayer = 40 };
            gUIInfo[MainUI].OrderInfo[UIOrderInfo.Canvas4] = new UIOrderInfo() { PlaneDistance = 100, OrderInLayer = 45 };

            // 商城
            gUIInfo[ShopUI].OrderInfo[UIOrderInfo.Canvas1] = new UIOrderInfo() { PlaneDistance = 100, OrderInLayer = 30 };

            // 机甲
            gUIInfo[MechaMainUI].OrderInfo[UIOrderInfo.Canvas1] = new UIOrderInfo() { PlaneDistance = 110, OrderInLayer = 30 };
            gUIInfo[MechaMainUI].OrderInfo[UIOrderInfo.Canvas2] = new UIOrderInfo() { PlaneDistance = 105, OrderInLayer = 35 };
            gUIInfo[MechaMainUI].OrderInfo[UIOrderInfo.Canvas3] = new UIOrderInfo() { PlaneDistance = 100, OrderInLayer = 40 };

            // 玩法
            // gUIInfo[GamePlayRootUI].OrderInfo[UIOrderInfo.Canvas1] = new UIOrderInfo() { PlaneDistance = 100, OrderInLayer = 30 };
            gUIInfo[CampsiteRootUI].OrderInfo[UIOrderInfo.Canvas1] = new UIOrderInfo() { PlaneDistance = 100, OrderInLayer = 30 };
            //工会
            gUIInfo[GuildMainUI].OrderInfo[UIOrderInfo.Canvas1] = new UIOrderInfo() { PlaneDistance = 100, OrderInLayer = 30 };

            gUIInfo[LoginErrorConfirm].OrderInfo[UIOrderInfo.MainCanvas] = new UIOrderInfo() { PlaneDistance = 10, OrderInLayer = 100 };
            gUIInfo[KickOutConfirm].OrderInfo[UIOrderInfo.MainCanvas] = new UIOrderInfo() { PlaneDistance = 10, OrderInLayer = 100 };
            gUIInfo[FlyItem].OrderInfo[UIOrderInfo.Canvas1] = new UIOrderInfo() { PlaneDistance = 10, OrderInLayer = 100 };
            gUIInfo[LoadingUI].OrderInfo[UIOrderInfo.Canvas1] = new UIOrderInfo() { PlaneDistance = 10, OrderInLayer = 90 };
            gUIInfo[ArenaLoadingUI].OrderInfo[UIOrderInfo.Canvas1] = new UIOrderInfo() { PlaneDistance = 10, OrderInLayer = 95 };
            gUIInfo[LeagueLoadingUI].OrderInfo[UIOrderInfo.Canvas1] = new UIOrderInfo() { PlaneDistance = 10, OrderInLayer = 95 };
            gUIInfo[UpConfirm].OrderInfo[UIOrderInfo.MainCanvas] = new UIOrderInfo() { PlaneDistance = 10, OrderInLayer = 100 };

        }
    }
}