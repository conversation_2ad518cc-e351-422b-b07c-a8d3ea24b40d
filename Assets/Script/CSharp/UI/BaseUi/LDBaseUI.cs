using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD
{
    public class LDUIDataBase
    {
    }

    public partial class LDBaseUI : LDBaseResUI
    {
        private WndUICfg m_UiInfo;
        protected string m_UIName = null;
        private bool m_TouchMaskFlag = false;
        private bool m_Inited = false;
        private TouchMask m_TouchMask;
        private List<int> m_TimerIds = new List<int>();
        private List<int> m_FrameTimerIds = new List<int>();
        private int m_CurrencyTab;
        private bool m_Preload = false;
        private double EnterTime;
        
        private UIReverse m_UIReverse;

        public void Init(string name, WndUICfg info)
        {
            m_UIReverse = this.gameObject.AddComponent<UIReverse>();
            EnterTime = DateTimeUtil.GetNowSecond();
            m_CurrencyTab = CurrencyTabUI.None;
            m_UIName = name;
            m_UiInfo = info;
            TryInitOnce();
            AddMsgListener();
            StartCoroutine(EndOfFrame());
            if (!string.IsNullOrEmpty(m_UiInfo.AudioClip))
            {
                StartCoroutine(DelayPlayClip());
            }
            LDSDKEvent.EnterUI(m_UIName);
            ChangeLanguage();
            OnInitImp();
        }

        /// <summary>
        /// 初始化工作
        /// 按钮监听  Item创建  等
        /// </summary>
        protected abstract void OnInitImp();

        /// <summary>
        /// UI关闭回调
        /// </summary>
        protected abstract void OnCloseImp();
        public void SetPreloadTrue()
        {
            m_Preload = true;
        }

        IEnumerator DelayPlayClip()
        {
            yield return new WaitForSeconds(m_UiInfo.AudiuClipDelayTime);
            if (!m_Preload)
            {
                Global.gApp.gAudioSource.PlayOneShot(m_UiInfo.AudioClip, DYAudioType.DYUnPauseAudio);
            }
        }
        IEnumerator EndOfFrame()
        {
            yield return new WaitForEndOfFrame();
            OnEndOfFrameCall();

            if (!m_UiInfo.MaskBlack)
            {
                if (m_TouchMask != null)
                {
                    m_TouchMask.SetGuassState(this, m_UiInfo.GaussMask);
                }
            }
        }

        public virtual void OnEndOfFrameCall()
        {
        }

        private void TryInitOnce()
        {
            if (!m_Inited)
            {
                CreateTouchMask();
                InitOnceInfo();
            }
        }

        private void InitOnceInfo()
        {
            m_Inited = true;
        }

        private void CreateTouchMask()
        {
            if (!m_UiInfo.NoTouchMask && !m_Inited)
            {
                GameObject touchMask = Global.gApp.gResMgr.InstantiateLoadObj(LDUICfg.TouchMaskUi, ResSceneType.Resident);
                Canvas[] canvas = gameObject.GetComponentsInChildren<Canvas>();
                if (canvas.Length > 0)
                {
                    Canvas minOrderCanvas = canvas[0];
                    foreach (Canvas mCanvas in canvas)
                    {
                        if (mCanvas.sortingOrder < minOrderCanvas.sortingOrder)
                        {
                            minOrderCanvas = mCanvas;
                        }
                    }

                    touchMask.transform.SetParent(minOrderCanvas.transform, false);
                }

                touchMask.transform.SetAsFirstSibling();
                m_TouchMask = touchMask.GetComponent<TouchMask>();
                m_TouchMask.SetMaskEnable(m_UiInfo.MaskBlack);

                if (m_UiInfo.TouchEmptyClose)
                {
                    m_TouchMask.AddCloseListener(m_UIName, m_UiInfo.TouchEmptyCloseDelayTime, this);
                }
            }
        }

        public bool TryShowTabUI()
        {
            bool isShow = false;
            if (m_UiInfo.ShowCurrencyTab > 0)
            {
                if (m_CurrencyTab != CurrencyTabUI.None)
                {
                    ShowCurrency(m_CurrencyTab);
                }
                else
                {
                    ShowCurrency(m_UiInfo.ShowCurrencyTab);
                }

                isShow = true;
            }

            if (m_UiInfo.ShowMenuTab)
            {
                ShowMenuTab();
                isShow = true;
            }

            return isShow;
        }

        protected void CloseUI()
        {
            RemoveAllTimer();
            RemoveAllFrameTimer();
            RemoveMsgListener();
            RemoveTouchMask();
            RemoveTimeTouchMask();
            if (!LDUICfg.IgnoreCloseBroadcastUIs.Contains(GetName()))
            {
                Global.gApp.gMsgDispatcher.Broadcast<string>(MsgIds.OnCloseUI, m_UIName);
            }
            LDSDKEvent.ExitUI(m_UIName, DateTimeUtil.GetNowSecond() - EnterTime);
            OnCloseImp();
        }

        public void Release()
        {
            CloseUI();
            // release Content
            Global.gApp.gResMgr.DestroyGameObj(GetRootNode());
        }

        public GameObject GetRootNode()
        {
            GameObject rootNode = null;
            ;
            if (m_UiInfo != null && GetComponentInChildren<Canvas>() == null)
            {
                rootNode = gameObject.transform.parent.gameObject;
            }
            else
            {
                rootNode = gameObject;
            }

            return rootNode;
        }

        public virtual void TouchClose()
        {
            Global.gApp.gUiMgr.CloseUI(m_UIName);
        }

        public TouchMask GetTouchMask()
        {
            return m_TouchMask;
        }

        public string GetName()
        {
            return m_UIName;
        }

        public WndUICfg GetUiInfo()
        {
            return m_UiInfo;
        }

        private void AddMsgListener()
        {
            RemoveMsgListener();
            RegEvent(true);
        }

        private void RemoveMsgListener()
        {
            RegEvent(false);
        }

        private void OnCloseOtherUI(string uiName)
        {
            if (m_UiInfo.OnUICloseListener.Count == 0 || m_UiInfo.OnUICloseListener.Contains(uiName))
            {
                OnOtherUICloseFresh(uiName);
            }
        }

        protected virtual void OnOtherUICloseFresh(string uiName)
        {
        }

        protected void RegEvent(bool addListener)
        {
            Global.gApp.gMsgDispatcher.RegEvent(MsgIds.Language, ChangeLanguage, addListener);
            if (m_UiInfo.OnUICloseListener != null)
            {
                Global.gApp.gMsgDispatcher.RegEvent<string>(MsgIds.OnCloseUI, OnCloseOtherUI, addListener);
            }

            RegEventImp(addListener);
        }

        protected virtual void RegEventImp(bool addListener)
        {
        }

        public void RemoveTouchMask()
        {
            if (m_TouchMaskFlag)
            {
                m_TouchMaskFlag = false;
                Global.gApp.gGameCtrl.RemoveGlobalTouchMask();
            }
        }

        public void AddTouchMask()
        {
            if (!m_TouchMaskFlag)
            {
                m_TouchMaskFlag = true;
                Global.gApp.gGameCtrl.AddGlobalTouchMask();
            }
        }

        private GameObject m_TouchGo;

        protected void AddTimeTouchMask(float time)
        {
            m_TouchGo = Global.gApp.gResMgr.InstantiateLoadObj(LDUICfg.TouchMaskUi, ResSceneType.Resident);
            m_TouchGo.transform.SetParent(Global.gApp.gUiMgr.GetTopCanvasTsf(), false);
            m_TouchGo.transform.SetAsFirstSibling();
            GameObject.Destroy(m_TouchGo, time);
        }

        protected void RemoveTimeTouchMask()
        {
            if (m_TouchGo != null)
            {
                Global.gApp.gResMgr.DestroyGameObj(m_TouchGo);
                m_TouchGo = null;
            }
        }

        public void ShowCurrency(int tab)
        {
            m_CurrencyTab = tab;
            GetNextOrder(out int dis, out int order);
            Global.gApp.gUiMgr.SetTabUI(tab, order, dis);
        }

        public void HideCurrency()
        {
            m_CurrencyTab = CurrencyTabUI.None;
            m_UiInfo.ShowCurrencyTab = CurrencyTabUI.None;
            Global.gApp.gUiMgr.HideTabUI();
        }

        // 强刷资源栏 且写入配置!!!  给类似于ActivityUI用
        public void ForceHideCurrency()
        {
            m_CurrencyTab = CurrencyTabUI.None;
            m_UiInfo.ShowCurrencyTab = CurrencyTabUI.None;
            Global.gApp.gUiMgr.HideTabUI();
        }

        // 强刷资源栏 且写入配置  给类似于ActivityUI用
        public void ForceShowCurrency(int tab)
        {
            m_UiInfo.ShowCurrencyTab = tab;
            ShowCurrency(tab);
        }

        public void ShowMenuTab()
        {
            GetNextOrder(out int dis, out int order);
            Global.gApp.gUiMgr.SetMenuTab(order, dis);
        }


        public int AddTimer(float dtTime, int callTimes, System.Action<float, bool> callBack)
        {
            int timerId = Global.gApp.gUiMgr.gTimerMgr.AddTimer(dtTime, callTimes, callBack);
            m_TimerIds.Add(timerId);
            return timerId;
        }

        public void RemoveTimer(int timerId)
        {
            Global.gApp.gUiMgr.gTimerMgr.RemoveTimer(timerId);
        }

        private void RemoveAllTimer()
        {
            foreach (int timerId in m_TimerIds)
            {
                RemoveTimer(timerId);
            }

            m_TimerIds.Clear();
        }

        public int AddFrameTimer(int dtFrame, int callTimes, System.Action<float, bool> callBack)
        {
            int frameTimerId = Global.gApp.gFrameTimerMgrRender.AddFrameTimer(dtFrame, callTimes, callBack);
            m_FrameTimerIds.Add(frameTimerId);
            return frameTimerId;
        }

        public void RemoveFrameTimer(int frameTimerId)
        {
            Global.gApp.gFrameTimerMgrRender.RemoveTimer(frameTimerId);
        }

        private void RemoveAllFrameTimer()
        {
            foreach (int timerId in m_FrameTimerIds)
            {
                RemoveFrameTimer(timerId);
            }

            m_FrameTimerIds.Clear();
        }


        public void GetMaxOrder(out int planeDistance, out int order)
        {
            planeDistance = 100;
            order = 30;

            Canvas[] canvas = GetRootNode().GetComponentsInChildren<Canvas>();
            int minPlaneDistance = 500;
            int maxOrder = -500;
            foreach (Canvas _cvs in canvas)
            {
                if (_cvs.planeDistance < minPlaneDistance)
                {
                    minPlaneDistance = (int)_cvs.planeDistance;
                }

                if (_cvs.sortingOrder > maxOrder)
                {
                    maxOrder = _cvs.sortingOrder;
                }
            }

            if ((LDFightConstVal.UICameraMask & gameObject.layer) != 0)
            {
                ParticleSystem[] particleSystems = gameObject.GetComponentsInChildren<ParticleSystem>();
                Renderer renderer;
                foreach (ParticleSystem _ps in particleSystems)
                {
                    if (_ps.TryGetComponent(out renderer))
                    {
                        if (renderer.sortingOrder > maxOrder)
                        {
                            maxOrder = renderer.sortingOrder;
                        }
                    }
                }
            }

            planeDistance = Mathf.Min(planeDistance, minPlaneDistance);
            planeDistance = Mathf.Max(5, planeDistance); // 不低于5
            order = Mathf.Max(order, maxOrder);
        }

        public void GetNextOrder(out int planeDistance, out int order)
        {
            GetMaxOrder(out planeDistance, out order);
            order += 5;
            planeDistance -= 5;
            planeDistance = Mathf.Max(5, planeDistance); // 不低于5
        }


    }
}