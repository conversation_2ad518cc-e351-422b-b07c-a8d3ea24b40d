using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class MechaMainUI
    {
        private LDNetMechaMgr m_MechaMgr;
        private LDNetMechaDTOItem m_Mecha;
        private MechaItem m_MechaCfg;
        private CommonModelUI m_CommonModel;
        private bool m_PreventLvUp = false;
        private ButtonOnPressed m_ButtonOnPressed;
        private long m_LastAniTime;

        protected override void OnInitImp()
        {
            m_MechaMgr = Global.gApp.gSystemMgr.gMechaDataMgr;
            m_ChipBtn.button.AddListener(OnChip);
            m_ChipLockBtn.button.AddListener(OnChip);
            m_PartBtn.button.AddListener(OnPart);
            m_PartLockBtn.button.AddListener(OnPart);
            m_RefitBtn.button.AddListener(OnRefit);
            m_RefitLockBtn.button.AddListener(OnRefit);
            m_EnhancementBtn.button.AddListener(OnEnhancement);
            m_EnhancementLockBtn.button.AddListener(OnEnhancement);
            m_AssistBtn.AddListener(OnAssistClick);
            m_AssistLockBtn.button.AddListener(OnAssistClick);
            m_UpgradeBtn.AddListenerNoAudio(OnUpgrade);
            m_AttrDetailBtn.button.AddListener(OnAttrDetail);
            // m_LevelUpBtn.AddListenerNoAudio(OnLevelUp);
            m_MechaListBtn.button.AddListener(OnMechaList);
            
            m_ButtonOnPressed = m_LevelUpBtn.gameObject.GetComponent<ButtonOnPressed>();
            m_ButtonOnPressed.PressAction = OnLevelUp;
            m_ButtonOnPressed.IgnorePointerExit = true;

            m_CommonModel = m_CommonModelUI.gameObject.GetComponent<CommonModelUI>();
            m_CommonModel.RenderCamera.fieldOfView = 48;

            OnFreshUI();
            TryGuide();
        }

        private void TryGuide()
        {
            Global.gApp.gSystemMgr.gGuideMgr.RemoveGuide(LDGuideType.FirstDIY, 0);
            if (Global.gApp.gSystemMgr.gGuideMgr.AddGuide(LDGuideType.FirstDIY, m_RefitBtn.rectTransform, 1)) { return; }

            Global.gApp.gSystemMgr.gGuideMgr.RemoveGuide(LDGuideType.SecondDIY, 0);
            if (Global.gApp.gSystemMgr.gGuideMgr.AddGuide(LDGuideType.SecondDIY, m_RefitBtn.rectTransform, 1)) { return; }

            Global.gApp.gSystemMgr.gGuideMgr.RemoveGuide(LDGuideType.MechaLvUp, 0);
            if (Global.gApp.gSystemMgr.gGuideMgr.AddGuide(LDGuideType.MechaLvUp, m_LevelUpBtn.rectTransform, 1)) { return; }

            Global.gApp.gSystemMgr.gGuideMgr.RemoveGuide(LDGuideType.MechaUpgrade, 0);
            if (Global.gApp.gSystemMgr.gGuideMgr.AddGuide(LDGuideType.MechaUpgrade, m_UpgradeBtn.rectTransform, 1)) { return; }

            Global.gApp.gSystemMgr.gGuideMgr.RemoveGuide(LDGuideType.MechaChange, 0);
            if (Global.gApp.gSystemMgr.gGuideMgr.AddGuide(LDGuideType.MechaChange, m_MechaListBtn.rectTransform, 1)) { return; }
            
            Global.gApp.gSystemMgr.gGuideMgr.RemoveGuide(LDGuideType.UAV, 0);
            if (Global.gApp.gSystemMgr.gGuideMgr.AddGuide(LDGuideType.UAV, m_ChipBtn.rectTransform, 1)) { return; }
            
            Global.gApp.gSystemMgr.gGuideMgr.RemoveGuide(LDGuideType.Enhancement, 0);
            if (Global.gApp.gSystemMgr.gGuideMgr.AddGuide(LDGuideType.Enhancement, m_EnhancementBtn.rectTransform, 1)) { return; }
            
            Global.gApp.gSystemMgr.gGuideMgr.RemoveGuide(LDGuideType.Assist, 0);
            if (Global.gApp.gSystemMgr.gGuideMgr.AddGuide(LDGuideType.Assist, m_AssistBtn.rectTransform, 1)) { return; }
        }

        public override void OnFreshUI()
        {
            m_Mecha = m_MechaMgr.GetCurBattleMecha();
            if (m_Mecha == null) { return; }

            m_MechaCfg = Mecha.Data.Get(m_Mecha.MechaId);
            if (m_MechaCfg == null) { return; }

            RefreshBaseInfo();
            RefreshAttrNode();
            RefreshUpgradeBtn();
            RefreshModelNode();
            RefreshEffect();
            RefreshFuncBtns();
        }

        public override void OnFreshUI(int val)
        {
            if(val == 1) // 刷模型
            {
                RefreshModelNode();
            }
            if (val == 2) // 升级
            {
                RefreshBaseInfo();
                RefreshAttrNode();
                RefreshUpgradeBtn();
                RefreshFuncBtns();
                PlayLvUpAni();
                m_PreventLvUp = false;
                
                CommonUI commonUI = Global.gApp.gUiMgr.GetOpenPanelCompent(LDUICfg.CommonUI) as CommonUI;
                commonUI.FreshCurrrency(LDSpecialItemId.Gold);
            }
        }

        // 基础信息
        private void RefreshBaseInfo()
        {
            // NameNode
            LDUIPrefabTools.InitNameNodeForMecha(m_NameNode.gameObject, m_MechaCfg.id);

            // 称号
            m_Title.text.SetTips(m_MechaCfg.MechaTitle);

            // 战力
            m_Power.text.SetText(Global.gApp.gSystemMgr.gRoleMgr.GetPowerStr());

            // 隐藏特效
            m_UpgradeEffect.gameObject.SetActive(false);
        }

        // 属性
        private void RefreshAttrNode()
        {
            LDNetRoleMgr roleMgr = Global.gApp.gSystemMgr.gRoleMgr;
            MechaQualityItem quaCfg = MechaQuality.Data.Get(m_Mecha.Quality);
            m_LevelValue.text.SetText($"{m_MechaMgr.GetCurMechaLevel()}");
            m_LevelValueMax.text.SetText($"/{quaCfg.lvLimit.ToString()}");
            m_AtkValue.text.SetText(roleMgr.GetAttrByType(LDAttrEnum.Attack).GetValueStr());
            m_DefValue.text.SetText(roleMgr.GetAttrByType(LDAttrEnum.Defense).GetValueStr());
            m_HpValue.text.SetText(roleMgr.GetAttrByType(LDAttrEnum.Hp).GetValueStr());
        }

        // 升级
        private void RefreshUpgradeBtn()
        {
            m_MechaMgr.GetLevelUpCost(out long ownedNum, out long num);
            m_LevelUpCost.text.SetCostText(ownedNum, num);
            m_FullLv.gameObject.SetActive(m_MechaMgr.IsMaxLevel() && m_Mecha.Quality >= LDQualityType.Max);
        }

        // 模型
        private void RefreshModelNode()
        {
            GameObject model = m_CommonModel.InitCreatureDIY(m_ModelImage.rawImage, m_Mecha.MechaId, 1,true);
            m_CommonModel.SetRootPosX(2000);
            model.transform.localEulerAngles = new Vector3(m_MechaCfg.mechaXYZ[0], m_MechaCfg.mechaXYZ[1], m_MechaCfg.mechaXYZ[2]);
            model.transform.localScale = Vector3.one * m_MechaCfg.mechaScale;
        }

        // 特效
        private void RefreshEffect()
        {
            LDCommonTools.DestoryChildren(m_guang_image.rectTransform);
            string path = LDUIResTools.GetQualityStreamerEffectPath(m_Mecha.Quality);
            InstanceUIEffectNode(path, ResSceneType.NormalRes, m_guang_image.rectTransform);
        }

        // 升级动画
        private void PlayLvUpAni()
        {
            m_UpgradeEffect.gameObject.SetActive(true);
            m_LevelValue.animator.Play("levelup", -1, 0);
            
            if (DateTimeUtil.GetServerTime() - m_LastAniTime < 500)
            {
                return;
            }
            m_LastAniTime = DateTimeUtil.GetServerTime();
            Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.NormalUp);
        }

        // 其他功能入口按钮
        private void RefreshFuncBtns()
        {
            bool uavUnlock = Global.gApp.gSystemMgr.gUAVMgr.IsUnlock();
            m_ChipBtn.gameObject.SetActive(uavUnlock);
            m_ChipLockBtn.gameObject.SetActive(!uavUnlock);

            bool partUnlock = Global.gApp.gSystemMgr.gMechaPartDataMgr.IsUnlock();
            m_PartBtn.gameObject.SetActive(partUnlock);
            m_PartLockBtn.gameObject.SetActive(!partUnlock);

            bool diyUnlock = Global.gApp.gSystemMgr.gDIYMgr.IsUnlock();
            m_RefitBtn.gameObject.SetActive(diyUnlock);
            m_RefitLockBtn.gameObject.SetActive(!diyUnlock);

            bool enhancementUnlock = Global.gApp.gSystemMgr.gEnhancementMgr.IsUnlock();
            m_EnhancementBtn.gameObject.SetActive(enhancementUnlock);
            m_EnhancementLockBtn.gameObject.SetActive(!enhancementUnlock);

            bool assistUnlock = Global.gApp.gSystemMgr.gAssistMgr.IsUnlock();
            m_AssistBtn.gameObject.SetActive(assistUnlock);
            m_AssistLockBtn.gameObject.SetActive(!assistUnlock);
        }

        private void OnChip()
        {
            Global.gApp.gSystemMgr.gGuideMgr.RemoveGuide(LDGuideType.UAV, 1);
 
            if (Global.gApp.gSystemMgr.gUAVMgr.IsUnlock(true))
            {
                Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.UAVMainUI);
            }
        }

        private void OnPart()
        {
            if (Global.gApp.gSystemMgr.gMechaPartDataMgr.IsUnlock(true))
            {
               Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.DiyPartListUI);
            }
        }

        private void OnRefit()
        {
            if (Global.gApp.gSystemMgr.gDIYMgr.IsUnlock(true))
            {
                Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.MechaDiyUI);
            }
        }

        private void OnEnhancement()
        {
            Global.gApp.gSystemMgr.gGuideMgr.RemoveGuide(LDGuideType.Enhancement, 1);
            
            if (Global.gApp.gSystemMgr.gEnhancementMgr.IsUnlock(true))
            {
                Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.Enhancement_mainUI);
            }
        }

        private void OnAssistClick()
        {
            Global.gApp.gSystemMgr.gGuideMgr.RemoveGuide(LDGuideType.Assist, 1);
            
            if (Global.gApp.gSystemMgr.gAssistMgr.IsUnlock(true))
            {
                Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.AssistMainUI);
            }
        }

        private void OnUpgrade()
        {
            Global.gApp.gSystemMgr.gGuideMgr.RemoveGuide(LDGuideType.MechaUpgrade, 1);

            // if (m_Mecha.Quality >= LDQualityType.Max)
            // {
            //     PlayCommonWrongAudioClip();
            //     Global.gApp.gSystemMgr.gCommonHandleMgr.ShowErrorTips(10);
            //     return;
            // }
            Global.gApp.gUiMgr.OpenUIAsync<MechaUpgradeUI>(LDUICfg.MechaUpgradeUI).SetLoadedCall(ui =>
            {
                ui?.RefreshUI(m_Mecha.MechaId);
            });
            PlayCommonAudioClip();
        }

        private void OnAttrDetail()
        {
            Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.AttrInfo);
        }

        private void OnLevelUp()
        {
            if (m_PreventLvUp)
            {
                return;
            }
            
            Global.gApp.gSystemMgr.gGuideMgr.RemoveGuide(LDGuideType.MechaLvUp, 1);
            
            if (Global.gApp.gUiMgr.GetOpenPanelCompent(LDUICfg.MechaLvPackMain))
            {
                m_ButtonOnPressed.ForcePointerExit();
                return;
            }

            if (m_MechaMgr.IsMaxLevel())
            {
                m_ButtonOnPressed.ForcePointerExit();
                if(m_Mecha.Quality >= LDQualityType.Max)
                {
                    PlayCommonWrongAudioClip();
                    Global.gApp.gToastMgr.ShowGameTips(91172);
                }
                else
                {
                    if (Global.gApp.gUiMgr.GetOpenPanelCompent(LDUICfg.MechaUpgradeUI))
                    {
                        return;
                    }
                    
                    Global.gApp.gUiMgr.OpenUIAsync<MechaUpgradeUI>(LDUICfg.MechaUpgradeUI).SetLoadedCall(ui =>
                    {
                        ui?.RefreshUI(m_Mecha.MechaId);
                        PlayCommonWrongAudioClip();
                        Global.gApp.gToastMgr.ShowGameTips(95006);
                    });
                }
                return;
            }

            m_MechaMgr.GetLevelUpCost(out long ownedNum, out long needNum);
            if (ownedNum < needNum)
            {
                if (Global.gApp.gUiMgr.GetOpenPanelCompent(LDUICfg.QuickBuyDiamondUI))
                {
                    return;
                }
                if (Global.gApp.gUiMgr.GetOpenPanelCompent(LDUICfg.MessageBox_KuaiSuHuoQu))
                {
                    return;
                }

                Global.gApp.gUiMgr.TryShowQuickBuyGold(needNum);
                PlayCommonWrongAudioClip();
                m_ButtonOnPressed.ForcePointerExit();
            }
            else
            {
                PlayCommonAudioClip();
                m_MechaMgr.SendMechaLvUp(m_Mecha.MechaId);
                m_PreventLvUp = true;
                
                if (!m_ButtonOnPressed.GetIsDown())
                {
                    m_MechaMgr.CheckLvUpLongPressTips();
                }
            }
        }

        private void OnMechaList()
        {
            Global.gApp.gSystemMgr.gGuideMgr.RemoveGuide(LDGuideType.MechaChange, 1);

            Global.gApp.gUiMgr.OpenUIAsync<MechaListMainUI>(LDUICfg.MechaListMainUI).SetLoadedCall(ui =>
            {
                ui?.RefreshUI(m_Mecha.MechaId);
            });
        }

        protected override void OnOtherUICloseFresh(string uiName)
        {
            if (uiName == LDUICfg.MessageBox_KuaiSuHuoQu)
            {
                RefreshUpgradeBtn();
            }
            if (uiName.Equals(LDUICfg.MechaListMainUI))
            {
                Global.gApp.gSystemMgr.gGuideMgr.AddGuide(LDGuideType.MechaChange, m_RefitBtn.rectTransform, 5);
            }
        }

        protected override void OnCloseImp()
        {
            Global.gApp.gUiMgr.CloseUI(LDUICfg.LongPressTipsUI);
        }
    }
}