using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD
{
    public partial class GiftMainUI
    {
        private readonly List<CollectActivityItem> m_CfgListItems = new List<CollectActivityItem>();
        private readonly List<GiftMainUI_item> m_SelectItems = new List<GiftMainUI_item>();

        private GiftMainUI_item m_CurSelectItem;
        private long m_SelectNum;

        private LDRoleInfoDTO friendInfo;

        protected override void OnInitImp()
        {
            m_btn_close.AddListener(TouchClose);
            m_Add_Button.AddListener(OnClickAdd);
            m_Reduce_Button.AddListener(OnClickReduce);

            m_Orange_btn_Gift.AddListener(OnClickSendGift);
        }

        protected override void RegEventImp(bool addListener)
        {
            base.RegEventImp(addListener);

            Global.gApp.gMsgDispatcher.RegEvent(MsgIds.OnServerCrossDay, OnServerCrossDay, addListener);
        }

        private void OnServerCrossDay()
        {
            OnFreshUI(0);
        }

        public override void OnFreshUI()
        {
        }

        public override void OnFreshUI(int value)
        {
            m_SelectNum = 0;
            RefreshItemsInfo();
            SetSelectNum();
            SetBtnInfo();
        }

        public void InitData(LDRoleInfoDTO roleInfo) //设置 服务器给的数据 cfg
        {
            friendInfo = roleInfo;

            m_CfgListItems.Clear();
            foreach (CollectActivityItem cfgItem in CollectActivity.Data.items)
            {
                if (cfgItem.activityId != Global.gApp.gSystemMgr.gCollectActivityMgr.Data.ActivityInfo.activityId)
                {
                    continue;
                }

                if (cfgItem.type == 1)
                {
                    m_CfgListItems.Add(cfgItem);
                }
            }

            SetInfo();
        }

        private void SetInfo()
        {
            InitItemsInfo();
            SetFriendInfo();
        }

        private void SetSelectNum()
        {
            m_Input_ui.inputField.text = m_SelectNum.ToString();
            m_Input_ui.inputField.enabled = false;
        }

        private void InitItemsInfo()
        {
            m_SelectItems.Clear();
            m_item.CacheInstanceList();
            for (int idx = 0; idx < m_CfgListItems.Count; idx++)
            {
                GiftMainUI_item itemScript = m_item.GetInstance(true);
                Toggle tg = itemScript.gameObject.GetComponent<Toggle>();
                tg.group = m_Content.gameObject.GetComponent<ToggleGroup>();
                itemScript.RefreshUI(m_CfgListItems[idx], OnSelectItemBack);
                tg.isOn = false;
                if (m_CurSelectItem == null && itemScript.ShowItemNum > 0)
                {
                    m_CurSelectItem = itemScript;
                }
                m_SelectItems.Add(itemScript);
            }
            if (m_CurSelectItem == null)
            {
                m_CurSelectItem = m_SelectItems[0];
            }

            bool isHaveOne = false;
            foreach (GiftMainUI_item itemScript in m_SelectItems)
            {
                if (itemScript.ShowItemNum > 0)
                {
                    isHaveOne = true;
                    itemScript.ToggleItem.isOn = true;
                    break;
                }
            }
            if (!isHaveOne)
            {
                m_SelectItems[0].ToggleItem.isOn = true;
            }
            SetSelectShow();
        }

        private void RefreshItemsInfo()
        {
            for (int idx = 0; idx < m_SelectItems.Count; idx++)
            {
                GiftMainUI_item itemScript = m_SelectItems[idx];
                itemScript.RefreshUI(m_CfgListItems[idx], OnSelectItemBack);
            }
        }

        private void OnSelectItemBack(GiftMainUI_item itemScript)
        {
            m_SelectNum = 0;

            m_CurSelectItem = itemScript;
            SetSelectShow();
        }

        private void SetSelectShow()
        {
            LDCommonItem item1 = new LDCommonItem(m_CurSelectItem.CfgItem.needItem);
            LDCommonTools.DestoryChildren(m_ItemDec.rectTransform);
            item1.SetNum(0);
            LDUIPrefabTools.GetKnapsackItemUI(item1, m_ItemDec.rectTransform);

            SetSelectNum();
            SetBtnInfo();
        }

        private void SetFriendInfo()
        {
            LDUIPrefabTools.InitOtherRoleHead(RoleHead.gameObject, friendInfo.RoleHeadInfo);
            m_List_PlayerName_Txt.text.SetText(friendInfo.Name);
            m_Level_Txt.text.SetText(friendInfo.Level);
        }

        private void SetBtnInfo()
        {
            m_Orange_btn_Gift.gameObject.SetActive(m_SelectNum > 0);
            m_Grey_btn_Gift.gameObject.SetActive(m_SelectNum <= 0);
        }
        private void OnClickAdd()
        {
            m_SelectNum++;
            m_SelectNum = Math.Min(m_SelectNum, m_CurSelectItem.ShowItemNum);
            SetSelectNum();
            SetBtnInfo();
        }

        private void OnClickReduce()
        {
            m_SelectNum--;
            m_SelectNum = Math.Max(m_SelectNum, 0);
            SetSelectNum();
            SetBtnInfo();
        }

        private void OnClickSendGift()
        {
            LDCommonItem item1 = new LDCommonItem(m_CurSelectItem.CfgItem.needItem);

            long receiver = friendInfo.PlayerId;
            int itemId = item1.Id;
            int count = (int)m_SelectNum;
            int activityId = m_CurSelectItem.CfgItem.activityId;
            int id = m_CurSelectItem.CfgItem.id;

            Global.gApp.gSystemMgr.gCollectActivityMgr.SendFriendGiftRequest(
                receiver, itemId, count, activityId, id);
        }

        protected override void OnCloseImp()
        {

        }
    }

    public partial class GiftMainUI_item
    {
        public long ShowItemNum;
        public CollectActivityItem CfgItem;
        public Toggle ToggleItem;
        private Action<GiftMainUI_item> m_SelectBack;
        public void RefreshUI(CollectActivityItem cfgItem, Action<GiftMainUI_item> selectBack)
        {
            CfgItem = cfgItem;
            m_SelectBack = selectBack;

            CreateItemUI(cfgItem, m_itemNode.rectTransform);
            ToggleItem = this.gameObject.GetComponent<Toggle>();
            ToggleItem.onValueChanged.AddListener(OnSelect);
        }

        private void OnSelect(bool isOn)
        {
            if (isOn)
            {
                m_SelectBack(this);
            }
        }

        private void CreateItemUI(CollectActivityItem cfgItem, RectTransform parent)  //设置数量
        {
            LDCommonItem item = new LDCommonItem(cfgItem.needItem);

            Global.gApp.gSystemMgr.gCollectActivityMgr.Data.CollectInfoDic.TryGetValue(cfgItem.id, out int exchangeNum);
            long bagNum = Global.gApp.gSystemMgr.gBagMgr.GetItemCount(item.Id);
            bool isValid = Global.gApp.gSystemMgr.gBagMgr.ItemValid(item.Id);
            if (!isValid)
            {
                bagNum = 0;
            }

            long showNum = bagNum - (cfgItem.activityNum - exchangeNum) * item.Num;
            ShowItemNum = Math.Max(showNum, 0);

            item.SetNum(ShowItemNum);

            LDCommonTools.DestoryChildren(parent);

            KnapsackItem itemUI = LDUIPrefabTools.GetKnapsackItemUI(item, parent);
            itemUI.SkinNotOwn.gameObject.SetActive(false);
            if (ShowItemNum == 0)
            {
                itemUI.Num_bg.gameObject.SetActive(true);
                itemUI.Num.text.SetText(0);
                itemUI.Nothing.gameObject.SetActive(true);
            }
            else
            {
                itemUI.Nothing.gameObject.SetActive(false);
            }
        }
    }
}