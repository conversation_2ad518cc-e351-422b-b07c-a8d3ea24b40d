using System;
using System.Collections.Generic;
using System.Linq;
using Google.Protobuf.Collections;
using LD.Protocol;

namespace LD
{
    public class LDNetAircraftDTO
    {
        /// 平台信息
        public Dictionary<int, LDNetAircraftInfo> AircraftInfos = new();

        /// 上阵
        public LDNetAircraftSlotInfo AircraftSlotInfo = new LDNetAircraftSlotInfo();

        /// ss机甲平台获取信息
        public Dictionary<int, LDNetAircraftSupremeInfo> AircraftSupremeInfos = new Dictionary<int, LDNetAircraftSupremeInfo>();

        public int RefinementLevel = 0;
        public int RefinementExp = 0;

        /// <summary>
        /// 机甲平台数据同步
        /// </summary>
        /// <param name="info"></param>
        public void SyncAircraftInfo(AircraftInfo info)
        {
            if (AircraftInfos.TryGetValue(info.AircraftId, out var aircraftInfo))
            {
                aircraftInfo.SyncAircraftInfo(info);
            }
            else
            {
                AircraftInfos.Add(info.AircraftId, new LDNetAircraftInfo(info));
            }
        }

        /// <summary>
        /// 同步上阵数据
        /// </summary>
        /// <param name="slotInfo"></param>
        public void SyncAircraftSlotInfo(AircraftSlotInfo slotInfo)
        {
            AircraftSlotInfo.SyncSlotInfo(slotInfo.SlotInfo);
        }

        /// <summary>
        /// 同步ss平台获取信息
        /// </summary>
        /// <param name="aircraftId"></param>
        /// <param name="isGet"></param>
        public void SyncAircraftSupremeInfo(int aircraftId, bool isGet)
        {
            if (AircraftSupremeInfos.ContainsKey(aircraftId))
            {
                AircraftSupremeInfos[aircraftId].SyncAircraftSupremeInfo(isGet);
            }
            else
            {
                AircraftSupremeInfos.Add(aircraftId, new LDNetAircraftSupremeInfo(aircraftId, isGet));
            }
        }

        /// <summary>
        ///  同步机甲平台洗练信息
        /// </summary>
        /// <param name="level"></param>
        /// <param name="exp"></param>
        public void SyncAircraftRefinementInfo(int level, int exp)
        {
            RefinementLevel = level;
            RefinementExp = exp;
        }

        /// <summary>
        /// 机甲平台是否获得
        /// </summary>
        /// <param name="aircraftId"></param>
        /// <returns></returns>
        public bool IsAircraftGet(int aircraftId)
        {
            if (AircraftInfos.TryGetValue(aircraftId, out LDNetAircraftInfo aircraftInfo))
            {
                return aircraftInfo != null;
            }

            return false;
        }

        /// <summary>
        /// ss机甲平台是否获得
        /// </summary>
        /// <param name="aircraftId"></param>
        /// <returns></returns>
        public bool IsAircraftSupremeGet(int aircraftId)
        {
            if (AircraftSupremeInfos.TryGetValue(aircraftId, out LDNetAircraftSupremeInfo supremeInfo))
            {
                return supremeInfo.IsGet;
            }

            return false;
        }

        public LDNetAircraftInfo GetAircraftInfo(int aircraftId)
        {
            if (AircraftInfos.TryGetValue(aircraftId, out LDNetAircraftInfo aircraftInfo))
            {
                return aircraftInfo;
            }

            return null; // 未找到对应的机甲信息
        }
    }

    /// <summary>
    /// ss机甲平台获取信息
    /// </summary>
    public class LDNetAircraftSupremeInfo
    {
        public int AircraftId;
        public bool IsGet;

        public LDNetAircraftSupremeInfo(int aircraftId, bool isGet)
        {
            AircraftId = aircraftId;
            SyncAircraftSupremeInfo(isGet);
        }

        public void SyncAircraftSupremeInfo(bool isGet)
        {
            IsGet = isGet;
        }
    }


    /// <summary>
    /// 上阵信息
    /// </summary>
    public class LDNetAircraftSlotInfo
    {
        public Dictionary<int, int> SlotInfo = new Dictionary<int, int>();

        public void SyncSlotInfo(MapField<int, int> slotInfo)
        {
            SlotInfo.Clear();
            if (slotInfo != null)
            {
                foreach (var kv in slotInfo)
                {
                    SlotInfo[kv.Key] = kv.Value;
                }
            }
        }

        public int GetSlotIndexByAircraftId(int aircraftId)
        {
            foreach (var kv in SlotInfo)
            {
                if (kv.Value == aircraftId)
                {
                    return kv.Key; // 返回对应的上阵槽位索引
                }
            }

            return -1; // 未上阵
        }

        public int GetAircraftIdBySlotIndex(int slotIndex)
        {
            if (SlotInfo.TryGetValue(slotIndex, out int aircraftId))
            {
                return aircraftId;
            }

            return -1; // 未上阵
        }

        /// <summary>
        /// 是否上阵
        /// </summary>
        /// <param name="aircraftId"></param>
        /// <returns></returns>
        public bool IsAircraftInUse(int aircraftId)
        {
            return SlotInfo.ContainsValue(aircraftId);
        }

        public AircraftSlotInfo Convert()
        {
            AircraftSlotInfo slotInfo = new AircraftSlotInfo();
            foreach (var kv in SlotInfo)
            {
                slotInfo.SlotInfo[kv.Key] = kv.Value;
            }

            return slotInfo;
        }
    }


    /// <summary>
    /// 机甲平台信息
    /// </summary>
    public class LDNetAircraftInfo
    {
        public int AircraftId;
        public int Quality;
        public int Level;
        public int Luck;
        public int SkinId;
        public LdNetAircraftRefinementInfo AircraftRefinementInfo;

        public LDNetAircraftInfo(AircraftInfo info)
        {
            SyncAircraftInfo(info);
        }

        public void SyncAircraftInfo(AircraftInfo info)
        {
            AircraftId = info.AircraftId;
            Quality = info.Quality;
            Level = info.Level;
            Luck = info.Luck;
            SkinId = info.SkinId;
            if (AircraftRefinementInfo == null)
            {
                AircraftRefinementInfo = new LdNetAircraftRefinementInfo(info.AircraftRefinementInfo);
            }
            else
            {
                AircraftRefinementInfo.SyncEffect(info.AircraftRefinementInfo.Effects.ToList());
                AircraftRefinementInfo.SyncRefinementEffect(info.AircraftRefinementInfo.RefinementEffects.ToList());
            }
        }
    }

    /// <summary>
    /// 平台洗练信息
    /// </summary>
    public class LdNetAircraftRefinementInfo
    {
        public List<LDNetAircraftEffect> Effects;
        public List<LDNetAircraftEffect> RefinementEffects;


        public LdNetAircraftRefinementInfo(AircraftRefinementInfo info)
        {
            Effects = new List<LDNetAircraftEffect>();
            if (info.Effects != null)
            {
                foreach (var effect in info.Effects)
                {
                    Effects.Add(new LDNetAircraftEffect(effect.EffectId, effect.EffectValue, false));
                }
            }

            RefinementEffects = new List<LDNetAircraftEffect>();
            if (info.RefinementEffects != null)
            {
                foreach (var effect in info.RefinementEffects)
                {
                    RefinementEffects.Add(new LDNetAircraftEffect(effect.EffectId, effect.EffectValue, effect.IsLock));
                }
            }
        }

        public void SyncEffect(List<AircraftEffect> effects)
        {
            Effects.Clear();
            if (effects != null)
            {
                foreach (var effect in effects)
                {
                    Effects.Add(new LDNetAircraftEffect(effect.EffectId, effect.EffectValue, false));
                }
            }
        }

        /// <summary>
        /// 同步洗练数据
        /// </summary>
        /// <param name="effects"></param>
        public void SyncRefinementEffect(List<AircraftEffect> effects)
        {
            RefinementEffects.Clear();
            if (effects != null)
            {
                foreach (var effect in effects)
                {
                    RefinementEffects.Add(new LDNetAircraftEffect(effect.EffectId, effect.EffectValue, effect.IsLock));
                }
            }
        }

        public double GetDefaultPower(List<LDNetAircraftEffect> otherAircraftEffects)
        {
            if (Effects.Count <= 0)
            {
                return 0;
            }

            // 计算默认战斗力
            return CheckPower(Effects, Effects, otherAircraftEffects);
        }

        public double GetRefinementPower(List<LDNetAircraftEffect> otherAircraftEffects)
        {
            if (RefinementEffects.Count <= 0)
            {
                return 0;
            }

            // 计算洗练战斗力
            return CheckPower(Effects, RefinementEffects, otherAircraftEffects);
        }

        private double CheckPower(List<LDNetAircraftEffect> equipEffects, List<LDNetAircraftEffect> calculateEffects, List<LDNetAircraftEffect> otherAircraftEffects)
        {
            Dictionary<int, LDAttributeItem> playerAttr = Global.gApp.gSystemMgr.gRoleMgr.GetAllAttrData();
            Dictionary<int, LDAttributeItem> playerNewAttr = new();
            foreach (KeyValuePair<int, LDAttributeItem> valuePair in playerAttr)
            {
                var playerAttrItem = valuePair.Value;
                var playerNewAttrItem = playerAttrItem.CopyItem();
                playerNewAttr.Add(valuePair.Key, playerNewAttrItem);
            }


            //装备着  先给他卸载掉

            List<LDEquipAttrAddition> equipEffectAtttrSingle = GetAttrEffectAttrAdditions2(equipEffects);
            var effectPowerSingle = UiTools.CalculateEffectFightPower(playerNewAttr, equipEffectAtttrSingle);

            //另外一个平台的效果 额外计算的战力
            List<LDEquipAttrAddition> otherSingle1 = GetAttrEffectAttrAdditions2(otherAircraftEffects);

            var otherPowerSingle1 = UiTools.CalculateEffectFightPower(playerNewAttr, otherSingle1);

            List<LDEquipAttrAddition> equipEffectAtttr = GetAttrEffectAttrAdditions(equipEffects);
            foreach (LDEquipAttrAddition attrAddition in equipEffectAtttr)
            {
                if (playerNewAttr.TryGetValue(attrAddition.Id, out var newAttrItem))
                {
                    newAttrItem.AddValue(attrAddition.GetBaseVal() * -1, attrAddition.GetBasePercentVal() * -1, attrAddition.GetExtraVal() * -1, attrAddition.GetExtraPercentVal() * -1);
                }
            }

            //计算人不穿装备的属性
            var playerAttrPower = UiTools.CalculateFightPower(playerNewAttr);
            // playerAttrPower -= effectPowerSingle;
            // Global.Log($"脱了装备后的战力：{playerAttrPower}");

            //给人传上新的装备
            List<LDEquipAttrAddition> newEquipEffectAtttr = this.GetAttrEffectAttrAdditions(calculateEffects);
            foreach (LDEquipAttrAddition attrAddition in newEquipEffectAtttr)
            {
                if (playerNewAttr.TryGetValue(attrAddition.Id, out var newAttrItem))
                {
                    newAttrItem.AddValue(attrAddition.GetBaseVal(), attrAddition.GetBasePercentVal(), attrAddition.GetExtraVal(), attrAddition.GetExtraPercentVal());
                }
                else
                {
                    playerNewAttr.Add(attrAddition.Id, new LDAttributeItem(attrAddition));
                }
            }

            //计算传上新装备属性的战斗力
            var playerAttrPowerAfter = UiTools.CalculateFightPower(playerNewAttr);
            List<LDEquipAttrAddition> equipEffectAtttrSingleNew = GetAttrEffectAttrAdditions2(calculateEffects);
            var effectPowerSingleNew = UiTools.CalculateEffectFightPower(playerNewAttr, equipEffectAtttrSingleNew);
            playerAttrPowerAfter += effectPowerSingleNew;


            //另外一个平台新属性下的战力
            var otherPowerSingle2 = UiTools.CalculateEffectFightPower(playerNewAttr, otherSingle1);

            //加上装备效果的战斗力
            foreach (LDNetAttrEffect effect in calculateEffects)
            {
                if (!string.IsNullOrEmpty(effect.EffectCfg.effectNum) && effect.EffectCfg.correspondingGameplay != 99 && effect.EffectCfg.powerCoe <= 0f)
                {
                    var per = Math.Max(0f, Math.Min(effect.EffectValue / effect.MaxVal, 1f));
                    playerAttrPowerAfter += effect.EffectCfg.score * per;
                }

                if (!string.IsNullOrEmpty(effect.EffectCfg.citiaoEffect))
                {
                    var per = Math.Max(0f, Math.Min(effect.EffectValue / effect.MaxVal, 1f));
                    playerAttrPowerAfter += effect.EffectCfg.score * per;
                }
            }

            // Global.Log($"穿上后的战力：{playerAttrPowerAfter}");

            //另外一个平台战力变化
            var otherPowerChange = otherPowerSingle2 - otherPowerSingle1;
            var power = playerAttrPowerAfter - playerAttrPower + otherPowerChange;
            // Global.Log($":  战力：{power}");
            return power;
        }

        public List<LDEquipAttrAddition> GetAttrEffectAttrAdditions(List<LDNetAircraftEffect> cEffects)
        {
            List<LDEquipAttrAddition> attr = new();

            foreach (LDNetAircraftEffect effect in cEffects)
            {
                if (effect.EffectCfg.correspondingGameplay == 99)
                {
                    var effectAttr = effect.GetEffectAttrAddition();
                    if (effectAttr != null)
                    {
                        attr.Add(effectAttr);
                    }
                }
            }

            return attr;
        }

        public List<LDEquipAttrAddition> GetAttrEffectAttrAdditions2(List<LDNetAircraftEffect> cEffects)
        {
            List<LDEquipAttrAddition> attr = new();

            foreach (LDNetAircraftEffect effect in cEffects)
            {
                if (effect.EffectCfg.correspondingGameplay != 99 && effect.EffectCfg.powerCoe > 0f)
                {
                    var effectAttr = effect.GetEffectAttrAddition();
                    if (effectAttr != null)
                    {
                        effectAttr.m_PowerCalculatePer = effect.EffectCfg.powerCoe;
                        attr.Add(effectAttr);
                    }
                }
            }

            return attr;
        }
    }

    /// <summary>
    /// 效果数据
    /// </summary>
    public class LDNetAircraftEffect : LDNetAttrEffect
    {
        public int EffectId;
        public double EffectValue;
        public bool IsLock = false; // 是否锁定

        public AttrEffectItem EffectCfg;

        public int AttrType = -1;

        public LDNetAircraftEffect(int effectId, double effectValue, bool isLock) : base(effectId, effectValue)
        {
            EffectId = effectId;
            EffectValue = effectValue;
            IsLock = isLock;

            this.EffectCfg = AttrEffect.Data.Get(effectId);
            if (!string.IsNullOrEmpty(this.EffectCfg.effectNum))
            {
                LDAttrAddition attr = new LDAttrAddition(this.EffectCfg.effectNum);

                AttrType = attr.AttrType;
            }
        }

        public string GetDesc()
        {
            return UiTools.Localize(this.EffectCfg.effectName, GetValueStr());
        }

        public string GetValueStr()
        {
            if (EffectCfg.displayNumericalType == 1)
            {
                if (EffectValue > 10000)
                {
                    return UiTools.FormateMoney(EffectValue, true);
                }

                return EffectValue.ToString();
            }
            else if (EffectCfg.displayNumericalType == 2)
            {
                return UiTools.FormateMoney(EffectValue * 100f, true);
            }
            else if (EffectCfg.displayNumericalType == 3)
            {
                return (EffectValue / 100f).ToString();
            }

            if (EffectValue > 10000)
            {
                return UiTools.FormateMoney(EffectValue, true);
            }
            else
            {
                if (EffectValue.ToString().Contains("."))
                {
                    return UiTools.RemoveRemainder(EffectValue.ToString(), 2);
                }
                else
                {
                    return EffectValue.ToString();
                }
            }
        }
    }
}