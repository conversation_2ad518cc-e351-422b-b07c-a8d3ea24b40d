using System;
using System.Collections.Generic;
using System.Linq;
using Google.Protobuf.Collections;
using LD.Protocol;
using UnityEngine;

namespace LD
{
    public enum LDEquipmentType
    {
        Equipment = 1,
        Weapon = 2,
    }

    public class LDLabaMachineReward
    {
        public int id;
        public LDCommonItem reward;
    }

    public class LDNetEquipmentDTO
    {
        public Dictionary<long, LDNetEquipInfo> EquipInfos = new();
        public LDNetEquipSlotInfo EquipSlotInfos = new();
        public int Exp;
        public int FurnaceLevel;
        public bool FurnaceLvUp = false;

        public Queue<LDNetEquipInfo> EquipCreateQueue = new();

        public void SyncEquipInfos(EquipInfo equipInfo)
        {
            if (EquipInfos.TryGetValue(equipInfo.Id, out var equip))
            {
                equip.SyncNetEquipInfo(equipInfo);
            }
            else
            {
                var newEquip = new LDNetEquipInfo(equipInfo);
                EquipInfos.Add(equipInfo.Id, newEquip);
                if (Global.gApp.gSystemMgr.gEquipmentMgr.PlayerAssembleFinishResponse)
                {
                    AddNewEquip(newEquip);
                }
            }
        }

        public void SyncEquipSlotInfos(EquipSlotInfo equipSlotInfo)
        {
            foreach (KeyValuePair<int, long> pair in equipSlotInfo.SlotInfo)
            {
                EquipSlotInfos.SyncEquipSlotInfo(pair.Key, pair.Value);
            }
        }

        public List<LDNetEquipInfo> GetEquipInfos()
        {
            List<long> equip = new();
            foreach (KeyValuePair<int, long> pair in EquipSlotInfos.EquipSlotInfos)
            {
                equip.Add(pair.Value);
            }

            var allEquips = new List<LDNetEquipInfo>();
            foreach (KeyValuePair<long, LDNetEquipInfo> equipInfo in EquipInfos)
            {
                if (!equip.Contains(equipInfo.Value.Id))
                {
                    allEquips.Add(equipInfo.Value);
                }
            }

            return allEquips;
        }

        public LDNetEquipInfo GetEquipInfo(long uid)
        {
            if (EquipInfos.TryGetValue(uid, out var equip))
            {
                return equip;
            }

            return null;
        }

        /// <summary>
        /// 分解装备
        /// </summary>
        /// <param name="uid"></param>
        public void BreakDownEquip(long uid)
        {
            EquipInfos.Remove(uid, out var equip);
        }

        public void AddNewEquip(LDNetEquipInfo equip)
        {
            if (equip.EquipCfg.type == LDEquipmentType.Weapon.GetHashCode())
            {
                return;
            }

            EquipCreateQueue.Enqueue(equip);
            Global.gApp.gUiMgr.TryShowNewEquip();
        }

        public void RemoveNewEquip(List<long> uids)
        {
            List<LDNetEquipInfo> allList = new();
            while (EquipCreateQueue.Count > 0)
            {
                allList.Add(EquipCreateQueue.Dequeue());
            }

            foreach (var equip in allList)
            {
                if (uids.Contains(equip.Id))
                {
                    continue;
                }

                EquipCreateQueue.Enqueue(equip);
            }
        }

        public LDNetEquipInfo GetNewEquip()
        {
            if (EquipCreateQueue.Count > 0)
            {
                return EquipCreateQueue.Dequeue();
            }

            return null;
        }
    }

    public class LDNetAttrEffect
    {
        public int GroupId;
        public int CiTiaoId;

        //==========
        public int EffectId;
        public double EffectValue;
        public AttrEffectItem EffectCfg;

        public double MinVal { get; private set; }
        public double MaxVal { get; private set; }

        public int AttrType = -1;

        public LDNetAttrEffect(int effectId)
        {
            this.EffectId = effectId;
            this.EffectCfg = AttrEffect.Data.Get(effectId);

            if (!string.IsNullOrEmpty(this.EffectCfg.effectNum))
            {
                LDAttrAddition attr = new LDAttrAddition(this.EffectCfg.effectNum);
                AttrType = attr.AttrType;
                //应该是固定值啊，如果不是就给他最小值

                this.EffectValue = (this.EffectCfg.lowerLimit / 10000f) * attr.Val;
            }

            if (!string.IsNullOrEmpty(this.EffectCfg.citiaoEffect))
            {
                var citiaoLines = LDCommonTools.Split(this.EffectCfg.citiaoEffect, ",");
                if (citiaoLines.Length < 2) return;
                var citiaoId = LDParseTools.IntParse(citiaoLines[1]);
                var citiaoCfg = Citiao.Data.Get(citiaoId);
                if (citiaoCfg == null) return;
                if (citiaoCfg.handleType.Length < 2)
                    return;
                var buffId = (int)citiaoCfg.handleType[1];
                var buffCfg = Buff.Data.Get(buffId);
                if (buffCfg == null) return;
                //应该是固定值啊，如果不是就给他最小值
                this.EffectValue = (this.EffectCfg.lowerLimit / 10000f) * buffCfg.buffParam[0];
            }
        }

        public LDNetAttrEffect(int effectId, double effectValue)
        {
            this.EffectId = effectId;
            this.EffectValue = effectValue;
            this.EffectCfg = AttrEffect.Data.Get(effectId);
            CheckRandomValue();
        }

        private void CheckRandomValue()
        {
            if (!string.IsNullOrEmpty(this.EffectCfg.effectNum))
            {
                LDAttrAddition attr = new LDAttrAddition(this.EffectCfg.effectNum);
                //应该是固定值啊，如果不是就给他最小值
                this.MaxVal = attr.Val;
                this.MinVal = (this.EffectCfg.lowerLimit / 10000f) * attr.Val;
            }

            if (!string.IsNullOrEmpty(this.EffectCfg.citiaoEffect))
            {
                var citiaoLines = LDCommonTools.Split(this.EffectCfg.citiaoEffect, ",");
                if (citiaoLines.Length < 2) return;
                var citiaoId = LDParseTools.IntParse(citiaoLines[1]);
                var citiaoCfg = Citiao.Data.Get(citiaoId);
                if (citiaoCfg == null) return;
                if (citiaoCfg.handleType.Length < 2)
                    return;
                var buffId = (int)citiaoCfg.handleType[1];
                var buffCfg = Buff.Data.Get(buffId);
                if (buffCfg == null) return;
                //应该是固定值啊，如果不是就给他最小值
                double scale = 1;
                if (buffCfg.buffParam.Length > 1)
                {
                    scale = buffCfg.buffParam[1];
                }

                this.MinVal = (this.EffectCfg.lowerLimit / 10000f) * buffCfg.buffParam[0] * scale;
                this.MaxVal = buffCfg.buffParam[0] * scale;
                this.EffectValue *= scale;
            }
        }

        public string GetDesc()
        {
            var str = AttrType == 2 ? this.EffectValue / 100 : this.EffectValue;
            return UiTools.Localize(this.EffectCfg.effectName, AttrType == 2 ? $"{str}%" : str);
        }

        public string GetDesc(bool isMore)
        {
            if (isMore)
            {
                var moreAttr = "";
                if (EffectCfg.lowerLimit == 10000)
                {
                    moreAttr = UiTools.Localize(65540, GetMaxStr());
                }
                else
                {
                    moreAttr = UiTools.Localize(65538, GetMinStr(), GetMaxStr());
                }

                var tips = UiTools.Localize(EffectCfg.effectName, GetValueStr()) + moreAttr;
                return tips;
            }
            else
            {
                return UiTools.Localize(EffectCfg.effectName, GetValueStr());
            }
        }

        public string GetRandomDesc()
        {
            var moreAttr = "";
            if (EffectCfg.lowerLimit == 10000)
            {
                moreAttr = GetMaxStr();
            }
            else
            {
                moreAttr = UiTools.Localize(65538, GetMinStr(), GetMaxStr());
            }

            var tips = UiTools.Localize(EffectCfg.effectName, moreAttr);
            return tips;
        }

        public int GetEffectPerfectIndex()
        {
            if (EffectCfg.perfectionDegree.Length > 0)
            {
                for (int i = 0; i < EffectCfg.perfectionDegree.Length; i++)
                {
                    var index = EffectCfg.perfectionDegree[i];
                    var percent = EffectCfg.perfectionDegree[i + 1] / 10000f;
                    var value = MaxVal * percent;

                    if (EffectValue <= value)
                        return (int)index;
                    i++;
                }
            }

            return 5;
        }

        public Color GetEffectPerfectColor()
        {
            int index = GetEffectPerfectIndex();
            return DYColorUtil.GetColor(EquipmentUITools.GetEquipCitiaoColor(index));
        }

        public string GetEffectPerfectImgPath()
        {
            int index = GetEffectPerfectIndex();
            ItemQualityItem qualityItem = ItemQuality.Data.Get(index);
            if (qualityItem != null)
            {
                return qualityItem.equipmentCiTiaoBG;
            }

            return "";
        }

        public LDEquipAttrAddition GetEffectAttrAddition()
        {
            if (!string.IsNullOrEmpty(EffectCfg.effectNum))
            {
                var spr = LDCommonTools.Split(EffectCfg.effectNum);
                var a = new LDEquipAttrAddition($"{spr[0]}_{spr[1]}_{EffectValue}");
                return a;
            }

            return null;
        }

        public string GetValueStr()
        {
            if (EffectCfg.displayNumericalType == 1)
            {
                if (EffectValue > 10000)
                {
                    return UiTools.FormateMoney(EffectValue, true);
                }

                return EffectValue.ToString();
            }
            else if (EffectCfg.displayNumericalType == 2)
            {
                return UiTools.FormateMoney(EffectValue * 100f, true);
            }
            else if (EffectCfg.displayNumericalType == 3)
            {
                return (EffectValue / 100f).ToString();
            }

            if (EffectValue > 10000)
            {
                return UiTools.FormateMoney(EffectValue, true);
            }
            else
            {
                if (EffectValue.ToString().Contains("."))
                {
                    return UiTools.RemoveRemainder(EffectValue.ToString(), 2);
                }
                else
                {
                    return EffectValue.ToString();
                }
            }
        }

        public string GetMinStr()
        {
            if (EffectCfg.displayNumericalType == 1)
            {
                if (MinVal > 10000)
                {
                    return UiTools.FormateMoney(MinVal, true);
                }
                else
                {
                    return MinVal.ToString();
                }
            }
            else if (EffectCfg.displayNumericalType == 2)
            {
                return UiTools.FormateMoney(MinVal * 100f, true);
            }
            else if (EffectCfg.displayNumericalType == 3)
            {
                return (MinVal / 100f).ToString();
            }

            if (EffectValue > 10000)
            {
                return UiTools.FormateMoney(MinVal, true);
            }
            else
            {
                if (MinVal.ToString().Contains("."))
                {
                    return UiTools.RemoveRemainder(MinVal.ToString(), 2);
                }
                else
                {
                    return MinVal.ToString();
                }
            }
        }

        public string GetMaxStr()
        {
            if (EffectCfg.displayNumericalType == 1)
            {
                if (MaxVal > 10000)
                {
                    return UiTools.FormateMoney(MaxVal, true);
                }

                return MaxVal.ToString();
            }
            else if (EffectCfg.displayNumericalType == 2)
            {
                return UiTools.FormateMoney(MaxVal * 100f, true);
            }
            else if (EffectCfg.displayNumericalType == 3)
            {
                return (MaxVal / 100f).ToString();
            }

            if (EffectValue > 10000)
            {
                return UiTools.FormateMoney(MaxVal, true);
            }
            else
            {
                if (MaxVal.ToString().Contains("."))
                {
                    return UiTools.RemoveRemainder(MaxVal.ToString(), 2);
                }
                else
                {
                    return MaxVal.ToString();
                }
            }
        }

        public EquipEffect Convert()
        {
            EquipEffect effect = new EquipEffect();
            effect.EffectId = EffectId;
            effect.EffectValue = EffectValue;
            return effect;
        }
    }

    public class LDNetEquipInfo
    {
        public long Id;
        public int EquipId;
        public int Quality;
        public long FightPower;
        public List<LDEquipAttrAddition> AttrAddition;
        public List<LDNetAttrEffect> EquipEffects;
        public EquipmentItem EquipCfg;
        public List<string> EquipProps = new();
        public List<LDNetAttrEffect> RefinementEffects;


        public LDNetEquipInfo()
        {
        }

        public LDNetEquipInfo(EquipInfo equip)
        {
            SyncNetEquipInfo(equip);
        }

        public void SyncNetEquipInfo(EquipInfo equip)
        {
            Id = equip.Id;
            EquipId = equip.EquipId;
            Quality = equip.Quality;
            FightPower = equip.FightPower;
            EquipCfg = Equipment.Data.Get(EquipId);
            AttrAddition = new List<LDEquipAttrAddition>();
            var attrRangeDict = Global.gApp.gSystemMgr.gEquipmentMgr.GetEquipQualityRange(Quality, Global.gApp.gSystemMgr.gEquipmentMgr.GetEquipmentTypeBySlotId(EquipCfg.slot).GetHashCode());
            foreach (var attr in equip.Props)
            {
                EquipProps.Add(attr);
                var attrObj = new LDEquipAttrAddition(attr);
                if (attrRangeDict.TryGetValue(attrObj.Id, out var range))
                {
                    attrObj.SetAttrValueRand(range.MinValue, range.MaxValue);
                }

                AttrAddition.Add(attrObj);
            }

            EquipEffects = new();
            if (equip.EquipEffect != null)
            {
                foreach (var effect in equip.EquipEffect)
                {
                    var effectInfo = new LDNetAttrEffect(effect.EffectId, effect.EffectValue);
                    EquipEffects.Add(effectInfo);
                }
            }

            RefinementEffects = new();
            if (equip.RefinementEffect != null)
            {
                foreach (var effect in equip.RefinementEffect)
                {
                    var effectInfo = new LDNetAttrEffect(effect.EffectId, effect.EffectValue);
                    RefinementEffects.Add(effectInfo);
                }
            }
        }

        public string GetEquipPropStr()
        {
            var a = "";
            foreach (string s in EquipProps)
            {
                a += s + " :  ";
            }

            return a;
        }

        public LDCommonItem GetEquipCommonItem()
        {
            var item = new LDCommonItem(LDCommonType.Equipment, EquipId, 0);
            item.SetNewQuality(Quality);
            return item;
        }

        public int GetEquipSlotId()
        {
            return EquipCfg.slot;
        }

        public double GetEquipPower()
        {
            double power = CheckPower();
            return Math.Floor(power);
        }

        public double GetEquipNewEffectPower()
        {
            double power = CheckNewEffectPower();
            return Math.Floor(power);
        }

        public List<LDEquipAttrAddition> GetAttrEffectAttrAdditions()
        {
            List<LDEquipAttrAddition> attr = new();

            foreach (LDNetAttrEffect effect in EquipEffects)
            {
                if (effect.EffectCfg.correspondingGameplay == 99)
                {
                    LDEquipAttrAddition effectAttr = effect.GetEffectAttrAddition();
                    if (effectAttr != null)
                    {
                        effectAttr.m_PowerCalculatePer = effect.EffectCfg.powerCoe;
                        attr.Add(effectAttr);
                    }
                }
            }

            return attr;
        }

        public List<LDEquipAttrAddition> GetAttrEffectAttrAdditions2()
        {
            List<LDEquipAttrAddition> attr = new();

            foreach (LDNetAttrEffect effect in EquipEffects)
            {
                if (effect.EffectCfg.correspondingGameplay != 99 && effect.EffectCfg.powerCoe > 0f)
                {
                    LDEquipAttrAddition effectAttr = effect.GetEffectAttrAddition();
                    if (effectAttr != null)
                    {
                        effectAttr.m_PowerCalculatePer = effect.EffectCfg.powerCoe;
                        attr.Add(effectAttr);
                    }
                }
            }

            return attr;
        }

        public List<LDEquipAttrAddition> GetAttrEffectAttrAdditions2(List<LDNetAircraftEffect> cEffects)
        {
            List<LDEquipAttrAddition> attr = new();

            foreach (LDNetAttrEffect effect in cEffects)
            {
                if (effect.EffectCfg.correspondingGameplay != 99 && effect.EffectCfg.powerCoe > 0f)
                {
                    LDEquipAttrAddition effectAttr = effect.GetEffectAttrAddition();
                    if (effectAttr != null)
                    {
                        effectAttr.m_PowerCalculatePer = effect.EffectCfg.powerCoe;
                        attr.Add(effectAttr);
                    }
                }
            }

            return attr;
        }

        private double CheckPower()
        {
            Dictionary<int, LDAttributeItem> playerAttr = Global.gApp.gSystemMgr.gRoleMgr.GetAllAttrData();
            Dictionary<int, LDAttributeItem> playerNewAttr = new();
            foreach (KeyValuePair<int, LDAttributeItem> valuePair in playerAttr)
            {
                var playerAttrItem = valuePair.Value;
                var playerNewAttrItem = playerAttrItem.CopyItem();
                playerNewAttr.Add(valuePair.Key, playerNewAttrItem);
            }

            // foreach (KeyValuePair<int, LDAttributeItem> valuePair in playerNewAttr)
            // {
            //     Global.LogError($"属性11  id:{valuePair.Value.Type}   value:{valuePair.Value.GetValue()}");
            // }

            //另外一个平台的效果 额外计算的战力
            List<LDNetAircraftEffect> otherAircraftEffects = Global.gApp.gSystemMgr.gAircraftMgr.GetAllAircraftPowerEffects();
            List<LDEquipAttrAddition> otherSingle1 = GetAttrEffectAttrAdditions2(otherAircraftEffects);
            double otherPowerSingle1 = UiTools.CalculateEffectFightPower(playerNewAttr, otherSingle1);

            var slotId = EquipCfg.slot;
            var isEquiping = Global.gApp.gSystemMgr.gEquipmentMgr.GetEquipBySlotId(slotId);
            var singleEffectPower = 0d;
            if (isEquiping != null)
            {
                //计算单独计算战力的效果
                List<LDEquipAttrAddition> equipEffectAtttr2 = isEquiping.GetAttrEffectAttrAdditions2();
                singleEffectPower = UiTools.CalculateEffectFightPower(playerNewAttr, equipEffectAtttr2);


                //装备着  先给他卸载掉
                List<LDEquipAttrAddition> equipingAttr = isEquiping.AttrAddition;
                foreach (LDEquipAttrAddition attrAddition in equipingAttr)
                {
                    if (playerNewAttr.TryGetValue(attrAddition.Id, out var newAttrItem))
                    {
                        newAttrItem.AddValue(attrAddition.GetBaseVal() * -1, attrAddition.GetBasePercentVal() * -1, attrAddition.GetExtraVal() * -1, attrAddition.GetExtraPercentVal() * -1);
                    }
                }

                List<LDEquipAttrAddition> equipEffectAtttr = isEquiping.GetAttrEffectAttrAdditions();
                foreach (LDEquipAttrAddition attrAddition in equipEffectAtttr)
                {
                    if (playerNewAttr.TryGetValue(attrAddition.Id, out var newAttrItem))
                    {
                        newAttrItem.AddValue(attrAddition.GetBaseVal() * -1, attrAddition.GetBasePercentVal() * -1, attrAddition.GetExtraVal() * -1, attrAddition.GetExtraPercentVal() * -1);
                    }
                }
            }

            //计算人不穿装备的属性
            var playerAttrPower = UiTools.CalculateFightPower(playerNewAttr);
            // playerAttrPower -= singleEffectPower;
            Global.Log($"脱了装备后的战力：{playerAttrPower}");
            // foreach (KeyValuePair<int, LDAttributeItem> valuePair in playerNewAttr)
            // {
            //     Global.LogError($"属性22 id:{valuePair.Value.Type}   value:{valuePair.Value.GetValue()}");
            // }

            //给人传上新的装备
            var newEquipAttr = this.AttrAddition;
            foreach (LDEquipAttrAddition attrAddition in newEquipAttr)
            {
                if (playerNewAttr.TryGetValue(attrAddition.Id, out var newAttrItem))
                {
                    newAttrItem.AddValue(attrAddition.GetBaseVal(), attrAddition.GetBasePercentVal(), attrAddition.GetExtraVal(), attrAddition.GetExtraPercentVal());
                }
                else
                {
                    playerNewAttr.Add(attrAddition.Id, new LDAttributeItem(attrAddition));
                }
            }

            List<LDEquipAttrAddition> newEquipEffectAtttr = this.GetAttrEffectAttrAdditions();
            foreach (LDEquipAttrAddition attrAddition in newEquipEffectAtttr)
            {
                if (playerNewAttr.TryGetValue(attrAddition.Id, out var newAttrItem))
                {
                    newAttrItem.AddValue(attrAddition.GetBaseVal(), attrAddition.GetBasePercentVal(), attrAddition.GetExtraVal(), attrAddition.GetExtraPercentVal());
                }
                else
                {
                    playerNewAttr.Add(attrAddition.Id, new LDAttributeItem(attrAddition));
                }
            }

            //计算传上新装备属性的战斗力
            var playerAttrPowerAfter = UiTools.CalculateFightPower(playerNewAttr);
            //计算单独计算战力的效果
            List<LDEquipAttrAddition> equipEffectAtttrNew = this.GetAttrEffectAttrAdditions2();
            var singleEffectPowerNew = UiTools.CalculateEffectFightPower(playerNewAttr, equipEffectAtttrNew);
            playerAttrPowerAfter += singleEffectPowerNew;

            // foreach (KeyValuePair<int, LDAttributeItem> valuePair in playerNewAttr)
            // {
            //     Global.LogError($"属性33 id:{valuePair.Value.Type}   value:{valuePair.Value.GetValue()}");
            // }

            //另外一个平台新属性下的战力
            var otherPowerSingle2 = UiTools.CalculateEffectFightPower(playerNewAttr, otherSingle1);


            // Global.Log($"effect加前的战力：{playerAttrPowerAfter}");
            //加上装备效果的战斗力
            foreach (LDNetAttrEffect effect in EquipEffects)
            {
                if (!string.IsNullOrEmpty(effect.EffectCfg.effectNum) && effect.EffectCfg.correspondingGameplay != 99 && effect.EffectCfg.powerCoe <= 0f)
                {
                    var per = Math.Max(0f, Math.Min(effect.EffectValue / effect.MaxVal, 1f));
                    playerAttrPowerAfter += effect.EffectCfg.score * per;
                }

                if (!string.IsNullOrEmpty(effect.EffectCfg.citiaoEffect))
                {
                    var per = Math.Max(0f, Math.Min(effect.EffectValue / effect.MaxVal, 1f));
                    playerAttrPowerAfter += effect.EffectCfg.score * per;
                }
            }

            Global.Log($"穿上:{Id}  后的战力：{playerAttrPowerAfter}");

            var otherPowerChange = otherPowerSingle2 - otherPowerSingle1;
            var power = playerAttrPowerAfter - playerAttrPower + otherPowerChange;
            Global.Log($":{Id}  装备的战力：{power}");
            return power;
        }

        private double CheckNewEffectPower()
        {
            Dictionary<int, LDAttributeItem> playerAttr = Global.gApp.gSystemMgr.gRoleMgr.GetAllAttrData();
            Dictionary<int, LDAttributeItem> playerNewAttr = new();
            foreach (KeyValuePair<int, LDAttributeItem> valuePair in playerAttr)
            {
                var playerAttrItem = valuePair.Value;
                var playerNewAttrItem = playerAttrItem.CopyItem();
                playerNewAttr.Add(valuePair.Key, playerNewAttrItem);
            }

            var slotId = EquipCfg.slot;
            var isEquiping = Global.gApp.gSystemMgr.gEquipmentMgr.GetEquipBySlotId(slotId);
            if (isEquiping != null)
            {
                //装备着  先给他卸载掉
                var equipingAttr = isEquiping.AttrAddition;
                foreach (LDEquipAttrAddition attrAddition in equipingAttr)
                {
                    if (playerNewAttr.TryGetValue(attrAddition.Id, out var newAttrItem))
                    {
                        newAttrItem.AddValue(attrAddition.GetBaseVal() * -1, attrAddition.GetBasePercentVal() * -1, attrAddition.GetExtraVal() * -1, attrAddition.GetExtraPercentVal() * -1);
                    }
                }

                var equipEffectAtttr = isEquiping.GetAttrEffectAttrAdditions();
                foreach (LDEquipAttrAddition attrAddition in equipEffectAtttr)
                {
                    if (playerNewAttr.TryGetValue(attrAddition.Id, out var newAttrItem))
                    {
                        newAttrItem.AddValue(attrAddition.GetBaseVal() * -1, attrAddition.GetBasePercentVal() * -1, attrAddition.GetExtraVal() * -1, attrAddition.GetExtraPercentVal() * -1);
                    }
                }
            }

            //计算人不穿装备的属性
            var playerAttrPower = UiTools.CalculateFightPower(playerNewAttr);
            Global.Log($"脱了装备后的战力：{playerAttrPower}");
            // foreach (KeyValuePair<int, LDAttributeItem> valuePair in playerNewAttr)
            // {
            //     Global.LogError($"属性22 id:{valuePair.Value.Type}   value:{valuePair.Value.GetValue()}");
            // }

            //给人传上新的装备
            var newEquipAttr = this.AttrAddition;
            foreach (LDEquipAttrAddition attrAddition in newEquipAttr)
            {
                if (playerNewAttr.TryGetValue(attrAddition.Id, out var newAttrItem))
                {
                    newAttrItem.AddValue(attrAddition.GetBaseVal(), attrAddition.GetBasePercentVal(), attrAddition.GetExtraVal(), attrAddition.GetExtraPercentVal());
                }
                else
                {
                    playerNewAttr.Add(attrAddition.Id, new LDAttributeItem(attrAddition));
                }
            }

            var newEquipEffectAtttr = this.GetAttrEffectAttrAdditions();
            foreach (LDEquipAttrAddition attrAddition in newEquipEffectAtttr)
            {
                if (playerNewAttr.TryGetValue(attrAddition.Id, out var newAttrItem))
                {
                    newAttrItem.AddValue(attrAddition.GetBaseVal(), attrAddition.GetBasePercentVal(), attrAddition.GetExtraVal(), attrAddition.GetExtraPercentVal());
                }
                else
                {
                    playerNewAttr.Add(attrAddition.Id, new LDAttributeItem(attrAddition));
                }
            }

            //计算传上新装备属性的战斗力
            var playerAttrPowerAfter = UiTools.CalculateFightPower(playerNewAttr);
            // foreach (KeyValuePair<int, LDAttributeItem> valuePair in playerNewAttr)
            // {
            //     Global.LogError($"属性33 id:{valuePair.Value.Type}   value:{valuePair.Value.GetValue()}");
            // }


            // Global.Log($"effect加前的战力：{playerAttrPowerAfter}");
            //加上装备效果的战斗力
            foreach (LDNetAttrEffect effect in RefinementEffects)
            {
                if (!string.IsNullOrEmpty(effect.EffectCfg.effectNum) && effect.EffectCfg.correspondingGameplay != 99)
                {
                    var per = Math.Max(0f, Math.Min(effect.EffectValue / effect.MaxVal, 1f));
                    playerAttrPowerAfter += effect.EffectCfg.score * per;
                }

                if (!string.IsNullOrEmpty(effect.EffectCfg.citiaoEffect))
                {
                    var per = Math.Max(0f, Math.Min(effect.EffectValue / effect.MaxVal, 1f));
                    playerAttrPowerAfter += effect.EffectCfg.score * per;
                }
            }

            Global.Log($"穿上:{Id}  后的战力：{playerAttrPowerAfter}");

            var power = playerAttrPowerAfter - playerAttrPower;
            Global.Log($":{Id}  装备的战力：{power}");
            return power;
        }


        public ItemQualityItem GetEquipQualityItem()
        {
            return ItemQuality.Data.Get(Quality);
        }

        public EquipInfo Convert()
        {
            var info = new EquipInfo();
            info.Id = Id;
            info.EquipId = EquipId;
            info.Quality = Quality;
            foreach (LDNetAttrEffect effect in EquipEffects)
            {
                info.EquipEffect.Add(effect.Convert());
            }

            return info;
        }

        public bool CanReforge()
        {
            AttrRandomPoolItem item = AttrRandomPool.Data.Get(this.Quality);
            string[] costs = null;
            int equipSlotId = GetEquipSlotId();
            switch (equipSlotId)
            {
                case 1:
                    return item.reforgeConsume1.Length > 0;
                case 2:
                    return item.reforgeConsume2.Length > 0;
                case 3:
                    return item.reforgeConsume3.Length > 0;
                case 4:
                    return item.reforgeConsume4.Length > 0;
                default:
                    return false;
            }
        }

        public List<int> GetEffectIds()
        {
            List<int> temp = new List<int>();
            foreach (var equipEffect in EquipEffects)
            {
                temp.Add(equipEffect.EffectId);
            }

            return temp;
        }
    }

    public class LDNetEquipSlotInfo
    {
        public Dictionary<int, long> EquipSlotInfos = new();

        public void SyncEquipSlotInfo(int slotId, long equipId)
        {
            EquipSlotInfos[slotId] = equipId;
        }

        /// <summary>
        /// 获取装备的装备总属性
        /// </summary>
        /// <returns></returns>
        public List<LDEquipAttrAddition> GetAttrs()
        {
            Dictionary<int, LDEquipAttrAddition> attrs = new Dictionary<int, LDEquipAttrAddition>();
            foreach (KeyValuePair<int, long> pair in EquipSlotInfos)
            {
                if (pair.Value > 0)
                {
                    var equip = Global.gApp.gSystemMgr.gEquipmentMgr.Data.GetEquipInfo(pair.Value);
                    if (equip != null)
                    {
                        List<LDEquipAttrAddition> equipAttr = equip.AttrAddition;
                        foreach (LDEquipAttrAddition addition in equipAttr)
                        {
                            if (attrs.TryGetValue(addition.Id, out var attr))
                            {
                                attr.AddVal(addition.Val);
                            }
                            else
                            {
                                var item = addition.CopyItem();
                                attrs.Add(addition.Id, item);
                            }
                        }
                    }
                }
            }

            if (attrs.Count > 0)
            {
                return attrs.Values.ToList();
            }

            return new();
        }

        /// <summary>
        /// 判断是否装备了
        /// </summary>
        /// <param name="slotId"></param>
        /// <returns></returns>
        public bool CheckIsEquip(int slotId)
        {
            if (EquipSlotInfos.ContainsKey(slotId))
            {
                var uid = EquipSlotInfos[slotId];
                if (Global.gApp.gSystemMgr.gEquipmentMgr.GetEquipByUid(uid) != null)
                {
                    return true;
                }
            }

            return false;
        }

        public bool CheckHaveAnyEquiped()
        {
            foreach (KeyValuePair<int, long> pair in EquipSlotInfos)
            {
                if (pair.Value > 0 && Global.gApp.gSystemMgr.gEquipmentMgr.GetEquipByUid(pair.Value) != null)
                {
                    return true;
                }
            }

            return false;
        }

        public bool CheckIsEquip(long uid)
        {
            foreach (KeyValuePair<int, long> pair in EquipSlotInfos)
            {
                if (pair.Value == uid)
                {
                    return true;
                }
            }

            return false;
        }
    }
}