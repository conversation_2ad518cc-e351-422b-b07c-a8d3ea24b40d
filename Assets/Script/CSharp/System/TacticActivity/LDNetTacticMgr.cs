using System.Collections.Generic;
using System.Linq;
using LD.Protocol;

namespace LD
{
    public partial class LDNetTacticMgr : LDNetDataMgr
    {
        #region field
        private TacticBaseItem m_TacticBaseCfg;
        private List<TacticAttrItem> m_TacticAttrCfgList;
        private List<TacticTopRewardsItem> m_TacticTopRewardsCfgList;

        private int m_LastPassTacticItemId;// 最后关卡id
        private int m_ActivityMechaId; // 活动机甲id
        public int m_FightActivityId;
        public int m_FightTacticId;
        #endregion


        protected override void InitImp()
        {
        }
        protected override void DestroyImp()
        {
        }


        #region CreateTacticItemDataDic
        private void InitTacticData()
        {
            m_TacticBaseCfg = default;
            m_TacticAttrCfgList = default;
            m_TacticTopRewardsCfgList = default;
            m_LastPassTacticItemId = default;
            m_ActivityMechaId = default;

            List<TacticAttrItem> attrItems = GetTacticAttCfgList();
            foreach (TacticAttrItem val in attrItems)
            {
                 Data.AddTacticAttrItemData(val.id,new LDTacticAttrItemData(val));
            }
        }
        #endregion

        #region CreateFighter
        // private void InitializedLDNetFighter()
        // {
            // 构造数据结构
            // LDRoleInfoDTO selfInfo = Global.gApp.gSystemMgr.gRoleMgr.Data.RoleInfo;
            //
            // foreach (KeyValuePair<int, LDTacticAttrItemData> pair in Data.TacticAttrItemDataDic)
            // {
            //
            //     LDNetFighter fighter = new LDNetFighter();
            //     fighter.BriefRole = selfInfo;
            //     fighter.Model = new List<LDDIYData>();
            //     fighter.Model.Add(GetMirrorDIYData(pair.Value));
            //     fighter.MechaInfos = new List<LDNetRoleMechaInfo>();
            //     fighter.MechaInfos.Add(GetMirrorRoleMechaInfo(pair.Value));
            //     fighter.PartInfos = GetMirrorRoleDiyPartInfoList(pair.Value);
            //
            //     Data.AddTacticAttrFighter(pair.Key,fighter);
            // }
        // }

        private void InitializedLDNetFighterById(int attrId)
        {
            // 构造数据结构
            LDRoleInfoDTO selfInfo = Global.gApp.gSystemMgr.gRoleMgr.Data.RoleInfo;
            if (Data.TacticAttrItemDataDic.TryGetValue(attrId,out LDTacticAttrItemData attrItemData))
            {
                LDNetTacticFighter tacticFighter = new LDNetTacticFighter();
                tacticFighter.Fighter = new LDNetFighter();
                tacticFighter.Fighter.BriefRole = selfInfo;
                tacticFighter.Fighter.Model = new List<LDDIYData>();
                tacticFighter.Fighter.Model.Add(GetMirrorDIYData(attrItemData));
                tacticFighter.Fighter.MechaInfos = new List<LDNetRoleMechaInfo>();
                tacticFighter.Fighter.MechaInfos.Add(GetMirrorRoleMechaInfo(attrItemData));
                tacticFighter.Fighter.PartInfos = GetMirrorRoleDiyPartInfoList(attrItemData);

                int[] citiaoProbability = attrItemData.m_Data.citiaoProbability;
                if (citiaoProbability.Length > 0)
                {
                    tacticFighter.CitiaoWeightRate = new List<int>();
                    int rate = citiaoProbability[0];
                    for (int i = 1; i < citiaoProbability.Length; i++)
                    {
                        tacticFighter.CitiaoWeightRate.Add(citiaoProbability[i]);
                        tacticFighter.CitiaoWeightRate.Add(rate);
                    }
                }

                tacticFighter.AttrId = attrId;

                Data.AddTacticAttrFighter(attrId,tacticFighter);
            }
        }

        // 获取LDDIYData镜像数据
        private LDDIYData GetMirrorDIYData(LDTacticAttrItemData tacticatAttrItemData)
        {
            LDDIYData diyData = new LDDIYData();
            diyData.BodyId = tacticatAttrItemData.MechaId;
            diyData.BodySkinId = tacticatAttrItemData.MechaSkinId;

            List<TacticDIYItem> posItems = new List<TacticDIYItem>();
            foreach (TacticDIYItem posItem in TacticDIY.Data.items)
            {
                if(posItem.mechaId == tacticatAttrItemData.MechaId && posItem.group == tacticatAttrItemData.m_Data.groupId )
                {
                    posItems.Add(posItem);
                }
            }

            if (posItems.Count != tacticatAttrItemData.PartIds.Count)
            {
                Global.LogError($"TacticDIY 配置不一致！");
            }

            int index = -1;
            foreach (int pId in tacticatAttrItemData.PartIds)
            {
                index++;

                LDDIYPartData partData = new LDDIYPartData();
                partData.PId = pId;
                if (tacticatAttrItemData.SkinDic.ContainsKey(partData.PId))
                {
                    partData.SId = tacticatAttrItemData.SkinDic[partData.PId].Id;
                }

                TacticDIYItem item = posItems[index];
                partData.PN = item.bone;
                MyVector3 LP = new MyVector3();
                LP.x = item.position[0];
                LP.y = item.position[1];
                LP.z = item.position[2];
                partData.LP = LP;

                MyVector3 LR = new MyVector3();
                LR.x = item.rotation[0];
                LR.y = item.rotation[1];
                LR.z = item.rotation[2];
                partData.LR = LR;
                partData.SF = item.scale;

                diyData.Data.Add(partData);
            }
            return diyData;
        }
        // 获取RoleMechaInfo镜像数据
        private LDNetRoleMechaInfo GetMirrorRoleMechaInfo(LDTacticAttrItemData tacticatAttrItemData)
        {
            LDNetRoleMechaInfo roleMechaInfo = new LDNetRoleMechaInfo();

            roleMechaInfo.CfgId = tacticatAttrItemData.MechaId;
            roleMechaInfo.Quality = tacticatAttrItemData.Mecha.Quality;
            roleMechaInfo.Level = 1;
            roleMechaInfo.SkinId = tacticatAttrItemData.MechaSkinId;
            roleMechaInfo.SkinStar = tacticatAttrItemData.MechaSkinStar ;
            roleMechaInfo.AttrList.Clear();

            foreach (string attr in tacticatAttrItemData.Attrs)
            {
                string[] attrParam = LDCommonTools.Split(attr);
                if (attrParam.Length > 2)
                {
                    int attrType = LDParseTools.IntParse(attrParam[0]);
                    double attrVal = LDParseTools.DoubleParse(attrParam[2]);
                    LDAttributeItem attributeItem = new LDAttributeItem(attrType, attrVal);
                    LDAttributeDTO dto = new LDAttributeDTO();
                    dto.AttrItem = attributeItem;
                    roleMechaInfo.AttrList.Add(dto);
                }
            }
            return roleMechaInfo;
        }

        private List<LDNetRoleDiyPartInfo> GetMirrorRoleDiyPartInfoList(LDTacticAttrItemData tacticAttrItemData)
        {
            List<LDNetRoleDiyPartInfo> roleDiyPartInfoList = new List<LDNetRoleDiyPartInfo> ();
            foreach (LDCommonItem part in tacticAttrItemData.DiypartList)
            {
                RoleDiyPartInfo info = new RoleDiyPartInfo();
                info.Cfgid = part.Id;
                info.Quality = part.Quality;
                info.Level = 1;
                if (tacticAttrItemData.SkinDic.ContainsKey(part.Id))
                {
                    info.SkinId = tacticAttrItemData.SkinDic[part.Id].Id;
                }
                roleDiyPartInfoList.Add(new LDNetRoleDiyPartInfo(info)); ;
            }
            return roleDiyPartInfoList;
        }

        #endregion

        #region 外部方法
        // 获取当期的base配置
        public TacticBaseItem GetTacticBaseCfg()
        {
            if (m_TacticBaseCfg == default)
            {
                foreach (TacticBaseItem item in TacticBase.Data.items)
                {
                    if (item.activityID == Data.ActivityInfo.ActivityId )
                    {
                        m_TacticBaseCfg = item;
                        break;
                    }
                }
            }
            return m_TacticBaseCfg;
        }

        // 根据主键获取base配置
        public TacticBaseItem GetTargetTacticBaseCfg(int id)
        {
            if (id == Data.ActivityInfo.ActivityId)
            {
                return GetTacticBaseCfg();
            }
            return TacticBase.Data.Get(id);
        }

        // 获取TacticAttr配置列表
        public List<TacticAttrItem> GetTacticAttCfgList()
        {
            if (m_TacticAttrCfgList == default)
            {
                TacticBaseItem cfg = GetTacticBaseCfg();
                m_TacticAttrCfgList = new List<TacticAttrItem>();
                foreach (int val in cfg.tacticId)
                {
                    TacticAttrItem itemCfg = TacticAttr.Data.Get(val);
                    m_TacticAttrCfgList.Add(itemCfg);
                }
            }
            return m_TacticAttrCfgList;
        }

        // 根据tacticBase 主键配置获取attr配置列
        public List<TacticAttrItem> GetTargetTacticAttCfgList(int id)
        {
            List<TacticAttrItem> tacticAttrItemList = new List<TacticAttrItem>();
            if (id == Data.ActivityInfo.ActivityId)
            {
                return GetTacticAttCfgList();
            }
            TacticBaseItem cfg =  GetTargetTacticBaseCfg(id);
            foreach (int val in cfg.tacticId)
            {
                TacticAttrItem itemCfg = TacticAttr.Data.Get(val);
                if (!tacticAttrItemList.Contains(itemCfg))
                {
                    tacticAttrItemList.Add(itemCfg);
                }
            }
            return tacticAttrItemList;
        }

        // 获取当期机甲id
        public int GetCurActivityMechaId()
        {
            if (m_ActivityMechaId == default)
            {
                LDCommonItem item = new LDCommonItem(GetTacticBaseCfg().mechaId);
                m_ActivityMechaId = item.Id;
            }
            return m_ActivityMechaId;
        }

        // 获取档期排行榜配置
        public List<TacticTopRewardsItem> GetTacticTopRewardsCfgList()
        {
            if (m_TacticTopRewardsCfgList == default)
            {
                TacticBaseItem cfg = GetTacticBaseCfg();
                m_TacticTopRewardsCfgList = new List<TacticTopRewardsItem>();
                foreach (TacticTopRewardsItem val in TacticTopRewards.Data.items)
                {
                    if (val.activityID == cfg.activityID)
                    {
                        m_TacticTopRewardsCfgList.Add(val);
                    }
                }
            }
            return m_TacticTopRewardsCfgList;
        }

        // 根据子活动id获取fighter
        private LDNetTacticFighter GetTacticFighter(int tacticId)
        {
            if (!Data.TacticAttrFighterDic.ContainsKey(tacticId))
            {
                InitializedLDNetFighterById(tacticId);
            }
            return Data.TacticAttrFighterDic.GetValueOrDefault(tacticId);
        }

        // 获取UI主界面逻辑显示关卡信息
        public LDTacticActivityMissionInfo GetLogicShowMissionInfo()
        {
            // 权重1 关卡是否有奖励没领取
            LDTacticActivityMissionInfo missionInfo = GetHasRewardFirstMissionInfo();
            if (missionInfo != null)
            {
                return missionInfo;
            }
            // 权重2 当前应该挑战的关卡
            return GetCurBattleMissionInfo();
        }

        // 获取第一个没有领取奖励的关卡信息
        public LDTacticActivityMissionInfo GetHasRewardFirstMissionInfo()
        {
            foreach (LDTacticActivityMissionInfo val in Data.TacticActivityMissionList)
            {
                Data.TacticAttrItemDataDic.TryGetValue(val.TacticAttrId, out LDTacticAttrItemData attrItemData);
                if (attrItemData.m_Data.lastPass == 0 && val.CommonMissionInfo.IsPass && !val.CommonMissionInfo.IsReward)
                {
                    return val;
                }

                if (attrItemData.m_Data.lastPass == 1 && GetLastPassCanGetReward() )
                {
                    return val;
                }
            }
            return null;
        }

        // 获取最后一个关卡id
        public int GetLastPassTacticItemId()
        {
            if (m_LastPassTacticItemId == 0)
            {
                m_LastPassTacticItemId = Data.TacticActivityMissionList.Last().TacticAttrId;
            }
            return m_LastPassTacticItemId;
        }

        // 获取最后一个关卡是否有奖励可领取
        public bool GetLastPassCanGetReward()
        {
            LDTacticAttrItemData attrItemData = Data.TacticAttrItemDataDic[GetLastPassTacticItemId()];
            LDTacticActivityMissionInfo missionInfo = Data.TacticActivityMissionInfo[GetLastPassTacticItemId()];
            for (int i = 0; i < attrItemData.ScoreList.Count; i++)
            {
                int score = attrItemData.ScoreList[i];
                if ( missionInfo.RankMissionInfo.RewardIndex < i && missionInfo.RankMissionInfo.Score >= score)
                {
                    return true;
                }
            }
            return false;
        }

        // 获取当前要挑战的关卡信息
        public LDTacticActivityMissionInfo GetCurBattleMissionInfo()
        {
            foreach (LDTacticActivityMissionInfo val in Data.TacticActivityMissionList)
            {
                Data.TacticAttrItemDataDic.TryGetValue(val.TacticAttrId, out LDTacticAttrItemData attrItemData);
                if (attrItemData.m_Data.lastPass == 0 && !val.CommonMissionInfo.IsPass)
                {
                    return val;
                }
            }
            int tacticItemId =  GetLastPassTacticItemId();

            if (Data.TacticActivityMissionInfo.ContainsKey(tacticItemId))
            {
                return Data.TacticActivityMissionInfo[tacticItemId];
            }
            return null;
        }

        // 根据排名获取奖励配置
        public TacticTopRewardsItem GetCurTopRewardsItem(int ranked)
        {
            foreach (TacticTopRewardsItem val in GetTacticTopRewardsCfgList())
            {
                if (ranked >= val.maxRank && ranked <= val.minRank)
                {
                    return val;
                }
            }
            return null;
        }

        // 怪物属性
        public List<int> GetMonsterAttr(int attrId)
        {
            List<int> attrs = new List<int>();
            TacticAttrItem item = TacticAttr.Data.Get(attrId);
            if (item != null)
            {
                attrs.AddRange(item.monsterAttr);
            }
            return attrs;
        }

        // 能否领取活动机甲的奖励
        public bool CanGetMechaReward()
        {
            if (!Data.MechaReward)
            {
               LDNetMechaDTOItem  mechaData =  Global.gApp.gSystemMgr.gMechaDataMgr.GetMechaData(GetCurActivityMechaId(), true);
               return mechaData != null;
            }
            return false;
        }

        // UI关卡右侧是否有奖励
        public bool CanGetRewardForRight(int index)
        {
            index++;
            if (index >= 0 && index < Data.TacticActivityMissionList.Count )
            {
                for (int i = index; i < Data.TacticActivityMissionList.Count; i++)
                {
                    LDTacticActivityMissionInfo info = Data.TacticActivityMissionList[i];
                    if (info.TacticAttrId == GetLastPassTacticItemId())
                    {
                        return GetLastPassCanGetReward();
                    }
                    else
                    {
                        if (info.CommonMissionInfo.IsPass && !info.CommonMissionInfo.IsReward)
                        {
                            return true;
                        }
                    }
                }
            }
            return false;
        }

        // UI关卡左侧是否有奖励
        public bool CanGetRewardForLeft(int index)
        {
            index--;
            if (index >= 0 && index < Data.TacticActivityMissionList.Count )
            {
                for (int i = 0; i <= index ; i++)
                {
                    LDTacticActivityMissionInfo info = Data.TacticActivityMissionList[i];
                    if (info.TacticAttrId == GetLastPassTacticItemId())
                    {
                        return GetLastPassCanGetReward();
                    }
                    else
                    {
                        if (info.CommonMissionInfo.IsPass && !info.CommonMissionInfo.IsReward)
                        {
                            return true;
                        }
                    }
                }
            }
            return false;
        }

        // 功能开放
        public override bool IsUnlock(bool isShowLockTips = false)
        {
            if (!CheckModuleOpen(LDSystemEnum.Tactic, isShowLockTips))
                return false;
            LDNetActivityInfo info = Global.gApp.gSystemMgr.gDiyActivityMgr.Data.GetActivityInfo(LDNetActivityType.TacticType);
            bool isOpen = info != null && info.IsOpen();

            if (!isOpen && isShowLockTips)
            {
                Global.gApp.gToastMgr.ShowGameTips(95002);
            }

            return isOpen;
        }
        #endregion

        #region 拍脸

        public override bool TryShowPerformance()
        {
            LDNetActivityInfo activityInfo = Global.gApp.gSystemMgr.gTacticMgr.Data.ActivityInfo;
            if (activityInfo == null || !activityInfo.IsOpen())
            {
                return false;
            }

            string key = $"{LDLocalDataKeys.TaticActivity}_{activityInfo.ActivityId}_{activityInfo.BeginTime}";
            long nextShowTime = Global.gApp.gSystemMgr.gLocalDataMgr.GetLongVal(true, key);
            if (nextShowTime != 0 && DateTimeUtil.GetServerTime() < nextShowTime)
            {
                return false;
            }

            PerformanceTactic performanceTactic = Global.gApp.gUiMgr.GetPerformanceTask<PerformanceTactic>();
            performanceTactic.IsShow = true;

            Global.gApp.gUiMgr.TryShowPerformanceUI();

            return true;
        }

        #endregion

        #region 红点
        public override LDRedTipsState GetRedState(LDSystemEnum sys)
        {
            if (!IsUnlock())
            {
                return LDRedTipsState.None;
            }

            if (sys == LDSystemEnum.Custom_Tactic_MechaReward)
            {
                if (CanGetMechaReward())
                {
                    return LDRedTipsState.RedPoint;
                }
            }
            if (sys == LDSystemEnum.Tactic)
            {
                if (GetHasRewardFirstMissionInfo() != null)
                {
                    return LDRedTipsState.RedPoint;
                }
            }

            return LDRedTipsState.None;
        }
        #endregion
    }
}