
using System.Collections.Generic;
using LD.Protocol;

namespace LD
{
    // TacticAttr数据类
    public class LDTacticAttrItemData
    {
        public TacticAttrItem m_Data;
        public LDCommonItem Mecha;
        public List<LDCommonItem> DiypartList = new List<LDCommonItem>();
        public List<int> PartIds = new List<int>();
        public Dictionary<int, LDCommonItem> SkinDic = new Dictionary<int, LDCommonItem>(); // key ComboId
        public List<string> Attrs = new List<string>();
        public List<int> ScoreList = new List<int>();
        public Dictionary<long,LDCommonItem> ScoreRewardDic = new Dictionary<long, LDCommonItem>();
        public int MechaId;
        public int MechaSkinId = -1;
        public int MechaSkinStar = 0;

        public LDTacticAttrItemData(TacticAttrItem data)
        {
            m_Data = data;
            Mecha = new LDCommonItem(data.mechaId);
            DiypartList.Clear();
            foreach (string partId in data.diypartId)
            {
                LDCommonItem mecha = new LDCommonItem(partId);
                mecha.SetNum(0);
                DiypartList.Add(mecha);
            }
            SkinDic.Clear();
            foreach (string skinId in data.mechaSkinId)
            {
                LDCommonItem skin = new LDCommonItem(skinId);
                SkinDic.Add(skin.SkinCfg.ComboId,skin);
            }
            PartIds.Clear();
            foreach (LDCommonItem diypart in DiypartList)
            {
                PartIds.Add(diypart.Id);
                if (SkinDic.ContainsKey(diypart.Id))
                {
                    diypart.SetSkinId(SkinDic[diypart.Id].Id);
                }
                else
                {
                    diypart.SetSkinId(0);
                }
            }
            ScoreList.Clear();
            if (m_Data.lastPass == 1)
            {
                foreach (string val in m_Data.listRankItem)
                {
                    string[] args = LDCommonTools.Split(val,";");
                    int score = LDParseTools.IntParse(args[0]);
                    ScoreList.Add(score);
                }
            }

            Attrs.AddRange(data.Attr);
            MechaItem mechaItem = LD.Mecha.Data.Get(Mecha.Id);
            Attrs.AddRange(mechaItem.DeployProperties);

            MechaId = Mecha.Id;
            if (SkinDic.ContainsKey(MechaId))
            {
                MechaSkinId = SkinDic[MechaId].Id;
                MechaSkinStar = SkinDic[MechaId].Quality;
            }
        }
    }

    //排行榜关卡
    public class LDRankMissionInfo
    {
        public int Score { get; set; }
        public int RewardIndex { get; set; } = -1;

        public LDRankMissionInfo(RankMissionInfo data)
        {
            Score = data.Score;
            RewardIndex = data.RewardIndex;
        }

        public LDRankMissionInfo()
        {
        }
    }

    //普通关卡
    public class LDCommonMissionInfo
    {
        public bool IsPass { get; set; }
        public bool IsReward { get; set; }

        public LDCommonMissionInfo(CommonMissionInfo data)
        {
            IsPass = data.IsPass;
            IsReward = data.IsReward;
        }

        public LDCommonMissionInfo()
        {
        }
    }

    //单个关卡通关数据
    public class LDTacticActivityMissionInfo
    {
        //子活动id
        public int TacticAttrId { get; set; }
        //普通关卡信息
        public LDCommonMissionInfo CommonMissionInfo { get; set; }
        //排行榜关卡信息
        public LDRankMissionInfo RankMissionInfo { get; set; }

        public LDTacticActivityMissionInfo(TacticActivityMissionInfo data)
        {
            UpdateData(data);
        }

        public void UpdateData(TacticActivityMissionInfo data)
        {
            TacticAttrId = data.TacticAttrId;
            if (data.CommonMissionInfo !=null)
            {
                CommonMissionInfo = new LDCommonMissionInfo( data.CommonMissionInfo);
            }
            else
            {
                CommonMissionInfo = new LDCommonMissionInfo();
            }
            if (data.RankMissionInfo != null)
            {
                RankMissionInfo = new LDRankMissionInfo(data.RankMissionInfo);
            }
            else
            {
                RankMissionInfo = new LDRankMissionInfo();
            }
        }
    }

    // 封装Fighter
    public class LDNetTacticFighter
    {
        public List<int> CitiaoWeightRate = new List<int>();
        public int AttrId;
        public LDNetFighter Fighter;
    }

    // 战术特训活动数据
    public class LDNetTacticDTO
    {
        // type类型 tacticType
        public LDNetActivityInfo ActivityInfo { get; private set; }
        // 是否领取了机甲专属奖励
        public bool MechaReward { get; private set; }
        // 所有关卡信息 同字典value引用
        public List<LDTacticActivityMissionInfo> TacticActivityMissionList { get; private set; } = new List<LDTacticActivityMissionInfo>();

        // 所有关卡信息 key：子活动id
        public Dictionary<int, LDTacticActivityMissionInfo> TacticActivityMissionInfo { get; private set; }= new Dictionary<int, LDTacticActivityMissionInfo>();

        // 所有关卡战斗对象 key：子活动id
        public Dictionary<int, LDNetTacticFighter> TacticAttrFighterDic { get; private set; } = new Dictionary<int, LDNetTacticFighter>();

        // 所有关卡配置数据对象 key：子活动id
        public Dictionary<int, LDTacticAttrItemData> TacticAttrItemDataDic { get; private set; } = new Dictionary<int, LDTacticAttrItemData>();

        // 持有活动配置
        public void SyncActivityInfo(Protocol.ActivityInfo info)
        {
            ActivityInfo = new LDNetActivityInfo(info.ActivityId, info.BeginTime, info.EndTime);
        }

        // 注入服务器数据
        public void SetTacticActivityMissionInfo(TacticActivityMissionInfo data)
        {
            LDTacticActivityMissionInfo info = new LDTacticActivityMissionInfo(data);
            TacticActivityMissionList.Add(info);
            TacticActivityMissionInfo.Add(data.TacticAttrId,info);
        }

        public void UpdateTacticActivityMissionInfo(TacticActivityMissionInfo data)
        {
            if (data == null)
            {
                return;
            }
            if (TacticActivityMissionInfo.ContainsKey(data.TacticAttrId))
            {
                TacticActivityMissionInfo[data.TacticAttrId].UpdateData(data);
            }
        }

        public void SetMechaReward(bool isGet)
        {
            MechaReward = isGet;
        }

        public void AddTacticAttrFighter(int id,LDNetTacticFighter fighter)
        {
            TacticAttrFighterDic[id] = fighter;
        }

        public void AddTacticAttrItemData(int id, LDTacticAttrItemData data)
        {
            TacticAttrItemDataDic[id] = data;
        }
    }
}