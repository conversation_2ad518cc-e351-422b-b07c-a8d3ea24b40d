using System.Collections.Generic;
using LD.Protocol;

namespace LD
{
    public partial class LDNetBattlePassMgr : LDNetDataMgr
    {
        protected override void InitImp()
        {
        }

        protected override void DestroyImp()
        {
        }

        public override bool IsUnlock(bool isShowLockTips = false)
        {
            if (!CheckModuleOpen(LDSystemEnum.BattlePass, isShowLockTips))
                return false;
            bool isOpen = false;
            var data = Global.gApp.gSystemMgr.gBattlePassMgr.Data;
            if (data.m_BattleItemInfo.Count > 0)
            {
                if (Global.gApp.gSystemMgr.gBattlePassMgr.CheckCanShowBtn())
                {
                    isOpen = true;
                }
            }

            if (!isOpen)
            {
                isOpen = CheckGPCanShowBtn();
            }

            if (!isOpen && isShowLockTips)
            {
                Global.gApp.gToastMgr.ShowGameTips(95002);
            }

            return isOpen;
        }

        public override LDRedTipsState GetRedState(LDSystemEnum sys)
        {
            if (sys == LDSystemEnum.Custom_BattlePass_Level)
            {
                BattlePassItem[] items = BattlePass.Data.items;
                foreach (BattlePassItem item in items)
                {
                    var red = GetRedTipsState(item.id);
                    if (red != LDRedTipsState.None)
                    {
                        return red;
                    }
                }
            }
            else if (sys == LDSystemEnum.BattlePass)
            {
                return LDRedTipsState.None;
            }
            else
            {
                var battlePassId = GetBattleIdBySystem(sys);
                var red = GetGPRedTipsState(battlePassId);
                if (red != LDRedTipsState.None)
                {
                    return red;
                }
            }

            return LDRedTipsState.None;
        }

        public LDRedTipsState GetRedTipsState(int battlePassId)
        {
            BattlePassItem[] items = BattlePass.Data.items;
            foreach (BattlePassItem item in items)
            {
                if (battlePassId != item.id)
                    continue;

                LDBattlePassInfo battlePassInfo = Global.gApp.gSystemMgr.gPaymentMgr.Data.GetBattlePassMallData(battlePassId);
                if (battlePassInfo == null)
                {
                    return LDRedTipsState.None;
                }

                int lv = Global.gApp.gSystemMgr.gRoleMgr.GetLevel();
                foreach (KeyValuePair<int, LDBattlePassItemInfo> valuePair in Data.m_BattleItemInfo)
                {
                    LDBattlePassItemInfo info = valuePair.Value;
                    bool isAdvance = battlePassInfo.Advance;
                    bool isSuperAdvance = battlePassInfo.SuperAdvance;
                    bool isGetFree = Global.gApp.gSystemMgr.gBattlePassMgr.Data.CheckIsBuy(info.Id, BattlePassBuyType.Free.GetHashCode());
                    bool isGetAdvance = Global.gApp.gSystemMgr.gBattlePassMgr.Data.CheckIsBuy(info.Id, BattlePassBuyType.Advance.GetHashCode());
                    bool isGetSuperAdvance = Global.gApp.gSystemMgr.gBattlePassMgr.Data.CheckIsBuy(info.Id, BattlePassBuyType.SuperAdvance.GetHashCode());
                    BattlePassRewardItem cfg = BattlePassReward.Data.Get(info.Id);
                    if (lv >= cfg.lv)
                    {
                        if (!isGetFree)
                        {
                            return LDRedTipsState.RedPoint;
                        }

                        if (isAdvance && !isGetAdvance)
                        {
                            return LDRedTipsState.RedPoint;
                        }

                        if (isSuperAdvance && !isGetSuperAdvance)
                        {
                            return LDRedTipsState.RedPoint;
                        }
                    }
                }
            }

            return LDRedTipsState.None;
        }

        public LDRedTipsState GetGPRedTipsState(int battlePassId)
        {
            GamePlayBattlePass.Data.TryGet(battlePassId, out GamePlayBattlePassItem gpCfg, false);
            if (gpCfg != null)
            {
                if (gpCfg.preconditionsID > 0)
                {
                    List<int> preIds = new();
                    preIds.Add(gpCfg.preconditionsID);
                    int preId = gpCfg.preconditionsID;
                    while (preId > 0)
                    {
                        var cfgPre = GamePlayBattlePass.Data.Get(preId);
                        if (cfgPre == null)
                        {
                            break;
                        }

                        preId = cfgPre.preconditionsID;
                        if (preId > 0)
                        {
                            preIds.Add(preId);
                        }
                    }

                    foreach (int id in preIds)
                    {
                        if (Global.gApp.gSystemMgr.gBattlePassMgr.CheckBattlePassBtnShow(id))
                        {
                            return LDRedTipsState.None;
                        }
                    }
                }
            }

            GamePlayBattlePassRewardItem[] items = GamePlayBattlePassReward.Data.items;
            LDGPBattlePassInfo battlePassInfo = GPData.GetBattlePassInfo(battlePassId);
            foreach (GamePlayBattlePassRewardItem item in items)
            {
                if (item.battlepass_id != battlePassId)
                    continue;
                bool isUnlock = GPData.CheckIsUnlock(battlePassId, item.id);
                bool isAdvance = battlePassInfo?.Advance ?? false;
                bool isGetFree = GPData.CheckIsBuy(battlePassId, item.id, BattlePassBuyType.Free.GetHashCode());
                bool isGetAdvance = GPData.CheckIsBuy(battlePassId, item.id, BattlePassBuyType.Advance.GetHashCode());
                if (!isGetFree && isUnlock)
                {
                    return LDRedTipsState.RedPoint;
                }

                if (isAdvance && !isGetAdvance && isUnlock)
                {
                    return LDRedTipsState.RedPoint;
                }
            }

            if (gpCfg != null)
            {
                if (gpCfg.resetPeriod == 1)
                {
                    if (battlePassInfo != null)
                    {
                        Dictionary<int, LDNetCTaskInfo> tasks = battlePassInfo.m_TaskInfo;
                        foreach (KeyValuePair<int, LDNetCTaskInfo> task in tasks)
                        {
                            if (task.Value.Status == CTaskStatus.CFinish.GetHashCode() && battlePassInfo.Advance)
                            {
                                return LDRedTipsState.RedPoint;
                            }
                        }
                    }
                }
            }

            return LDRedTipsState.None;
        }

        public bool CheckCanShowBtn()
        {
            foreach (KeyValuePair<int, LDBattlePassItemInfo> valuePair in Data.m_BattleItemInfo)
            {
                LDBattlePassItemInfo info = valuePair.Value;
                bool isGetFree = Global.gApp.gSystemMgr.gBattlePassMgr.Data.CheckIsBuy(info.Id, BattlePassBuyType.Free.GetHashCode());
                bool isGetAdvance = Global.gApp.gSystemMgr.gBattlePassMgr.Data.CheckIsBuy(info.Id, BattlePassBuyType.Advance.GetHashCode());
                bool isGetSuperAdvance = Global.gApp.gSystemMgr.gBattlePassMgr.Data.CheckIsBuy(info.Id, BattlePassBuyType.SuperAdvance.GetHashCode());
                if (!isGetFree || !isGetAdvance || !isGetSuperAdvance)
                {
                    return true;
                }
            }

            return false;
        }

        public bool CheckGPCanShowBtn()
        {
            foreach (KeyValuePair<int, LDGPBattlePassInfo> valuePair in GPData.m_BattlePassInfo)
            {
                bool show = CheckGPCanShowBtn(valuePair.Value.BattlePassId);
                if (show)
                {
                    return true;
                }
            }

            return false;
        }

        public bool CheckGPCanShowBtn(int battlePassId)
        {
            var cfg = GamePlayBattlePass.Data.Get(battlePassId);

            if (cfg.preconditionsID > 0)
            {
                List<int> preIds = new();
                preIds.Add(cfg.preconditionsID);
                int preId = cfg.preconditionsID;
                while (preId > 0)
                {
                    var cfgPre = GamePlayBattlePass.Data.Get(preId);
                    if (cfgPre == null)
                    {
                        break;
                    }

                    preId = cfgPre.preconditionsID;
                    if (preId > 0)
                    {
                        preIds.Add(preId);
                    }
                }

                foreach (int id in preIds)
                {
                    bool isShowPre = CheckGPCanShowBtn(id);
                    if (isShowPre)
                    {
                        return false;
                    }
                }
            }

            if (!Global.gApp.gSystemMgr.gFilterMgr.Filter(cfg.unlockCondition))
                return false;
            if (cfg.resetPeriod != 1)
            {
                LDGPBattlePassInfo info = GPData.GetBattlePassInfo(battlePassId);
                if (info == null)
                {
                    return false;
                }

                GamePlayBattlePassRewardItem[] items = GamePlayBattlePassReward.Data.items;
                foreach (GamePlayBattlePassRewardItem rewardItem in items)
                {
                    if (rewardItem.battlepass_id == battlePassId)
                    {
                        bool isGetFree = Global.gApp.gSystemMgr.gBattlePassMgr.GPData.CheckIsBuy(info.BattlePassId, rewardItem.id, BattlePassBuyType.Free.GetHashCode());
                        bool isGetAdvance = Global.gApp.gSystemMgr.gBattlePassMgr.GPData.CheckIsBuy(info.BattlePassId, rewardItem.id, BattlePassBuyType.Advance.GetHashCode());
                        if (!isGetFree || !isGetAdvance)
                        {
                            return true;
                        }
                    }
                }
            }
            else
            {
                return true;
            }

            return false;
        }


        /// <summary>
        /// 玩法战力是否显示按钮
        /// </summary>
        /// <param name="sys"></param>
        /// <returns></returns>
        public bool CheckBattlePassBtnShow(LDSystemEnum sys)
        {
            switch (sys)
            {
                case LDSystemEnum.Custom_BattlePass_Level:
                    return CheckCanShowBtn();
                case LDSystemEnum.Custom_BattlePass_CoinPass:
                case LDSystemEnum.Custom_BattlePass_Team:
                case LDSystemEnum.Custom_BattlePass_PveTower:
                case LDSystemEnum.Custom_BattlePass_PveTower2:
                case LDSystemEnum.Custom_BattlePass_Expedition:
                    int battlePassId = GetBattleIdBySystem(sys);
                    return CheckGPCanShowBtn(battlePassId);
            }

            return false;
        }

        public bool CheckBattlePassBtnShow(int battlePassId)
        {
            BattlePass.Data.TryGet(battlePassId, out var item, false);
            if (item != null)
            {
                return CheckCanShowBtn();
            }
            else
            {
                return CheckGPCanShowBtn(battlePassId);
            }
        }


        public int GetBattleIdBySystem(LDSystemEnum systemEnum)
        {
            int battlePassId = 1;
            switch (systemEnum)
            {
                case LDSystemEnum.Custom_BattlePass_Level:
                    battlePassId = 1;
                    break;
                case LDSystemEnum.Custom_BattlePass_CoinPass:
                    battlePassId = 5;
                    break;
                case LDSystemEnum.Custom_BattlePass_Team:
                    battlePassId = 8;
                    break;
                case LDSystemEnum.Custom_BattlePass_PveTower:
                    battlePassId = 6;
                    break;
                case LDSystemEnum.Custom_BattlePass_Expedition:
                    battlePassId = 7;
                    break;
                case LDSystemEnum.Custom_BattlePass_PveTower2:
                    battlePassId = 9;
                    bool show = Global.gApp.gSystemMgr.gBattlePassMgr.CheckGPCanShowBtn(battlePassId);
                    if (!show)
                    {
                        battlePassId = Global.gApp.gSystemMgr.gBattlePassMgr.GetTowerBattlePassId(battlePassId);
                    }

                    break;
            }

            return battlePassId;
        }

        public int GetTowerBattlePassId(int id)
        {
            int nextId = 0;
            GamePlayBattlePassItem[] cfgs = GamePlayBattlePass.Data.items;
            for (int i = 0; i < cfgs.Length; i++)
            {
                var cfg = cfgs[i];
                if (cfg.preconditionsID == id)
                {
                    nextId = cfg.id;
                    break;
                }
            }

            if (nextId == 0)
            {
                return id;
            }
            else
            {
                bool canShow = Global.gApp.gSystemMgr.gBattlePassMgr.CheckGPCanShowBtn(nextId);
                if (canShow)
                {
                    return nextId;
                }
                else
                {
                    return GetTowerBattlePassId(nextId);
                }
            }
        }

        public string GetProgressByCondition(string conditionStr, LDNetCTaskInfo taskInfo = null)
        {
            string[] conditions = LDCommonTools.Split(conditionStr);
            switch (conditions[0])
            {
                case "57":
                {
                    var missionId = LDParseTools.IntParse(conditions[1]);
                    if (taskInfo != null)
                    {
                        missionId = Global.gApp.gSystemMgr.gCoinPassMgr.GetMaxFinishedId();
                    }

                    var missionCfg = CoinMission.Data.Get(missionId);
                    if (missionCfg != null)
                    {
                        var str = $"{missionCfg.Order}-{missionCfg.PassNum}";
                        return str;
                    }
                    else
                    {
                        return conditions[1];
                    }
                }
                case "58":
                {
                    var missionId = LDParseTools.IntParse(conditions[1]);
                    if (taskInfo != null)
                    {
                        missionId = Global.gApp.gSystemMgr.gPveTowerDataMgr.GetMaxFinishedPassId();
                    }

                    var missionCfg = TowerMission.Data.Get(missionId);
                    if (missionCfg != null)
                    {
                        var str = $"{missionCfg.order}-{missionCfg.StageNumber}";
                        return str;
                    }
                    else
                    {
                        return conditions[1];
                    }
                }

                default:

                    if (taskInfo != null)
                    {
                        return taskInfo.Progress.ToString();
                    }

                    return conditions[1];
            }
        }
    }
}