using System.Collections.Generic;
using System.Linq;

namespace LD
{
    public partial class LDNetMechaAssistMgr : LDNetDataMgr
    {
        private Dictionary<int, List<AssistSkillItem>> assistSkillCache = new();

        public Dictionary<int, List<AssistSkillItem>> m_AssistSkillCache
        {
            get
            {
                if (assistSkillCache.Count <= 0)
                {
                    CheckAssistSkillItem();
                }

                return assistSkillCache;
            }
        }

        private Dictionary<int, AssistLvItem> assistLvItems = new();

        public Dictionary<int, AssistLvItem> m_AssistLVItems
        {
            get
            {
                if (assistLvItems.Count <= 0)
                {
                    CheckData();
                }

                return assistLvItems;
            }
        }

        private List<AssistLvItem> assistLvList = new();

        public List<AssistLvItem> m_AllAssistLvItems
        {
            get
            {
                if (assistLvList.Count <= 0)
                {
                    CheckData();
                }

                return assistLvList;
            }
        }


        private int m_ClickLvUpBtnTimes;

        private void CheckData()
        {
            assistLvList = new();
            var items = AssistLv.Data.items;
            foreach (AssistLvItem lvItem in items)
            {
                assistLvItems.Add(lvItem.slotLV, lvItem);
                assistLvList.Add(lvItem);
            }

            assistLvList.Sort((x, y) => x.slotLV.CompareTo(y.slotLV));
        }


        protected override void InitImp()
        {
        }

        protected override void DestroyImp()
        {
        }

        public override bool IsUnlock(bool isShowLockTips = false)
        {
            return CheckModuleOpen(LDSystemEnum.Assist, isShowLockTips);
        }

        public override LDRedTipsState GetRedState(LDSystemEnum sys)
        {
            if (sys == LDSystemEnum.Custom_Assist_AutoEquip)
            {
                if (CheckCanAutoEquip())
                {
                    return LDRedTipsState.Arrow;
                }
            }

            if (sys == LDSystemEnum.Custom_Assist_LevelUp)
            {
                var allSlotCfg = AssistSlot.Data.items;
                foreach (AssistSlotItem slotItem in allSlotCfg)
                {
                    if (Global.gApp.gSystemMgr.gFilterMgr.Filter(slotItem.unlock))
                    {
                        var state = GetSlotTipsState(slotItem.slot);
                        if (state > LDRedTipsState.None)
                        {
                            return state;
                        }
                    }
                }
            }

            return LDRedTipsState.None;
        }

        public LDRedTipsState GetSlotTipsState(int slotId)
        {
            var slotInfo = GetSlotInfo(slotId);
            if (slotInfo == null || (slotInfo.MechaId <= 0))
            {
                return LDRedTipsState.None;
            }

            int level = slotInfo?.Level ?? 0;
            var cfg = Global.gApp.gSystemMgr.gAssistMgr.GetAssistLvItem(level);
            bool isMaxLevel = Global.gApp.gSystemMgr.gAssistMgr.IsSlotMaxLevel(slotId);
            if (isMaxLevel)
                return LDRedTipsState.None;

            bool isEnough = true;
            foreach (var cost in cfg.upgradeItem)
            {
                var lines = LDCommonTools.Split(cost);
                var need = LDParseTools.IntParse(lines[2]);
                var hasCount = Global.gApp.gSystemMgr.gBagMgr.GetItemCount(LDParseTools.IntParse(lines[1]));
                if (hasCount < need)
                {
                    isEnough = false;
                    break;
                }
            }

            if (isEnough)
                return LDRedTipsState.Arrow;
            return LDRedTipsState.None;
        }

        public bool IsMechaEquiped(int mechaId)
        {
            if (mechaId <= 0)
                return false;
            foreach (KeyValuePair<int, LDNetMechaAssistSlotInfo> slotInfo in Data.m_MechaAssitDict)
            {
                if (mechaId == slotInfo.Value.MechaId)
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 获取槽位信息
        /// </summary>
        /// <param name="slotId"></param>
        /// <returns></returns>
        public LDNetMechaAssistSlotInfo GetSlotInfo(int slotId)
        {
            return Data.m_MechaAssitDict.GetValueOrDefault(slotId);
        }

        /// <summary>
        /// 判断槽位是否达到最大等级
        /// </summary>
        /// <param name="slotId"></param>
        /// <returns></returns>
        public bool IsSlotMaxLevel(int slotId)
        {
            var slotInfo = GetSlotInfo(slotId);
            if (slotInfo == null)
                return false;
            var maxLvCfg = Global.gApp.gSystemMgr.gAssistMgr.m_AllAssistLvItems[^1];
            if (maxLvCfg != null)
            {
                return slotInfo.Level >= maxLvCfg.slotLV;
            }

            return false;
        }

        /// <summary>
        /// 获取属性加成
        /// </summary>
        /// <returns></returns>
        public List<LDAttrAddition> GetAssistAttrAdditions()
        {
            List<LDAttrAddition> AttrAdditions = new List<LDAttrAddition>();

            var slotItems = AssistSlot.Data.items;
            foreach (AssistSlotItem slotItem in slotItems)
            {
                List<LDAttrAddition> slotAttr = GetAssistSlotAttrAdditions(slotItem.slot);
                var slotInfo = GetSlotInfo(slotItem.slot);
                if (slotInfo == null || slotInfo.MechaId <= 0)
                {
                    continue;
                }


                foreach (LDAttrAddition slotA in slotAttr)
                {
                    LDAttrAddition disAttr = null;
                    foreach (LDAttrAddition addition in AttrAdditions)
                    {
                        if (addition.Id == slotA.Id)
                        {
                            disAttr = addition;
                        }
                    }

                    if (disAttr != null)
                    {
                        disAttr.AddVal(slotA.Val);
                    }
                    else
                    {
                        AttrAdditions.Add(slotA);
                    }
                }
            }

            AssistResonanceItem resonanceItem = GetUnlockResonanceItem();
            if (resonanceItem != null)
            {
                foreach (string s in resonanceItem.resonanceAttr)
                {
                    var attrItem = new LDAttrAddition(s);
                    LDAttrAddition disAttr = null;
                    foreach (LDAttrAddition addition in AttrAdditions)
                    {
                        if (addition.Id == attrItem.Id)
                        {
                            disAttr = addition;
                        }
                    }

                    if (disAttr != null)
                    {
                        disAttr.AddVal(attrItem.Val);
                    }
                    else
                    {
                        AttrAdditions.Add(attrItem);
                    }
                }
            }

            if (AttrAdditions.Count <= 0)
            {
                return GetDefaultAttr();
            }

            return AttrAdditions;
        }

        public List<LDAttrAddition> GetAssistSlotAttrAdditions(int slotId)
        {
            List<LDAttrAddition> AttrAdditions = new List<LDAttrAddition>();
            var slotInfo = Data.m_MechaAssitDict.GetValueOrDefault(slotId);
            if (slotInfo == null)
            {
                return GetDefaultAttr();
            }
            else
            {
                if (slotInfo.MechaId <= 0)
                {
                    return GetDefaultAttr();
                }

                List<LDAttrAddition> mechaQuaAddition = new List<LDAttrAddition>();
                if (slotInfo.MechaId > 0)
                {
                    // 属性
                    var mecha = Global.gApp.gSystemMgr.gMechaDataMgr.GetMechaData(slotInfo.MechaId);
                    MechaQualityItem quaCfg = MechaQuality.Data.Get(LDQualityType.Astrict(mecha.Quality));
                    mechaQuaAddition = Global.gApp.gSystemMgr.gMechaDataMgr.GetQualityAttrAdditions(mecha.MechaId, quaCfg);

                    var slotLevel = slotInfo?.Level ?? 0;
                    var mechId = slotInfo?.MechaId ?? 0;
                    var assistLvItem = Global.gApp.gSystemMgr.gAssistMgr.GetAssistMechaSkill(mechId, slotLevel);

                    var attrInherit = 0;
                    if (assistLvItem != null)
                    {
                        attrInherit = assistLvItem.inheritAttr;
                    }


                    foreach (LDAttrAddition slotA in mechaQuaAddition)
                    {
                        slotA.CoefficientVal(attrInherit);
                    }
                }

                AssistLvItem lvCfg = GetAssistLvItem(slotInfo.Level);
                foreach (string s in lvCfg.slotAttr)
                {
                    var attrItem = new LDAttrAddition(s);
                    AttrAdditions.Add(attrItem);
                }

                // TODO: 继承属性
                // int mechaInheriaAttr = lvCfg.inheritAttr;

                foreach (LDAttrAddition mechaAttr in mechaQuaAddition)
                {
                    if (mechaAttr.Val <= 0)
                    {
                        continue;
                    }

                    // mechaAttr.CoefficientVal(mechaInheriaAttr);

                    LDAttrAddition disAttr = null;
                    foreach (LDAttrAddition addition in AttrAdditions)
                    {
                        if (addition.Id == mechaAttr.Id)
                        {
                            disAttr = addition;
                        }
                    }

                    if (disAttr != null)
                    {
                        disAttr.AddVal(mechaAttr.Val);
                    }
                    else
                    {
                        AttrAdditions.Add(mechaAttr);
                    }
                }
            }

            return AttrAdditions;
        }

        public List<LDAttrAddition> GetDefaultAttr()
        {
            List<LDAttrAddition> AttrAdditions = new List<LDAttrAddition>();
            AttrAdditions.Add(new LDAttrAddition($"{LDAttrEnum.Hp}_1_0"));
            AttrAdditions.Add(new LDAttrAddition($"{LDAttrEnum.Attack}_1_0"));
            AttrAdditions.Add(new LDAttrAddition($"{LDAttrEnum.Defense}_1_0"));

            return AttrAdditions;
        }


        /// <summary>
        /// 获取已经装备的技能
        /// </summary>
        /// <returns></returns>
        public List<AssistSkillInfo> GetAssistSkills()
        {
            List<AssistSkillInfo> temp = new();
            foreach (KeyValuePair<int, LDNetMechaAssistSlotInfo> slotInfo in Data.m_MechaAssitDict)
            {
                var info = GetAssistSlotSkill(slotInfo.Value.SlotId);
                if (info != null)
                {
                    temp.Add(info);
                }
            }

            return temp;
        }

        public AssistSkillInfo GetAssistSlotSkill(int slot)
        {
            var slotInfo = Data.m_MechaAssitDict.GetValueOrDefault(slot);
            if (slotInfo == null || slotInfo.MechaId <= 0 || slotInfo.Level <= 0)
            {
                return null;
            }

            int level = slotInfo.Level;
            List<AssistSkillItem> skillList = m_AssistSkillCache[slotInfo.MechaId];
            skillList.Sort((a, b) => a.lvUnlock.CompareTo(b.lvUnlock));
            AssistSkillItem cfg = null;
            foreach (AssistSkillItem skill in skillList)
            {
                if (level >= skill.lvUnlock)
                {
                    cfg = skill;
                }
            }

            if (cfg != null)
            {
                return new AssistSkillInfo()
                {
                    SlotId = slotInfo.SlotId,
                    skill = cfg,
                };
            }

            return null;
        }

        public AssistSkillItem GetAssistMechaSkill(int mechaId, int level)
        {
            List<AssistSkillItem> skillList = m_AssistSkillCache.GetValueOrDefault(mechaId);
            if (skillList == null)
                return null;
            skillList.Sort((a, b) => b.lvUnlock.CompareTo(a.lvUnlock));
            foreach (AssistSkillItem skill in skillList)
            {
                if (level >= skill.lvUnlock)
                {
                    return skill;
                }
            }

            return null;
        }

        /// <summary>
        /// 槽位机甲技能预览
        /// </summary>
        /// <param name="mechaId"></param>
        /// <returns></returns>
        public List<AssistSkillPreview> GetAssistSlotSkillPreview(int mechaId)
        {
            if (mechaId == 0)
                return null;
            List<AssistSkillItem> skillList = m_AssistSkillCache[mechaId];
            skillList.Sort((a, b) => a.lvUnlock.CompareTo(b.lvUnlock));
            List<AssistSkillPreview> skillPreviews = new();
            List<int> allcitiao = new();
            foreach (AssistSkillItem skill in skillList)
            {
                bool addCitiao = false;
                foreach (string s in skill.ciTiaoID)
                {
                    var citiao = LDParseTools.IntParse(s);
                    if (allcitiao.Contains(citiao) == false)
                    {
                        allcitiao.Add(citiao);

                        addCitiao = true;
                    }
                }

                if (!addCitiao)
                {
                    if (skill.inheritAttr > 0)
                    {
                        skillPreviews.Add(new AssistSkillPreview()
                        {
                            Level = skill.lvUnlock,
                            attr = skill.inheritAttr
                        });
                    }
                }
                else
                {
                    skillPreviews.Add(new AssistSkillPreview()
                    {
                        Level = skill.lvUnlock,
                        Citiao = skill.tipsID,
                    });
                }
            }

            return skillPreviews;
        }


        private void CheckAssistSkillItem()
        {
            var allItems = AssistSkill.Data.items;
            foreach (AssistSkillItem item in allItems)
            {
                if (assistSkillCache.TryGetValue(item.mechaID, out List<AssistSkillItem> dic))
                {
                    dic.Add(item);
                }
                else
                {
                    assistSkillCache.Add(item.mechaID, new List<AssistSkillItem>() { item });
                }
            }
        }

        public List<LDNetMechaAssistSlotInfo> GetAllSlotInfo()
        {
            return Data.m_MechaAssitDict.Values.ToList();
        }

        public AssistLvItem GetAssistLvItem(int level)
        {
            return m_AssistLVItems.GetValueOrDefault(level);
        }

        /// <summary>
        /// 槽位名称
        /// </summary>
        /// <param name="slotId"></param>
        /// <returns></returns> 
        public int GetSlotName(int slotId)
        {
            switch (slotId)
            {
                case 1:
                    return 66301;
                case 2:
                    return 66302;
                case 3:
                    return 66303;
                default:
                    return 0;
            }
        }


        //是否达成共鸣
        public bool IsResonance(string condition)
        {
            var conditions = LDCommonTools.Split(condition);
            bool limit = conditions[0] == "1";
            int quality = LDParseTools.IntParse(conditions[1]);
            int count = LDParseTools.IntParse(conditions[2]);

            if (GetMechaCount(limit, quality) >= count)
            {
                return true;
            }

            return false;
        }

        public int GetMechaCount(bool isLimit, int quality)
        {
            int count = 0;
            foreach (KeyValuePair<int, LDNetMechaAssistSlotInfo> pair in Data.m_MechaAssitDict)
            {
                if (pair.Value.MechaId > 0)
                {
                    var mechaInfo = Global.gApp.gSystemMgr.gMechaDataMgr.GetMechaData(pair.Value.MechaId);
                    var mechaCfg = Mecha.Data.Get(mechaInfo.MechaId);
                    if (isLimit)
                    {
                        if (mechaCfg.rarity != 2)
                        {
                            continue;
                        }
                    }

                    if (mechaInfo.Quality >= quality)
                    {
                        count++;
                    }
                }
            }

            return count;
        }


        /// <summary>
        /// 当前激活的共鸣
        /// </summary>
        /// <returns></returns>
        public AssistResonanceItem GetUnlockResonanceItem()
        {
            var allItems = AssistResonance.Data.items;
            for (int i = allItems.Length - 1; i >= 0; i--)
            {
                var cfg = allItems[i];
                if (IsResonance(cfg.condition))
                {
                    return cfg;
                }
            }

            return null;
        }

        /// <summary>
        /// 获取共鸣等ji
        /// </summary>
        /// <returns></returns>
        public int GetResonanceLevel()
        {
            var cfg = GetUnlockResonanceItem();
            if (cfg == null)
                return 0;
            return cfg.resonanceLv;
        }


        /// <summary>
        /// 检查是否可以一键上阵
        /// </summary>
        /// <returns></returns>
        public bool CheckCanAutoEquip()
        {
            var equipList = new List<int>();
            var unlockSlotCount = 0;
            var allSlotCfg = AssistSlot.Data.items;
            foreach (AssistSlotItem slotItem in allSlotCfg)
            {
                if (Global.gApp.gSystemMgr.gFilterMgr.Filter(slotItem.unlock))
                {
                    unlockSlotCount++;
                    var slotInfo = GetSlotInfo(slotItem.slot);
                    if (slotInfo != null && slotInfo.MechaId > 0)
                    {
                        equipList.Add(slotInfo.MechaId);
                    }
                }
            }


            Dictionary<int, double> mechaPowerDict = new();
            List<LDNetMechaDTOItem> allMecha = Global.gApp.gSystemMgr.gMechaDataMgr.GetAllMecha();
            double equipMechPower = 0;
            int equipBattleMecha = Global.gApp.gSystemMgr.gMechaDataMgr.GetCurBattleMecha().MechaId;
            foreach (LDNetMechaDTOItem item in allMecha)
            {
                if (equipBattleMecha == item.MechaId)
                {
                    continue;
                }

                var power = GetMechaPower(item.MechaId);
                mechaPowerDict.Add(item.MechaId, power);
                if (equipList.Contains(item.MechaId))
                {
                    equipMechPower += power;
                }
            }

            if (equipList.Count < unlockSlotCount && mechaPowerDict.Count >= unlockSlotCount)
            {
                return true;
            }

            var allMechPowerList = mechaPowerDict.Values.ToList();
            allMechPowerList.Sort((a, b) => (int)(b - a));

            double haveMechaMaxPower = 0;
            for (int i = 0; i < unlockSlotCount; i++)
            {
                if (allMechPowerList.Count > i)
                {
                    haveMechaMaxPower += allMechPowerList[i];
                }
            }

            return equipMechPower < haveMechaMaxPower;
        }

        /// <summary>
        /// 获取一键上阵的机甲Id
        /// </summary>
        /// <returns></returns>
        public List<int> GetAutoEquipList()
        {
            var unlockSlotCount = 0;
            var allSlotCfg = AssistSlot.Data.items;
            foreach (AssistSlotItem slotItem in allSlotCfg)
            {
                if (Global.gApp.gSystemMgr.gFilterMgr.Filter(slotItem.unlock))
                {
                    unlockSlotCount++;
                }
            }

            List<AutoEquipMechaInfo> autoEquipList = new();

            List<LDNetMechaDTOItem> allMecha = Global.gApp.gSystemMgr.gMechaDataMgr.GetAllMecha();
            int equipBattleMecha = Global.gApp.gSystemMgr.gMechaDataMgr.GetCurBattleMecha().MechaId;
            foreach (LDNetMechaDTOItem item in allMecha)
            {
                if (equipBattleMecha == item.MechaId)
                {
                    continue;
                }

                var power = GetMechaPower(item.MechaId);
                autoEquipList.Add(new() { MechaId = item.MechaId, Power = power });
            }

            autoEquipList.Sort((a, b) => (int)(b.Power - a.Power));

            List<int> equipMecha = new List<int>();
            for (int i = 0; i < unlockSlotCount; i++)
            {
                if (autoEquipList.Count > i)
                {
                    equipMecha.Add(autoEquipList[i].MechaId);
                }
            }

            return equipMecha;
        }

        /// <summary>
        /// 计算上阵机甲的战斗力
        /// </summary>
        /// <returns></returns>
        public double GetMechaPower(int mechaId)
        {
            double power = 0;
            var mecha = Global.gApp.gSystemMgr.gMechaDataMgr.GetMechaData(mechaId);
            MechaQualityItem quaCfg = MechaQuality.Data.Get(LDQualityType.Astrict(mecha.Quality));
            var attr = Global.gApp.gSystemMgr.gMechaDataMgr.GetQualityAttrAdditions(mechaId, quaCfg);

            Dictionary<int, LDAttrAddition> mechaAttrDict = new();

            foreach (LDAttrAddition attrAddition in attr)
            {
                mechaAttrDict.Add(attrAddition.Id, attrAddition);
            }

            power = UiTools.CalculateFightPower(mechaAttrDict);

            return power;
        }

        /// <summary>
        /// 上阵主机甲，下阵助战的机甲
        /// </summary>
        /// <param name="mechaId"></param>
        public void UnEquipMecha(int mechaId)
        {
            foreach (KeyValuePair<int, LDNetMechaAssistSlotInfo> slotInfo in Data.m_MechaAssitDict)
            {
                if (slotInfo.Value.MechaId == mechaId)
                {
                    slotInfo.Value.MechaId = 0;
                }
            }
        }


        /// <summary>
        /// 机甲助战战斗数据
        /// </summary>
        /// <returns></returns>
        public List<AssistBattleInfo> GetAssistBattleInfos()
        {
            List<AssistBattleInfo> battleInfoList = new();
            var assistMechaList = AssistMecha.Data.items;
            foreach (KeyValuePair<int, LDNetMechaAssistSlotInfo> pair in Data.m_MechaAssitDict)
            {
                if (pair.Value.MechaId > 0)
                {
                    AssistBattleInfo info = new();
                    info.MechaId = pair.Value.MechaId;
                    info.SlotId = pair.Value.SlotId;

                    var assistLv = GetAssistMechaSkill(pair.Value.MechaId, pair.Value.Level);
                    if (assistLv != null)
                    {
                        foreach (string s in assistLv.ciTiaoID)
                        {
                            info.CitiaoList.Add(LDParseTools.IntParse(s));
                        }
                    }

                    foreach (AssistMechaItem item in assistMechaList)
                    {
                        if (item.mechaID == pair.Value.MechaId)
                        {
                            info.AssistMechaId = item.id;
                            // info.AdmissionCD = item.AdmissionCD;
                            // info.Duration = item.duration;
                            break;
                        }
                    }

                    battleInfoList.Add(info);
                }
            }

            battleInfoList.Sort((a, b) => (int)(a.SlotId - b.SlotId));
            return battleInfoList;
        }

        // 检测 单击升级 长按提示
        public void CheckLvUpLongPressTips()
        {
            m_ClickLvUpBtnTimes++;
            if (m_ClickLvUpBtnTimes == 5) // 第5次检测一下就行了 不重要
            {
                long time = Global.gApp.gSystemMgr.gLocalDataMgr.GetLongVal(true, LDLocalDataKeys.Assist_LvBtnLongPressTips, 0);
                if (DateTimeUtil.SameDay(time, DateTimeUtil.GetServerTime()))
                {
                    return;
                }

                int val = Global.gApp.gSystemMgr.gLocalDataMgr.GetIntVal(true, LDLocalDataKeys.Assist_LvBtnLongPressTips_NoTips, 0);
                if (val <= 0)
                {
                    Global.gApp.gUiMgr.OpenUIAsync<LongPressTipsUI>(LDUICfg.LongPressTipsUI).SetLoadedCall(tipsUI =>
                        tipsUI.RefreshUI(LongPressTipsUI.Type_AssistLvUp)
                    );
                    Global.gApp.gSystemMgr.gLocalDataMgr.SetLongVal(true, LDLocalDataKeys.Assist_LvBtnLongPressTips, DateTimeUtil.GetServerTime());
                }
            }
        }


        // 存储单机升级长按提示
        public void SaveLvUpBtnLongPressState(bool enable)
        {
            Global.gApp.gSystemMgr.gLocalDataMgr.SetIntVal(true, LDLocalDataKeys.Assist_LvBtnLongPressTips_NoTips, enable ? 1 : 0);
        }

        //public AssistMechaCoordinateItem GetMechaCoordinateItem(int mechaId)
        //{
        //    var items = AssistMecha.DataCoordinate.items;
        //    foreach (AssistMechaCoordinateItem item in items)
        //    {
        //        if (item.mechaID == mechaId)
        //        {
        //            return item;
        //        }
        //    }

        //    return null;
        //}
        public bool IsCanGuide()
        {
            if (Data.m_MechaAssitDict.Count > 0)
            {
                return false;
            }

            return true;
        }
    }

    public class AssistSkillInfo
    {
        public int SlotId;
        public AssistSkillItem skill;
    }

    class AutoEquipMechaInfo
    {
        public int MechaId;
        public double Power;
    }

    public class AssistBattleInfo
    {
        public int SlotId;
        public int MechaId;
        public int AssistMechaId;

        public int SkinId = -1;

        // TODO: dqx 助战机甲隐藏皮肤问题
        public int HideSkin = -1;
        public AssistMechaItem AssistMechaItem;
        public List<int> CitiaoList = new();

        public void Init()
        {
            AssistMechaItem = AssistMecha.Data.Get(AssistMechaId);
        }
    }
}