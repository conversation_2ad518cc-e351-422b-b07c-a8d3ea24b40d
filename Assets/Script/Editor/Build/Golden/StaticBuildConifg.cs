using System;
using UnityEngine;
using System.Reflection;

public enum EVersionType
{
    Dev,
    Release,
}

public enum ERegionType
{
    Japan,
    Brazil,
}

public enum ECDNType
{
    None,
    EnTest,
    EATest,
    SEATest,
    CNTest,
    CNVXTest,
    CNDBTest,
    QA,
    En,// 欧美
    EA,// 东亚
    SEA,// 东南亚
    CN,// 中国
    CNVX,// 微信
    CNDB,// 抖音
}

public enum EClientType
{
    Dev,
    Designer,
    En,// 欧美
    EA,// 东亚
    SEA,// 东南亚
    CN,// 国服
    CNVX,// 国服
    CNDB,// 国服
}

public enum ESDKType
{
    En,// 欧美
    EA,// 东亚
    SEA,// 东南亚
    CN,// 国服
    CNVX,// 国服
    CNDB,// 国服
}

public enum EArchType
{
    v8,
    all,
}

public enum EiOSPFType
{
    Dev,
    ADHOC,
    APPSTORE,
}

public enum EIconType
{
    Icon_20231207,
    Icon_20231214,
}

public static class StaticBuildConifg
{
    public static string bridge_hassdk_ScriptPath = Application.dataPath.Replace("Assets", "") + "Channel/sdk_script";
    public static string bridge_nosdk_ScriptPath = Application.dataPath.Replace("Assets", "") + "Channel/defaultScript";

    public static string android_en_dev_ScriptPath = Application.dataPath.Replace("Assets", "") + "Channel/onemt_android_en_dev";
    public static string android_en_release_ScriptPath = Application.dataPath.Replace("Assets", "") + "Channel/onemt_android_en_release";
    public static string ios_en_dev_ScriptPath = Application.dataPath.Replace("Assets", "") + "Channel/onemt_ios_en_dev";
    public static string ios_en_release_ScriptPath = Application.dataPath.Replace("Assets", "") + "Channel/onemt_ios_en_release";

    public static string android_ea_dev_ScriptPath = Application.dataPath.Replace("Assets", "") + "Channel/onemt_android_ea_dev";
    public static string android_ea_release_ScriptPath = Application.dataPath.Replace("Assets", "") + "Channel/onemt_android_ea_release";
    public static string ios_ea_dev_ScriptPath = Application.dataPath.Replace("Assets", "") + "Channel/onemt_ios_ea_dev";
    public static string ios_ea_release_ScriptPath = Application.dataPath.Replace("Assets", "") + "Channel/onemt_ios_ea_release";

    public static string android_sea_dev_ScriptPath = Application.dataPath.Replace("Assets", "") + "Channel/onemt_android_sea_dev";
    public static string android_sea_release_ScriptPath = Application.dataPath.Replace("Assets", "") + "Channel/onemt_android_sea_release";
    public static string ios_sea_dev_ScriptPath = Application.dataPath.Replace("Assets", "") + "Channel/onemt_ios_sea_dev";
    public static string ios_sea_release_ScriptPath = Application.dataPath.Replace("Assets", "") + "Channel/onemt_ios_sea_release";
    
    public static string android_en_dev_ScriptPath_huawei = Application.dataPath.Replace("Assets", "") + "Channel/onemt_android_en_dev_huawei";
    public static string android_en_release_ScriptPath_huawei = Application.dataPath.Replace("Assets", "") + "Channel/onemt_android_en_release_huawei";
    
    public static string android_ea_dev_ScriptPath_huawei = Application.dataPath.Replace("Assets", "") + "Channel/onemt_android_ea_dev_huawei";
    public static string android_ea_release_ScriptPath_huawei = Application.dataPath.Replace("Assets", "") + "Channel/onemt_android_ea_release_huawei";

    public static string android_cn_dev_ScriptPath = Application.dataPath.Replace("Assets", "") + "Channel/onemt_android_cn_dev";
    public static string android_cn_release_ScriptPath = Application.dataPath.Replace("Assets", "") + "Channel/onemt_android_cn_release";



    public static string en_adhocFileName = "com_ios_mecharogue_online_adhoc";
    public static string en_appstoreFileName = "com_ios_mecharogue_online_appstore";
    public static string en_test_devFileName = "com_ios_monster_test_adhoc";

    public static string ea_adhocFileName = "com_ios_mecharoguejp_online_adhoc";
    public static string ea_appstoreFileName = "com_ios_mecharoguejp_online_appstore";
    public static string ea_test_devFileName = "com_ios_mecharoguejp_online_test_adhoc";

    public static string sea_adhocFileName = "com_ios_mecharogue_online_adhoc";
    public static string sea_appstoreFileName = "com_ios_mecharogue_online_appstore";
    public static string sea_test_devFileName = "com_ios_monster_test_adhoc";



    public static string cn_adhocFileName = "com_ios_mecharogue_online_adhoc";
    public static string cn_appstoreFileName = "com_ios_mecharogue_online_appstore";
    public static string cn_test_devFileName = "com_ios_monster_test_adhoc";


    // package info
    public static string GameDisplayName = "Mech Assemble";
    public static string GameName_TW = "AngelicCombat：AFK";
    public static string GameName_EN = "AngelicCombat：AFK";

    public static string packageName = "com.and.monstergo.test";
    public static int VersionCode = 1;
    public static string appVersion = "1.0.0";
    public static string resVersion = "0.0.2";

    public static int channelType = 0;

    #region 废弃

    /// <summary>
    /// 连续增量基于版本
    /// </summary>
    /// discard
    public static string resRelativeVersion = string.Empty;

    // discard
    public static bool hasIAP = false;
    // discard

    public static bool isSilentDownload = false;
    public static bool isFullAsset = true;
    public static bool onlyScript = false;

    public static bool clearABCache = false;
    public static bool isTest = false;
    public static bool isSetBundleName = false;
    public static EArchType archType = EArchType.all;
    // 测试正式标签
    public static EVersionType versionType = EVersionType.Dev;
    public static ERegionType region = ERegionType.Japan;
    // 决定包名com.xx.xx
    public static EClientType clientType = EClientType.Dev;

    /// <summary>
    /// 是否网红包。网红包用的单独的服务器列表，和正式服不同服.
    /// </summary>
    public static bool isWangHongPkg = false;

    // platform's setting
    public static bool isAndroidSplite = false;
    public static EiOSPFType iosPF = EiOSPFType.ADHOC;

    // functions
    public static bool collectLackChar = false;

    #endregion

    public static string buildNum = "1";

    // SDK 表示打包 用。可以后面考虑跟use sdk 结合
    public static bool hasSDK = true;

    // 热更 跟整更的标记
    public static bool OTA = false;
    public static bool isProfileMode = false;
    public static EIconType iconType = EIconType.Icon_20231214;

    //决定 控制runsetting 的宏
    public static ECDNType cdnType = ECDNType.None;
    // 拷贝哪个sdk 跟 client 其实用一个就行
    public static ESDKType sdkType = ESDKType.En;

    // set by editor
    public static string profileid = "";
    public static string GetGameName()
    {
        if(StaticBuildConifg.sdkType == ESDKType.En)
        {
            return "Mech Assemble: Zombie Swarm";
        }
        else if(StaticBuildConifg.sdkType == ESDKType.EA)
        {
            return "メカアサルト：ゾンビ対抗メカカスタム";
        }
        return "Mech Assemble: Zombie Swarm";
    }     
    public static string GetGameDisplayName()
    {
        if(sdkType == ESDKType.En)
        {
            return "Mech Assemble";
        }
        else if(sdkType == ESDKType.EA)
        {
            return "メカアサルト";
        }
        return "Mech Assemble";
    }

    //序列化 jenkins 传过来的输
    public static void HandlerArgs()
    {
        string[] args = Environment.GetCommandLineArgs();
        FieldInfo[] fields = typeof(StaticBuildConifg).GetFields(BindingFlags.Public | BindingFlags.Static);
        foreach (var arg in args)
        {
            Debug.Log("---------Build Arg: " + arg);
            foreach (var field in fields)
            {
                if (arg.StartsWith(field.Name + "|") || arg.StartsWith(field.Name + "__"))
                {
                    string result = arg.Substring((field.Name + "|").Length).Trim();
                    if (arg.StartsWith(field.Name + "__"))
                    {
                        result = arg.Substring((field.Name + "__").Length).Trim();
                    }

                    if (field.FieldType == typeof(int))
                    {
                        field.SetValue(null, int.Parse(result));
                    }
                    else if (field.FieldType == typeof(bool))
                    {
                        field.SetValue(null, result == "true");
                    }
                    else if (field.FieldType == typeof(string))
                    {
                        field.SetValue(null, result);
                    }
                    else if (field.FieldType == typeof(float))
                    {
                        field.SetValue(null, float.Parse(result));
                    }
                    else if (field.FieldType.IsEnum)
                    {
                        field.SetValue(null, Enum.Parse(field.FieldType, result));
                    }

                    break;
                }
            }
        }
    }

    public static bool IsDevPackage()
    {
        return packageName.Contains(".test") || packageName.EndsWith(".beta");
    }
}