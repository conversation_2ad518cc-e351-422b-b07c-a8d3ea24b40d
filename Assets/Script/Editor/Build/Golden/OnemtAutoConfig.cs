using UnityEngine;
using UnityEditor;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityEditor.Callbacks;
using System.Xml;
using System;
#if UNITY_IOS
using UnityEditor.iOS.Xcode;
#endif

public class OnemtAutoConfig : Editor
{
    private static string IOSLocalizationPath = Application.dataPath + "/../Channel/Localization/";

    public static string JA_USER_USAGE
    {
        get
        {
            switch (StaticBuildConifg.sdkType)
            {
                case ESDKType.EA:
                    return "IDFAのアクセスを許可することで、お客様のニーズを正確に理解し、カスタマイズされた広告をプッシュして、より安全で高品質のサービスを提供できます。お客様の同意なしに、IDFAを他の目的に使用することはありません。";
                case ESDKType.En:
                    return
                        "By allowing IDFA access, we will provide you with better quality and secure personalized services and content. IDFA will not be used for other purposes without your consent.";
                //case ESDKType.Tw:
                //    return "通過允許IDFA訪問，向您提供更優質、安全的個性服務及內容，未經您的同意，IDFA不會用於其他目的。";
                //case ESDKType.Kr:
                //    return "광고 식별자의 접근을 허용함으로써 더 나은 품질과 안전한 개별 서비스와 콘텐츠를 제공해 드릴 수 있습니다. 광고 식별자는 제공자의 동의 없이 다른 용도로 사용되지 않습니다.";
                default:
                    break;
            }

            return "By allowing IDFA access, we will provide you with better quality and secure personalized services and content. IDFA will not be used for other purposes without your consent.";
        }
    }

    public static string Str_NSMicrophoneUsageDescription
    {
        get
        {
            switch (StaticBuildConifg.sdkType)
            {
                case ESDKType.EA:
                // return "このゲームにマイクの使用は許可されていますか？";
                case ESDKType.En:
                    return "Allow games to use microphones";
                //case ESDKType.Tw:
                //    return "是否允許遊戲使用麥克風";
                //case ESDKType.Kr:
                //    return "게임에서 마이크 사용 허용";
                default:
                    break;
            }

            return "Allow games to use microphones";
        }
    }

    public static string Str_NSCameraUsageDescription
    {
        get
        {
            switch (StaticBuildConifg.sdkType)
            {
                case ESDKType.EA:
                //return "少女廻戦アプリにカメラの使用は許可されていますか？　武将の詳細情報画面にて、武将と一緒に撮影できます。";
                case ESDKType.En:
                    return "Allow games to use cameras";
                //case ESDKType.Tw:
                //    return "是否允許遊戲使用相機";
                //case ESDKType.Kr:
                //    return "'소녀세계'가 카메라를 사용하여 촬영과 녹화를 진행하도록 허용하시겠습니까? 캐릭터 정보 화면에서 캐릭터와 함께 사진을 찍을 수 있습니다.";
                default:
                    break;
            }

            return "Allow games to use cameras";
        }
    }

    public static string Str_NSLocationWhenInUseUsageDescription
    {
        get
        {
            switch (StaticBuildConifg.sdkType)
            {
                case ESDKType.EA:
                //return "ゲームの位置情報へのアクセスを許可するか？";
                case ESDKType.En:
                    return "Allow games to use location";
                //case ESDKType.Tw:
                //    return "是否允許遊戲訪問您的地理位置";
                //case ESDKType.Kr:
                //    return "게임에서 위치 정보 확인 허용";
                default:
                    break;
            }

            return "Allow games to use location";
        }
    }

    public static string Str_NSPhotoLibraryUsageDescription
    {
        get
        {
            switch (StaticBuildConifg.sdkType)
            {
                case ESDKType.EA:
                // return "少女廻戦から「写真」にアクセスしようとしています。権限を許可することでゲーム内の画像を保存することができます。";
                case ESDKType.En:
                    return "Allow games to access your albums";
                //case ESDKType.Tw:
                //    return "是否允許遊戲訪問您的相册";
                //case ESDKType.Kr:
                //    return "게임에서 앨범 접근 허용";
                default:
                    break;
            }

            return "Allow games to access your albums";
        }
    }

    public static string Str_NSPhotoLibraryAddUsageDescription
    {
        get
        {
            switch (StaticBuildConifg.sdkType)
            {
                case ESDKType.EA:
                // return "このアプリにメディアデータベースの使用は許可されていますか？";
                case ESDKType.En:
                    return "";
                //case ESDKType.Tw:
                //    return "";
                //case ESDKType.Kr:
                //    return "'소녀세계'가 앨범에 접근하여 사진의 편집 및 저장을 진행하도록 허용하시겠습니까?";
                default:
                    break;
            }

            return "";
        }
    }

    [PostProcessBuild(1)]
    public static void OnPostprocessBuild(BuildTarget BuildTarget, string path)
    {
        if (BuildTarget == BuildTarget.Android && StaticBuildConifg.hasSDK)
        {
            string scriptPath = StaticBuildConifg.IsDevPackage() ? StaticBuildConifg.android_en_dev_ScriptPath : StaticBuildConifg.android_en_release_ScriptPath;
            if (StaticBuildConifg.sdkType == ESDKType.En)
            {
                scriptPath = StaticBuildConifg.IsDevPackage() ? StaticBuildConifg.android_en_dev_ScriptPath : StaticBuildConifg.android_en_release_ScriptPath;
                if (StaticBuildConifg.channelType == 1)
                {
                    scriptPath = StaticBuildConifg.IsDevPackage() ? StaticBuildConifg.android_en_dev_ScriptPath_huawei : StaticBuildConifg.android_en_release_ScriptPath_huawei;
                }
            }
            else if (StaticBuildConifg.sdkType == ESDKType.EA)
            {
                scriptPath = StaticBuildConifg.IsDevPackage() ? StaticBuildConifg.android_ea_dev_ScriptPath : StaticBuildConifg.android_ea_release_ScriptPath;
                if (StaticBuildConifg.channelType == 1)
                {
                    scriptPath = StaticBuildConifg.IsDevPackage() ? StaticBuildConifg.android_ea_dev_ScriptPath_huawei : StaticBuildConifg.android_ea_release_ScriptPath_huawei;
                }
            }
            else if (StaticBuildConifg.sdkType == ESDKType.SEA)
            {
                scriptPath = StaticBuildConifg.IsDevPackage() ? StaticBuildConifg.android_sea_dev_ScriptPath : StaticBuildConifg.android_sea_release_ScriptPath;
            }
            else if (StaticBuildConifg.sdkType == ESDKType.CN)
            {
                scriptPath = StaticBuildConifg.IsDevPackage() ? StaticBuildConifg.android_cn_dev_ScriptPath : StaticBuildConifg.android_cn_release_ScriptPath;
            }

            CopyChannel(path, scriptPath);

            // modify build gradle info
            ChangeBuildGradle(path);
        }
#if UNITY_IOS
        if (BuildTarget == BuildTarget.iOS)
        {
            //配置xcode Generate, 包括push和apple sign in
            GenerateEntitlementFile(path);

            string projPath = PBXProject.GetPBXProjectPath(path);
            PBXProject proj = new PBXProject();
            proj.ReadFromString(File.ReadAllText(projPath));

            string target = proj.GetUnityMainTargetGuid();
            proj.SetBuildProperty(target, "ENABLE_BITCODE", "NO");

            string plistPath = path + "/Info.plist";
            PlistDocument plist = new PlistDocument();
            plist.ReadFromString(File.ReadAllText(plistPath));
            PlistElementDict rootDict = plist.root;

            // set provisioningProfiles
            PlistElementDict plistPPDic = rootDict.CreateDict("provisioningProfiles");
            plistPPDic.SetString(StaticBuildConifg.packageName, StaticBuildConifg.profileid);
            rootDict.SetString("CFBundleName", StaticBuildConifg.GetGameName());
            rootDict.SetBoolean("UIViewControllerBasedStatusBarAppearance", false);
            rootDict.SetBoolean("com.apple.developer.game-center", true);
            rootDict.SetBoolean("ITSAppUsesNonExemptEncryption", false);


            PlistElementArray plistArrayUIBackgroundModes = rootDict.CreateArray("UIBackgroundModes");
            plistArrayUIBackgroundModes.AddString("remote-notification");
            string scriptPath = StaticBuildConifg.versionType == EVersionType.Dev ? StaticBuildConifg.ios_en_dev_ScriptPath : StaticBuildConifg.ios_en_release_ScriptPath;
            if (StaticBuildConifg.sdkType == ESDKType.En)
            {
                scriptPath = StaticBuildConifg.IsDevPackage() ? StaticBuildConifg.ios_en_dev_ScriptPath : StaticBuildConifg.ios_en_release_ScriptPath;
            }
            else if (StaticBuildConifg.sdkType == ESDKType.EA)
            {
                scriptPath = StaticBuildConifg.IsDevPackage() ? StaticBuildConifg.ios_ea_dev_ScriptPath : StaticBuildConifg.ios_ea_release_ScriptPath;

            }
            else if (StaticBuildConifg.sdkType == ESDKType.SEA)
            {
                scriptPath = StaticBuildConifg.IsDevPackage() ? StaticBuildConifg.ios_sea_dev_ScriptPath : StaticBuildConifg.ios_sea_release_ScriptPath;

            }


            CopyFile(path, scriptPath + "/OptionExportPlist/sh_test_adhoc_OptionExport.plist", "/sh_test_adhoc_OptionExport.plist");
            CopyFile(path, scriptPath + "/OptionExportPlist/sh_adhoc_OptionExport.plist", "/sh_adhoc_OptionExport.plist");
            CopyFile(path, scriptPath + "/OptionExportPlist/sh_appstore_OptionExport.plist", "/sh_appstore_OptionExport.plist");

            CopyDirectory(Application.dataPath.Replace("Assets", "") + "buildscripts/fastlane", path + "/fastlane/");
            if (StaticBuildConifg.hasSDK)
            {
                CopySDKFile(scriptPath, path);

                //获取OMTService-Info.plist
                string omtServicePlistPath = path + "/OMTService-Info.plist";
                PlistDocument servicePlistDocument = new PlistDocument();
                servicePlistDocument.ReadFromString(File.ReadAllText(omtServicePlistPath));
                PlistElementDict sdkConfigInfo = servicePlistDocument.root;
                PlistElementDict appInfoDict = (PlistElementDict)sdkConfigInfo["SDK_AppInfo"];

                //1.3 设置Capability
                string entitlementFilePath = "Unity-iPhone.entitlements";
                string absoluteEntitlementFilePath = path + "/" + entitlementFilePath;
                //添加推送支持
                PlistDocument tempEntitlements = new PlistDocument();
                tempEntitlements.root["aps-environment"] = new PlistElementString("development");

                // 只有美国 支持苹果登录
                //if (StaticBuildConifg.sdkType == ESDKType.En)
                {
                    //添加Apple登录支持
                    var appleSignInArray = new PlistElementArray();
                    appleSignInArray.AddString("Default");
                    tempEntitlements.root["com.apple.developer.applesignin"] = appleSignInArray;
                }


                //添加AssociatedDomains
                var associateDomainArray = new PlistElementArray();
                associateDomainArray.AddString("webcredentials:webcredentials:sdkconfigfile.onemt.co");
                /*string applinks = sdkConfigInfo["SDK_AppInfo"]["AppLinks"].AsString();
                if (!string.IsNullOrEmpty(applinks))
                {
                    associateDomainArray.AddString("applinks:" + applinks);
                }
                else
                {
                    associateDomainArray.AddString("applinks:webcredentials:sdkconfigfile.onemt.co");
                }*/
                tempEntitlements.root["com.apple.developer.associated-domains"] = associateDomainArray;

                // 添加GameCenter
                tempEntitlements.root["com.apple.developer.game-center"] = new PlistElementBoolean(true);

                proj.AddCapability(target, PBXCapabilityType.GameCenter, entitlementFilePath);
                proj.AddCapability(target, PBXCapabilityType.PushNotifications, entitlementFilePath);
                proj.AddCapability(target, PBXCapabilityType.SignInWithApple, entitlementFilePath);
                proj.AddCapability(target, PBXCapabilityType.AssociatedDomains, entitlementFilePath);

                tempEntitlements.WriteToFile(absoluteEntitlementFilePath);
                rootDict.SetString("NSMicrophoneUsageDescription", Str_NSMicrophoneUsageDescription);

                rootDict.SetString("NSCameraUsageDescription", Str_NSCameraUsageDescription);
                rootDict.SetString("NSLocationWhenInUseUsageDescription", Str_NSLocationWhenInUseUsageDescription);
                rootDict.SetString("NSPhotoLibraryUsageDescription", Str_NSPhotoLibraryUsageDescription);
                rootDict.SetString("NSPhotoLibraryAddUsageDescription", Str_NSPhotoLibraryAddUsageDescription);
                rootDict.SetString("NSUserTrackingUsageDescription", JA_USER_USAGE);
                rootDict.SetBoolean("PHPhotoLibraryPreventAutomaticLimitedAccessAlert", true);

                rootDict.SetString("UILaunchStoryboardName", "OMTLaunchScreen");
                rootDict.SetString("UILaunchStoryboardName~ipad", "OMTLaunchScreen");
                rootDict.SetString("UILaunchStoryboardName~iphone", "OMTLaunchScreen");
                rootDict.SetString("UILaunchStoryboardName~ipod", "OMTLaunchScreen");

                string frameTarget = proj.GetUnityFrameworkTargetGuid();
                // firebase
                List<string> inputPaths = new List<string>();
                inputPaths.Add("$(SRCROOT)/$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)");
                inputPaths.Add("${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Resources/DWARF/${TARGET_NAME}");
                proj.AddShellScriptBuildPhase(target, "[SDK]FirebaseCrashlytics", @"/bin/sh", "\"${PODS_ROOT}/FirebaseCrashlytics/run\"", inputPaths);

                // add plist
                var fileGUID = proj.AddFile(path + "/OMTService-Info.plist", "OMTService-Info.plist");
                proj.AddFileToBuild(target, fileGUID);
                fileGUID = proj.AddFile(path + "/GoogleService-Info.plist", "GoogleService-Info.plist");
                proj.AddFileToBuild(target, fileGUID);

                fileGUID = proj.AddFile(path + "/onemt-flutter-sdk-config.json", "onemt-flutter-sdk-config.json");
                proj.AddFileToBuild(target, fileGUID);



                var bridgingFileGuid = proj.AddFile(path + "/Unity-iPhone-Bridging-Header.h", "Unity-iPhone-Bridging-Header.h");
                var swiftFileGuid = proj.AddFile(path + "/Void.swift", "Void.swift");

                var pid = proj.AddFile(path + "/PrivacyInfo.xcprivacy", "PrivacyInfo.xcprivacy");

                //swift混编,添加到Unity-iPhone
                proj.AddFileToBuild(target, swiftFileGuid);
                proj.SetBuildProperty(target, "SWIFT_OBJC_BRIDGING_HEADER", "Unity-iPhone-Bridging-Header.h");
                proj.SetBuildProperty(target, "SWIFT_OBJC_INTERFACE_HEADER_NAME", "ProductName-Swift.h");
                proj.AddBuildProperty(target, "LD_RUNPATH_SEARCH_PATHS", "@executable_path/Frameworks");
                proj.SetBuildProperty(target, "SWIFT_VERSION", "5.0");
                proj.AddBuildProperty(target, "OTHER_LDFLAGS", "$(inherited)");
                proj.AddBuildProperty(target, "OTHER_LDFLAGS", "-ObjC");
                proj.AddBuildProperty(target, "OTHER_LDFLAGS", "-weak_framework");
                proj.AddBuildProperty(target, "OTHER_LDFLAGS", "SwiftUI");

                //添加到UnityFramework
                var targetGuid = proj.GetUnityFrameworkTargetGuid();
                proj.SetBuildProperty(targetGuid, "ENABLE_BITCODE", "NO");
                proj.SetBuildProperty(targetGuid, "CLANG_ENABLE_MODULES", "YES");
                proj.AddFileToBuild(targetGuid, swiftFileGuid);
                proj.SetBuildProperty(targetGuid, "SWIFT_OBJC_BRIDGING_HEADER", "");
                proj.SetBuildProperty(targetGuid, "SWIFT_OBJC_INTERFACE_HEADER_NAME", "UnityFramework-Swift.h");
                proj.AddBuildProperty(targetGuid, "LD_RUNPATH_SEARCH_PATHS", "@executable_path/Frameworks");
                proj.AddBuildProperty(targetGuid, "SWIFT_VERSION", "5.0");
                proj.AddFileToBuild(targetGuid, pid);
                proj.SetBuildProperty(target, "PROVISIONING_PROFILE_SPECIFIER", StaticBuildConifg.profileid);

                string podfilePath = path + "/Podfile";
                string podfileContext = File.ReadAllText(podfilePath);

                PlistElementDict vendorConfigDict = (PlistElementDict)sdkConfigInfo["SDK_Vendor_Config"];

                //1.1 读取FB信息,使用fb+AppId作为Scheme
                PlistElementDict fbConfigDict = (PlistElementDict)vendorConfigDict["Facebook"];
                Debug.Log("AdMob enter Setting111 ");
                //1.8 广告变现
                if (podfileContext.Contains("AdMob"))
                {
                    try
                    {
                        Debug.Log("AdMob enter Setting222 ");
                        var admobConfigDict = (PlistElementDict)vendorConfigDict["Ad"];
                        var admobAppId = admobConfigDict["AdMobAppId"].AsString();
                        rootDict.SetString("GADApplicationIdentifier", admobAppId);

                        //添加SKAdNetworkItems
                        var skaPlistPath = path + "/SKAdNetworkItems.plist";
                        PlistElementArray skaItems = rootDict.CreateArray("SKAdNetworkItems");

                        Debug.Log("Config 111 SKAdNetworkItems");
                        if (File.Exists(skaPlistPath))
                        {
                            Debug.Log("Config 222 SKAdNetworkItems");
                            List<string> skaIds = GetSkAdNetworkItemFromPlist(skaPlistPath);
                            Debug.Log("Config SKAdNetworkItemslength " + skaIds.Count);
                            foreach (var id in skaIds)
                            {
                                Debug.Log("SKAdNetworkItems " + id);
                                skaItems.AddDict().SetString("SKAdNetworkIdentifier", id);
                            }
                        }
                    }
                    catch (Exception e)
                    {
                        Debug.LogError("=============" + e.Message);
                        throw;
                    }
                }
                if (Directory.Exists(IOSLocalizationPath))
                {
                    p_addLocalizationConfig(IOSLocalizationPath, path, proj);
                }

            }
            plist.WriteToFile(plistPath);
            proj.WriteToFile(projPath);
        }
#endif
    }

    static void CopySDKFile(string scriptPath, string path)
    {
        // Copy SKAdNetworkItems
        CopyFile(path, scriptPath + "/SKAdNetworkItems.plist", "/SKAdNetworkItems.plist");
        // Copy OMT plist
        CopyFile(path, scriptPath + "/OMTService-Info.plist", "/OMTService-Info.plist");
        // Notice
        CopyFile(path, scriptPath + "/onemt-flutter-sdk-config.json", "/onemt-flutter-sdk-config.json");

        // Copy Google plist
        CopyFile(path, scriptPath + "/GoogleService-Info.plist", "/GoogleService-Info.plist");

        // Copy changePlist
        CopyFile(path, scriptPath + "/changePlist.sh", "/changePlist.sh");

        // Copy podfile
        CopyFile(path, scriptPath + "/Podfile", "/Podfile");

        // Copy unity bridging header
        CopyFile(path, scriptPath + "/Unity-iPhone-Bridging-Header.h", "/Unity-iPhone-Bridging-Header.h");

        // Copy void
        CopyFile(path, scriptPath + "/Void.swift", "/Void.swift");

        CopyFile(path, scriptPath + "/PrivacyInfo.xcprivacy", "/PrivacyInfo.xcprivacy");

        CopyDirectory(IOSLocalizationPath, path + "/Localization/", true);
    }

    public static void CopyDirectory(string SourcePath, string DestinationPath, bool overwriteexisting = true)
    {
        Debug.Log("1 SourcePath" + SourcePath);
        Debug.Log("2 DestinationPath" + DestinationPath);
        if (Directory.Exists(SourcePath))
        {
            Debug.Log("2 SourcePath" + SourcePath);

            if (Directory.Exists(DestinationPath) == false)
                Directory.CreateDirectory(DestinationPath);

            foreach (string fls in Directory.GetFiles(SourcePath))
            {
                FileInfo flinfo = new FileInfo(fls);
                flinfo.CopyTo(DestinationPath + flinfo.Name, overwriteexisting);
                Debug.Log("1 fls " + fls);
                Debug.Log("1 fls " + DestinationPath + flinfo.Name);
            }

            foreach (string drs in Directory.GetDirectories(SourcePath))
            {
                DirectoryInfo drinfo = new DirectoryInfo(drs);
                CopyDirectory(drs, DestinationPath + drinfo.Name + "/", overwriteexisting);

                Debug.Log("2 drs " + drs);
                Debug.Log("2 drs " + DestinationPath + drinfo.Name);
            }
        }
    }

    static void CopyFile(string path, string srcName, string destName)
    {
        string exportPListPath = path + destName;
        if (File.Exists(srcName))
        {
            using (StreamReader sr = File.OpenText(srcName))
            {
                File.WriteAllText(exportPListPath, sr.ReadToEnd());
            }
        }
        else
        {
            Debug.LogError("CopyFile,没找到要拷贝的文件：" + srcName);
        }
    }

    static void ChangeBuildGradle(string pathToBuiltProject)
    {
        string path = pathToBuiltProject + "/launcher/build.gradle";
        string file = File.ReadAllText(path);
        file = file.Replace("**APPLICATIONID**", StaticBuildConifg.packageName);
        file = file.Replace("**VERSIONCODE**", StaticBuildConifg.VersionCode.ToString());
        file = file.Replace("**VERSIONNAME**", StaticBuildConifg.appVersion);
        File.WriteAllText(path, file);
    }

    public static void CopyChannel(string pathToBuiltProject, string SourcePath)
    {
        foreach (string dirPath in Directory.GetDirectories(SourcePath, "*", SearchOption.AllDirectories))
            Directory.CreateDirectory(dirPath.Replace(SourcePath, pathToBuiltProject));
        foreach (string newPath in Directory.GetFiles(SourcePath, "*.*", SearchOption.AllDirectories))
            File.Copy(newPath, newPath.Replace(SourcePath, pathToBuiltProject), true);
    }
#if UNITY_IOS
    //xcode项目添加generate entitlement file

    private static List<string> GetSkAdNetworkItemFromPlist(string plistPath)
    {
        List<string> ids = new List<string>();
        PlistDocument plistDocument = new PlistDocument();
        plistDocument.ReadFromString(File.ReadAllText(plistPath));
        PlistElementDict rootDict = plistDocument.root;
        PlistElementArray skaItems = rootDict["SKAdNetworkItems"].AsArray();
        Debug.Log("skaItems count + " + skaItems.values.Count);
        foreach (var item in skaItems.values)
        {
            ids.Add(item["SKAdNetworkIdentifier"].AsString());
        }
        return ids;
    }

    static void GenerateEntitlementFile(string path)
    {
        string projectPath = path + "/Unity-iPhone.xcodeproj/project.pbxproj";
        //xxx是项目得名称，一定要修改成对应的项目名称
        ProjectCapabilityManager projectCapabilityManager = new ProjectCapabilityManager(projectPath, "projectx.entitlements", "Unity-iPhone");
        projectCapabilityManager.AddSignInWithApple();
        projectCapabilityManager.AddPushNotifications(true);
        projectCapabilityManager.AddAssociatedDomains(new string[1] { "webcredentials:sdkconfigfile.onemt.co" });
        projectCapabilityManager.WriteToFile();
    }

    private static void p_addLocalizationConfig(string localPath, string buildPath, PBXProject pbxProject)
    {
        //获取xcode项目配置
        if (Directory.Exists(localPath))
        {
            //1. 获取本地化语言数组
            DirectoryInfo localDir = new DirectoryInfo(localPath);
            DirectoryInfo[] subDirs = localDir.GetDirectories();

            if (subDirs.Length == 0)
            {
                return;
            }

            string[] nameArray = new string[subDirs.Length];
            string nameString = "";
            for (int i = 0; i < subDirs.Length; i++)
            {
                DirectoryInfo directoryInfo = subDirs[i];
                string name = directoryInfo.Name;
                nameArray[i] = name;
                if (i == 0)
                {
                    nameString = name;
                }
                else
                {
                    nameString = nameString + " " + name;
                }
            }

            //2. 添加Localization目录下的配置文件到项目中
            string targetGuid = pbxProject.GetUnityMainTargetGuid();
            foreach (string code in nameArray)
            {
                string filePath = "Localization/" + code + "/InfoPlist.strings";
                string fullFilePath = buildPath + "/" + filePath;
                string fileGuid = pbxProject.AddFile(fullFilePath, filePath);
                pbxProject.AddFileToBuild(targetGuid, fileGuid);
            }

            //3. 添加设置脚本
            string forLangStr = "for lang in " + nameString;
            var localScript = string.Join(Environment.NewLine,
                forLangStr,
                "do",
                "   INFO_PLIST_FILE=\"${PROJECT_DIR}/Localization/${lang}/InfoPlist.strings\"",
                "   BUILD_APP_DIR=\"${BUILT_PRODUCTS_DIR}/${FULL_PRODUCT_NAME}\"",
                "   FILE_LOCATION=\"${BUILD_APP_DIR}/${lang}/InfoPlist.strings\" ",
                "   cp \"${INFO_PLIST_FILE}\" \"${FILE_LOCATION}\"",
                "done");

            pbxProject.AddShellScriptBuildPhase(targetGuid, "[SDK]Localize", @"/bin/sh", localScript);

            //string projectPath = PBXProject.GetPBXProjectPath(buildPath);
            //File.WriteAllText(projectPath, pbxProject.WriteToString());
        }
    }
#endif
}