using System;
using UnityEngine;
using UnityEditor;
using System.IO;
using System.Collections.Generic;
using System.Text;
using UnityEditor.SceneManagement;

public class BuildToolWindow : EditorWindow
{
    private static BuildToolWindow s_instance;

    [MenuItem("GameTools/BuildTool")]
    public static void ShowWindow()
    {
        s_instance = GetWindow<BuildToolWindow>();
    }

    static bool buildcopy = false;
    static int type = 0;
    private int platIdx;
    private static string[] platforms = new string[] { "Android", "iOS", "WebGL" };

    void OnGUI()
    {
        EditorGUIUtility.labelWidth = 80;
        DrawBuildTool();

        if (GUILayout.Button("Build", GUILayout.Width(120)))
        {
            if (platIdx == 0)
            {
                // android
                setBasicSettings(1);
            }
            else if (platIdx == 1)
            {
                // ios
                setBasicSettings(2);
            }
            else if (platIdx == 2)
            {
                setBasicSettings(3);
            }
        }
    }

    private string CalPKName()
    {
        string packageName = string.Empty;
        ERegionType selRegion = StaticBuildConifg.region;
        EVersionType selVersion = StaticBuildConifg.versionType;

        if (platIdx == 0)
        {
            if (StaticBuildConifg.clientType == EClientType.En)
            {
                if (selVersion == EVersionType.Dev)
                    packageName = "com.and.monstergo.test";
                else
                    packageName = "com.and.monstergo.online";
            }

            if (StaticBuildConifg.clientType == EClientType.EA)
            {
                if (selVersion == EVersionType.Dev)
                    packageName = "com.and.mecharoguejp.online.test";
                else
                    packageName = "com.and.mecharoguejp.online";
            }
        }
        else if (platIdx == 1)
        {
            // iOS

            if (StaticBuildConifg.clientType == EClientType.En)
            {
                if (selVersion == EVersionType.Dev)
                    packageName = "com.ios.monster.test";
                else
                    packageName = "com.ios.mecharogue.online";
            }

            if (StaticBuildConifg.clientType == EClientType.EA)
            {
                if (selVersion == EVersionType.Dev)
                    packageName = "com.ios.mecharoguejp.online.test";
                else
                    packageName = "com.ios.mecharoguejp.online";
            }
        }
        else if (platIdx == 2)
        {
            // WebGL

            if (StaticBuildConifg.clientType == EClientType.En)
            {
                if (selVersion == EVersionType.Dev)
                    packageName = "com.and.wangpaijishi.online.test";
                else
                    packageName = "com.and.wangpaijishi.online";
            }

            if (StaticBuildConifg.clientType == EClientType.EA)
            {
                if (selVersion == EVersionType.Dev)
                    packageName = "com.and.wangpaijishi.online.test";
                else
                    packageName = "com.and.wangpaijishi.online";
            }
        }

        return packageName;
    }

    void DrawBuildTool()
    {
        EditorGUILayout.BeginVertical();

        // package name
        StaticBuildConifg.packageName = CalPKName();
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField("Package name:", GUILayout.Width(100));
        GUI.enabled = false;
        EditorGUILayout.LabelField(StaticBuildConifg.packageName);
        GUI.enabled = true;
        EditorGUILayout.EndHorizontal();

        // platform
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField("Platform:", GUILayout.Width(100));
        platIdx = EditorGUILayout.Popup(platIdx, platforms, GUILayout.Width(100));
        EditorGUILayout.EndHorizontal();

        // dev or release
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField("Build Type:", GUILayout.Width(100));
        StaticBuildConifg.versionType = (EVersionType)EditorGUILayout.EnumPopup(StaticBuildConifg.versionType, GUILayout.Width(100));
        EditorGUILayout.EndHorizontal();

        // client type
        //EditorGUILayout.BeginHorizontal();
        //EditorGUILayout.LabelField("Client type:", GUILayout.Width(100));
        //StaticBuildConifg.clientType = (EClientType)EditorGUILayout.EnumPopup(StaticBuildConifg.clientType, GUILayout.Width(100));
        //EditorGUILayout.EndHorizontal();

        // csv
        //EditorGUILayout.BeginHorizontal();
        //EditorGUILayout.LabelField("SilentDownload:", GUILayout.Width(100));
        //StaticBuildConifg.isSilentDownload = EditorGUILayout.Toggle(StaticBuildConifg.isSilentDownload, GUILayout.Width(100));
        //EditorGUILayout.EndHorizontal();

        // has sdk
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField("SDK :", GUILayout.Width(100));
        StaticBuildConifg.hasSDK = EditorGUILayout.Toggle(StaticBuildConifg.hasSDK, GUILayout.Width(100));
        EditorGUILayout.EndHorizontal();

        if (StaticBuildConifg.hasSDK)
        {
            // sdk type
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("SDK type:", GUILayout.Width(100));
            StaticBuildConifg.sdkType = (ESDKType)EditorGUILayout.EnumPopup(StaticBuildConifg.sdkType, GUILayout.Width(100));
            EditorGUILayout.EndHorizontal();

            //// has iap
            //EditorGUILayout.BeginHorizontal();
            //EditorGUILayout.LabelField("IAP :", GUILayout.Width(100));
            //StaticBuildConifg.hasIAP = EditorGUILayout.Toggle(StaticBuildConifg.hasIAP, GUILayout.Width(100));
            //EditorGUILayout.EndHorizontal();
        }

        // version code
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField("Bundle code:", GUILayout.Width(100));
        StaticBuildConifg.VersionCode = EditorGUILayout.IntField(StaticBuildConifg.VersionCode, GUILayout.Width(150));

        EditorGUILayout.LabelField("App version:", GUILayout.Width(100));
        StaticBuildConifg.appVersion = EditorGUILayout.TextField(StaticBuildConifg.appVersion, GUILayout.Width(150));

        EditorGUILayout.LabelField("Res version:", GUILayout.Width(100));
        StaticBuildConifg.resVersion = EditorGUILayout.TextField(StaticBuildConifg.resVersion, GUILayout.Width(150));
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.EndVertical();

        //StaticBuildConifg.isFullAsset = true;
    }

    static void setBasicSettings(int type)
    {
        //最终 被sdk 文件里的 androidmanefest.xml 冲掉了，这个设置其实没用。
        PlayerSettings.productName = StaticBuildConifg.GetGameDisplayName();
        Debug.Log("StaticBuildConifg.sdkType:" + StaticBuildConifg.sdkType);
        //bool need = false;
        Versions.NewVer = StaticBuildConifg.resVersion;
        Versions.isFullAsset = StaticBuildConifg.isFullAsset;
        //WriteVersionTxt(type);
        //if (StaticBuildConifg.OTA)
        //{
        //    // 打bundle 
        //    BuildBundleAndPackage();
        //}
        //else
        {
            //CopyFile(Application.dataPath + "/Scripts/Main/SDK/", StaticBuildConifg.bridge_hassdk_ScriptPath);
            EditorSceneManager.OpenScene("Assets/ResUpdate/Scene/LogoScene.unity");
            AssetDatabase.Refresh();

            BuildBundleAndPackage();
            if (!StaticBuildConifg.OTA)
            {
                if (StaticBuildConifg.hasSDK)
                {
                    if (type == 1)
                    {
                        BuildChannelForAndroid();
                    }
                    else if (type == 2)
                    {
                        BuildChannelForIOS();
                    }
                    else if (type == 3)
                    {
                        BuildChannelForWebGL();
                    }
                }
                else
                {
                    if (type == 1)
                    {
                        BuildForAndroid();
                    }
                    else if (type == 2)
                    {
                        BuildForIOS();
                    }
                    else if (type == 3)
                    {
                        BuildChannelForWebGL();
                    }
                }
            }
        }
    }

    public static void BuildForAndroid()
    {
        string apiPath = GetAPKFormalPath();
        PlayerSettings.SetScriptingBackend(BuildTargetGroup.Android, ScriptingImplementation.IL2CPP);
        PlayerSettings.Android.targetArchitectures = AndroidArchitecture.ARM64 | AndroidArchitecture.ARMv7;
        PlayerSettings.bundleVersion = StaticBuildConifg.appVersion;
        PlayerSettings.Android.bundleVersionCode = StaticBuildConifg.VersionCode;
        PlayerSettings.SetApplicationIdentifier(BuildTargetGroup.Android, StaticBuildConifg.packageName);
        SetMacro(BuildTargetGroup.Android);

        SetIcon();
        EditorUserBuildSettings.exportAsGoogleAndroidProject = false;
        BuildOptions option = BuildOptions.None;
        if (StaticBuildConifg.isProfileMode)
        {
            option = BuildOptions.Development;
            option |= BuildOptions.ConnectWithProfiler;
            option |= BuildOptions.EnableDeepProfilingSupport;
            option |= BuildOptions.AllowDebugging;

            PlayerSettings.enableInternalProfiler = true;
        }
        else
        {
            PlayerSettings.enableInternalProfiler = false;
        }

        BuildPipeline.BuildPlayer(GetBuildScenes(), apiPath, BuildTarget.Android, option);
    }

    static void BuildChannelForAndroid()
    {
        AssetDatabase.Refresh();
        PlayerSettings.SetScriptingBackend(BuildTargetGroup.Android, ScriptingImplementation.IL2CPP);
        PlayerSettings.Android.targetArchitectures = AndroidArchitecture.ARM64 | AndroidArchitecture.ARMv7;
        PlayerSettings.bundleVersion = StaticBuildConifg.appVersion;
        PlayerSettings.Android.bundleVersionCode = StaticBuildConifg.VersionCode;
        PlayerSettings.SetApplicationIdentifier(BuildTargetGroup.Android, StaticBuildConifg.packageName);
        SetMacro(BuildTargetGroup.Android);
        SetIcon();
        string apiPath = GetOnemtExportPath();
        EditorUserBuildSettings.exportAsGoogleAndroidProject = true;
        BuildOptions option = BuildOptions.None;
        if (StaticBuildConifg.isProfileMode)
        {
            option = BuildOptions.Development;
            option |= BuildOptions.ConnectWithProfiler;
            option |= BuildOptions.EnableDeepProfilingSupport;
            option |= BuildOptions.AllowDebugging;

            PlayerSettings.enableInternalProfiler = true;
        }
        else
        {
            PlayerSettings.enableInternalProfiler = false;
        }

        BuildPipeline.BuildPlayer(GetBuildScenes(), apiPath, BuildTarget.Android, option);

        Debug.Log("Build success. Path is : " + apiPath);
    }

    static void BuildChannelForIOS()
    {
        string scriptPath;

        if (StaticBuildConifg.sdkType == ESDKType.En)
        {
            scriptPath = StaticBuildConifg.IsDevPackage() ? StaticBuildConifg.ios_en_dev_ScriptPath : StaticBuildConifg.ios_en_release_ScriptPath;
        }

        //CopyFile(Application.dataPath + "/3rd/", StaticBuildConifg.bridge_hassdk_ScriptPath);
        //CopyFile(Application.dataPath + "/Plugins/iOS/", scriptPath + "/iOS");
        AssetDatabase.Refresh();
        string apiPath = GetXcodePath();
        PlayerSettings.iOS.appleEnableAutomaticSigning = false;
        StaticBuildConifg.profileid = GetProfileId();
        PlayerSettings.iOS.appleDeveloperTeamID = "78JLJXWDZJ";
        PlayerSettings.iOS.iOSManualProvisioningProfileID = GetProfileId();
        PlayerSettings.iOS.iOSManualProvisioningProfileType = ProvisioningProfileType.Distribution;
        PlayerSettings.bundleVersion = StaticBuildConifg.appVersion;
        PlayerSettings.iOS.buildNumber = StaticBuildConifg.VersionCode.ToString();
        PlayerSettings.SetApplicationIdentifier(BuildTargetGroup.iOS, StaticBuildConifg.packageName);
        SetMacro(BuildTargetGroup.iOS);
        SetIcon();
        BuildOptions option = BuildOptions.None;
        if (StaticBuildConifg.isProfileMode)
        {
            option = BuildOptions.Development;
            option |= BuildOptions.ConnectWithProfiler;
            option |= BuildOptions.EnableDeepProfilingSupport;
            option |= BuildOptions.AllowDebugging;

            PlayerSettings.enableInternalProfiler = true;
        }
        else
        {
            PlayerSettings.enableInternalProfiler = false;
        }

        BuildPipeline.BuildPlayer(GetBuildScenes(), apiPath, BuildTarget.iOS, option);
    }

    static void BuildChannelForWebGL()
    {
        string scriptPath;

        if (StaticBuildConifg.sdkType == ESDKType.En)
        {
            scriptPath = StaticBuildConifg.IsDevPackage() ? StaticBuildConifg.ios_en_dev_ScriptPath : StaticBuildConifg.ios_en_release_ScriptPath;
        }

        //CopyFile(Application.dataPath + "/3rd/", StaticBuildConifg.bridge_hassdk_ScriptPath);
        //CopyFile(Application.dataPath + "/Plugins/iOS/", scriptPath + "/iOS");
        AssetDatabase.Refresh();
        string apiPath = GetWebGLPath();
        StaticBuildConifg.profileid = GetProfileId();
        PlayerSettings.bundleVersion = StaticBuildConifg.appVersion;
        PlayerSettings.SetApplicationIdentifier(BuildTargetGroup.WebGL, StaticBuildConifg.packageName);
        SetMacro(BuildTargetGroup.WebGL);
        SetIcon();
        BuildOptions option = BuildOptions.None;
        if (StaticBuildConifg.isProfileMode)
        {
            option = BuildOptions.Development;
            option |= BuildOptions.ConnectWithProfiler;
            option |= BuildOptions.EnableDeepProfilingSupport;
            option |= BuildOptions.AllowDebugging;

            PlayerSettings.enableInternalProfiler = true;
        }
        else
        {
            PlayerSettings.enableInternalProfiler = false;
        }

        BuildPipeline.BuildPlayer(GetBuildScenes(), apiPath, BuildTarget.WebGL, option);
    }

    static string GetProfileId()
    {
        string profileid = string.Empty;
        if (StaticBuildConifg.sdkType == ESDKType.En)
        {
            if (StaticBuildConifg.IsDevPackage())
            {
                profileid = StaticBuildConifg.en_test_devFileName;
            }
            else
            {
                profileid = StaticBuildConifg.en_adhocFileName;
            }
        }
        else if (StaticBuildConifg.sdkType == ESDKType.EA)
        {
            if (StaticBuildConifg.IsDevPackage())
            {
                profileid = StaticBuildConifg.ea_test_devFileName;
            }
            else
            {
                profileid = StaticBuildConifg.ea_adhocFileName;
            }
        }
        else if (StaticBuildConifg.sdkType == ESDKType.SEA)
        {
            if (StaticBuildConifg.IsDevPackage())
            {
                profileid = StaticBuildConifg.sea_test_devFileName;
            }
            else
            {
                profileid = StaticBuildConifg.sea_adhocFileName;
            }
        }       
        else if (StaticBuildConifg.sdkType == ESDKType.CN)
        {
            if (StaticBuildConifg.IsDevPackage())
            {
                profileid = StaticBuildConifg.cn_test_devFileName;
            }
            else
            {
                profileid = StaticBuildConifg.cn_adhocFileName;
            }
        }

        return profileid;
    }

    static void BuildForIOS()
    {
        string apiPath = GetXcodePath();
        PlayerSettings.iOS.appleEnableAutomaticSigning = false;

        StaticBuildConifg.profileid = GetProfileId();
        PlayerSettings.iOS.appleDeveloperTeamID = "78JLJXWDZJ";
        PlayerSettings.iOS.iOSManualProvisioningProfileID = GetProfileId();
        PlayerSettings.iOS.iOSManualProvisioningProfileType = ProvisioningProfileType.Distribution;
        PlayerSettings.bundleVersion = StaticBuildConifg.appVersion;
        PlayerSettings.iOS.buildNumber = StaticBuildConifg.VersionCode.ToString();
        PlayerSettings.SetApplicationIdentifier(BuildTargetGroup.iOS, StaticBuildConifg.packageName);
        SetMacro(BuildTargetGroup.iOS);
        SetIcon();
        BuildOptions option = BuildOptions.None;
        if (StaticBuildConifg.isProfileMode)
        {
            option = BuildOptions.Development;
            option |= BuildOptions.ConnectWithProfiler;
            option |= BuildOptions.EnableDeepProfilingSupport;
            option |= BuildOptions.AllowDebugging;

            PlayerSettings.enableInternalProfiler = true;
        }
        else
        {
            PlayerSettings.enableInternalProfiler = false;
        }

        BuildPipeline.BuildPlayer(GetBuildScenes(), apiPath, BuildTarget.iOS, option);
    }

    public static void JenkinsTest()
    {
        Debug.Log("+++++++++++++++++++++++++++++++++jenkins+++++++++++++++++++++++");
    }

    public static void AutoAndroid()
    {
        StaticBuildConifg.HandlerArgs();
        setBasicSettings(1);
    }

    public static void AutoAndroid2()
    {
        Debug.Log("========================auto android2=========================");
    }

    public static void AutoIOS()
    {
        StaticBuildConifg.HandlerArgs();
        setBasicSettings(2);
    }

    public static void AutoWebGL()
    {
        StaticBuildConifg.HandlerArgs();
        setBasicSettings(3);
    }

    public static void BuildBundleAndPackage()
    {
        /////=----------
        // 打整包 
        Debug.Log("========================生成XLUA代码=========================");
        //XLua.DelegateBridge.Gen_Flag = true;
        //Generator.ClearAll();
        //Generator.GenAll();
        Debug.Log("========================生成XLUA代码结束=========================");
        //HybriCLRTools.AOTGenerateAll();
        Debug.Log("========================开始打包Bundle=========================");

        BuildTools.BuildAllAssetsN();
        //AssetGraphEditorWindow.RunInBuild();
        Debug.Log("========================Bundle打包结束=========================");

        AssetDatabase.Refresh();
    }

    public static void SetIcon()
    {
        string iconPath = null;
        if (StaticBuildConifg.iconType == EIconType.Icon_20231207)
        {
            iconPath = "Assets/Art/icon/Icon_20231207/";
        }
        else if (StaticBuildConifg.iconType == EIconType.Icon_20231214)
        {
            iconPath = "Assets/Art/icon/Icon_20231214/";
        }
#if UNITY_ANDROID
        PlatformIconKind[] piks = PlayerSettings.GetSupportedIconKindsForPlatform(BuildTargetGroup.Android);

        foreach (var pik in piks)
        {
            PlatformIcon[] icons = PlayerSettings.GetPlatformIcons(BuildTargetGroup.Android, pik);

            foreach (var item in icons)
            {
                for (int i = 0; i < item.maxLayerCount; i++)
                {
                    string prefix = string.Empty;
                    if (pik.ToString() == "Adaptive (API 26)")
                    {
                        if (i == 0)
                        {
                            prefix = "-1";
                        }
                    }

                    Texture2D tex = AssetDatabase.LoadAssetAtPath<Texture2D>(iconPath + "icon" + item.height + prefix + ".png");
                    item.SetTexture(tex, i);
                }
            }

            PlayerSettings.SetPlatformIcons(BuildTargetGroup.Android, pik, icons);
        }

        // var push48 = Application.dataPath.Replace("Assets", "") + iconPath + "push48.png";
        // File.Copy(push48, Application.dataPath + "/icon/push48.png", true);

        //var push192 = Application.dataPath.Replace("Assets", "") + iconPath + "icon192.png";
        //  File.Copy(push192, Application.dataPath + "/icon/icon192.png", true);
#elif UNITY_IOS
#endif

        // set default icon
        Texture2D defTex = AssetDatabase.LoadAssetAtPath<Texture2D>(iconPath + "icon192.png");
        PlayerSettings.SetIconsForTargetGroup(BuildTargetGroup.Unknown, new Texture2D[] { defTex });
    }

    static void SetMacro(BuildTargetGroup btg)
    {
        string def = PlayerSettings.GetScriptingDefineSymbolsForGroup(btg);
        List<string> defList = new List<string>(def.Split(';'));

        //DefMacro(StaticBuildConifg.hasIAP, "ONEMT_OPENIAP", defList);
        //DefMacro(StaticBuildConifg.versionType == EVersionType.Dev, "ONEMT_DEV", defList);
        //DefMacro(StaticBuildConifg.collectLackChar, "COLLECT_CHAR", defList);

        // cdn
        //DefMacro(StaticBuildConifg.cdnType == ECDNType.None, "ONEMT_CDN_NONE", defList);
        //DefMacro(StaticBuildConifg.cdnType == ECDNType.QA, "ONEMT_CDN_QA", defList);
        DefMacro(StaticBuildConifg.cdnType == ECDNType.En ||
                 StaticBuildConifg.cdnType == ECDNType.EA || StaticBuildConifg.cdnType == ECDNType.SEA
            , "ONEMT_CDN_RELEASE", defList);       
        
        DefMacro(StaticBuildConifg.cdnType == ECDNType.CN,"ONEMT_CDN_CN_RELEASE", defList);

        DefMacro(StaticBuildConifg.cdnType == ECDNType.En, "ONEMT_CDN_EN", defList);
        DefMacro(StaticBuildConifg.cdnType == ECDNType.EA, "ONEMT_CDN_EA", defList);
        DefMacro(StaticBuildConifg.cdnType == ECDNType.SEA, "ONEMT_CDN_SEA", defList);
        DefMacro(StaticBuildConifg.cdnType == ECDNType.CN, "ONEMT_CDN_CN", defList);
        DefMacro(StaticBuildConifg.cdnType == ECDNType.CNVX, "ONEMT_CDN_CNVX", defList);
        DefMacro(StaticBuildConifg.cdnType == ECDNType.CNDB, "ONEMT_CDN_CNDB", defList);

        DefMacro(StaticBuildConifg.cdnType == ECDNType.SEATest, "ONEMT_CDN_SEA_TEST", defList);
        DefMacro(StaticBuildConifg.cdnType == ECDNType.EATest, "ONEMT_CDN_EA_TEST", defList);
        DefMacro(StaticBuildConifg.cdnType == ECDNType.EnTest, "ONEMT_CDN_EN_TEST", defList);
        DefMacro(StaticBuildConifg.cdnType == ECDNType.CNTest, "ONEMT_CDN_CN_TEST", defList);
        DefMacro(StaticBuildConifg.cdnType == ECDNType.CNVXTest, "ONEMT_CDN_CNVX_TEST", defList);
        DefMacro(StaticBuildConifg.cdnType == ECDNType.CNDBTest, "ONEMT_CDN_CNDB_TEST", defList);
        // 华为标签 
        DefMacro(StaticBuildConifg.channelType == 1, "ONEMT_HUAWEI", defList);

        //DefMacro(StaticBuildConifg.cdnType == ECDNType.JapanTest, "ONEMT_CDN_JAPAN_TEST", defList);
        //DefMacro(StaticBuildConifg.cdnType == ECDNType.EnTest, "ONEMT_CDN_BRAZIL_TEST", defList);
        //DefMacro(StaticBuildConifg.cdnType == ECDNType.TwTest, "ONEMT_CDN_TW_TEST", defList);
        //DefMacro(StaticBuildConifg.cdnType == ECDNType.KrTest, "ONEMT_CDN_KR_TEST", defList);

        // client
        //DefMacro(StaticBuildConifg.clientType == EClientType.Dev, "ONEMT_CLIENT_DEV", defList);
        //DefMacro(StaticBuildConifg.clientType == EClientType.En, "ONEMT_CLIENT_EN", defList);
        //DefMacro(StaticBuildConifg.clientType == EClientType.Japan, "ONEMT_CLIENT_JAPAN", defList);
        //DefMacro(StaticBuildConifg.clientType == EClientType.Tw, "ONEMT_CLIENT_TW", defList);
        //DefMacro(StaticBuildConifg.clientType == EClientType.Kr, "ONEMT_CLIENT_KR", defList);

        //DefMacro(StaticBuildConifg.isWangHongPkg, "ONEMT_CLIENT_WangHong", defList);

        //DefMacro(StaticBuildConifg.region == ERegionType.Brazil, "ONEMT_REGION_BRAZIL", defList);
        //DefMacro(StaticBuildConifg.region == ERegionType.Japan, "ONEMT_REGION_JAPAN", defList);

        //DefMacro(StaticBuildConifg.isTest, "ONEMT_TEST", defList);

        //DefMacro(StaticBuildConifg.clientSubType == EClientSubType.Kr_Onestore, "KR_ONESTORE", defList);

        PlayerSettings.SetScriptingDefineSymbolsForGroup(btg, string.Join(";", defList.ToArray()));
    }

    static void DefMacro(bool isDef, string name, List<string> defList)
    {
        defList.Remove(name);
        if (isDef)
            defList.Add(name);
    }

    public static string[] GetBuildScenes()
    {
        List<string> allScenes = new List<string>();
        foreach (EditorBuildSettingsScene scene in EditorBuildSettings.scenes)
        {
            if (!scene.enabled) continue;
            allScenes.Add(scene.path);
        }

        return allScenes.ToArray();
    }

    public static string GetAPKFormalPath()
    {
        string version = PlayerSettings.bundleVersion + "_" + PlayerSettings.Android.bundleVersionCode;
        string apkName = "android/" + "android_" + StaticBuildConifg.buildNum + ".apk";
        string apkPath = Path.Combine(Application.dataPath, "../") + apkName;
        return apkPath;
    }

    public static string GetOnemtExportPath()
    {
        string apkName = "android/" + "project_Android_" + StaticBuildConifg.buildNum;
        string apkPath = Path.Combine(Application.dataPath, "../") + apkName;
        return apkPath;
    }

    public static string GetXcodePath()
    {
        string apkName = "ios/project_Xcode_" + StaticBuildConifg.buildNum;
        string apkPath = Application.dataPath.Replace("Assets", apkName);
        return apkPath;
    }

    public static string GetWebGLPath()
    {
        string apkName = "WebGL/project_WebGL_" + StaticBuildConifg.buildNum;
        string apkPath = Application.dataPath.Replace("Assets", apkName);
        return apkPath;
    }


    static void CopyFile(string pathToBuiltProject, string SourcePath)
    {
        foreach (string dirPath in Directory.GetDirectories(SourcePath, "*", SearchOption.AllDirectories))
            Directory.CreateDirectory(dirPath.Replace(SourcePath, pathToBuiltProject));
        foreach (string newPath in Directory.GetFiles(SourcePath, "*.*", SearchOption.AllDirectories))
            File.Copy(newPath, newPath.Replace(SourcePath, pathToBuiltProject), true);
    }


    /// <summary>
    /// 保存jenkins需要的打包批次名
    /// </summary>
    /// <param name="type"></param>
    public static void WriteVersionTxt(int type)
    {
        string outputPath = String.Empty;
        if (type == 1)
        {
            string platformName = "android";
            outputPath = Application.dataPath.Replace("Assets", platformName);
        }
        else
        {
            string platformName = "ios";
            outputPath = Application.dataPath.Replace("Assets", platformName);
        }

        if (!Directory.Exists(outputPath))
        {
            Directory.CreateDirectory(outputPath);
        }

        string versionTxtPath = outputPath + "/version.txt";

        if (File.Exists(versionTxtPath))
        {
            File.Delete(versionTxtPath);
        }

        using (StreamWriter writer = new StreamWriter(versionTxtPath, true, Encoding.UTF8))
        {
            string text = String.Format("#{0}-{1}-{2}", StaticBuildConifg.buildNum, StaticBuildConifg.resVersion, StaticBuildConifg.VersionCode);
            writer.WriteLine(text);
            Debug.Log("本次打包批次名为: " + text);
        }
    }
}