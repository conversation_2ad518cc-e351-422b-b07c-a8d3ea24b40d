using System;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;

/// <summary>
/// UI预制版本检查面板
/// </summary>
public class UIVersionWindow : EditorWindow
{
    static UIVersionWindow _window;
    string VersionPath;
    string[] SubPaths;
    int SubIndex;
    List<VersionFile> verFiles = new List<VersionFile>();//版本文件
    int SelectIndex;
    Vector2 scrollPos;

    const int BTN_WIDTH = 80;
    const string PATH_KEY = "VersionPath";
    const string VERSION_NUM = "VersionNum";

    public static void ShowWindow()
    {
        _window = GetWindow(typeof(UIVersionWindow), true, "UI预制版本检查面板") as UIVersionWindow;
        _window.Init();
        _window.Show();
    }

    public void Init()
    {
        VersionPath = EditorPrefs.GetString(PATH_KEY);
        ResetSub();
    }

    void ResetSub()
    {
        SubPaths = null;
        if (VersionPath.Length > 0 && Directory.Exists(VersionPath))
        {
            SubPaths = Directory.GetDirectories(VersionPath);
            if (SubPaths.Length > 0 && SubIndex == -1) SubIndex = 0;
        }
    }

    void OnGUI()
    {
        DrawButtons();
        DrawItems();
    }

    void DrawButtons()
    {
        InspectorHelper.BeginGroup(10);

        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField("版本目录：", GUILayout.MaxWidth(BTN_WIDTH));
        EditorGUILayout.TextField(VersionPath);
        if (GUILayout.Button("选择目录", EditorStylesExt.btnLeft, GUILayout.MaxWidth(BTN_WIDTH)))
        {
            string path = EditorUtility.OpenFolderPanel("选择版本根目录", (VersionPath.Length > 0 ? VersionPath : Application.dataPath), "");
            if (path.Length > 0)
            {
                VersionPath = path;
                EditorPrefs.SetString(PATH_KEY, VersionPath);
                ResetSub();
            }
        }
        GUI.backgroundColor = Color.green;
        if (GUILayout.Button("开始检查", EditorStylesExt.btnRight, GUILayout.MaxWidth(BTN_WIDTH))) CheckVersion();
        GUI.backgroundColor = Color.white;
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField("选择版本：", GUILayout.MaxWidth(BTN_WIDTH));
        if (SubPaths != null) SubIndex = EditorGUILayout.Popup(SubIndex, SubPaths);
        else EditorGUILayout.LabelField("没有任何版本信息");
        if (GUILayout.Button("生成版本", EditorStylesExt.btnLeft, GUILayout.MaxWidth(BTN_WIDTH))) CreateSubVersion();
        if (GUILayout.Button("删除版本", EditorStylesExt.btnMid, GUILayout.MaxWidth(BTN_WIDTH))) DeleteVersion();
        if (GUILayout.Button("清空版本", EditorStylesExt.btnRight, GUILayout.MaxWidth(BTN_WIDTH))) ClearVersion();
        EditorGUILayout.EndHorizontal();
        InspectorHelper.EndGroup();
    }

    void DrawItems()
    {
        scrollPos = GUILayout.BeginScrollView(scrollPos, GUILayout.ExpandHeight(true));
        InspectorHelper.BeginGroup(10);
        if (verFiles.Count > 0)
        {
            for (int i = 0; i < verFiles.Count; i++)
            {
                VersionFile file = verFiles[i];
                GUI.backgroundColor = Color.white;

                GUILayout.BeginHorizontal(i == SelectIndex ? EditorStylesExt.createRect : EditorStylesExt.helpBox, GUILayout.Height(30));
                GUILayout.Label((i + 1).ToString("000"), GUILayout.Width(30));
                GUILayout.Label(file.GetFileName(), i == SelectIndex ? EditorStylesExt.createRect : EditorStylesExt.tooltip, GUILayout.ExpandWidth(true));
                GUI.backgroundColor = file.GetColor();
                if (GUILayout.Button(file.State.ToString(), EditorStylesExt.largeButton, GUILayout.MaxWidth(BTN_WIDTH)))
                {
                    SelectIndex = i;
                    if (file.State == VersionState.Update || file.State == VersionState.New)
                    {
                        GameObject target = AssetDatabase.LoadAssetAtPath<GameObject>(UIInternationEditor.GetAssetPath(file.DestFile));
                        if (target)
                            Selection.activeGameObject = target;
                    }
                    else EditorUtility.RevealInFinder(file.SourceFile);
                }
                GUILayout.EndHorizontal();
            }
            GUI.backgroundColor = Color.white;
        }
        else GUILayout.Label("无数据", EditorStylesExt.helpBox);
        InspectorHelper.EndGroup();
        CallMenu();
        GUILayout.EndScrollView();
    }

    void CallMenu()
    {
        Event curEvent = Event.current;
        if (curEvent.type == EventType.ContextClick)//contextRect.Contains(curEvent.mousePosition)
        {
            GenericMenu menu = new GenericMenu();
            menu.AddItem(new GUIContent("导出到Txt"), false, ExportData);
            menu.AddItem(new GUIContent("清空数据"), false, ClearData);
            menu.AddSeparator(string.Empty);
            menu.ShowAsContext();
            curEvent.Use();
        }
    }

    void ClearData()
    {
        verFiles.Clear();
    }

    void ExportData()
    {
        string path = EditorUtility.OpenFilePanel("导出数据", Application.dataPath, "txt");
        if (path.Length > 0)
        {
            FileStream fs = null;
            StreamWriter sw = null;
            try
            {
                fs = new FileStream(path, FileMode.Create);
                sw = new StreamWriter(fs);
                for (int i = 0; i < verFiles.Count; i++)
                    sw.WriteLine(verFiles[i]);
                sw.Flush();
                Debug.Log("文件导出成功:" + path);
            }
            catch (Exception ex)
            {
                Debug.LogError(ex.Message);
            }
            finally
            {
                if (sw != null) sw.Close();
                if (fs != null) fs.Close();
            }
        }
    }

    void CreateSubVersion()
    {
        if (!EditorPrefs.HasKey(VERSION_NUM)) EditorPrefs.SetInt(VERSION_NUM, 1);
        int num = EditorPrefs.GetInt(VERSION_NUM);
        EditorPrefs.SetInt(VERSION_NUM, num + 1);

        string rootPath = Application.dataPath + "/Data/ui/panel";
        string newPath = string.Format("{0}/{1:0000}/", VersionPath, num);
        if (Directory.Exists(rootPath))
        {
            if (!Directory.Exists(newPath)) Directory.CreateDirectory(newPath);
            string[] files = Directory.GetFiles(rootPath, "*.prefab", SearchOption.AllDirectories);
            for (int i = 0, count = files.Length; i < count; i++)
            {
                string fileName = Path.GetFileName(files[i]);
                string dirPath = Path.GetDirectoryName(files[i]);
                string dirName = dirPath.Substring(dirPath.IndexOf('\\') + 1);
                dirPath = newPath + dirName;
                if (!Directory.Exists(dirPath)) Directory.CreateDirectory(dirPath);
                File.Copy(files[i], dirPath + "/" + fileName);
                EditorUtility.DisplayProgressBar("生成预制", string.Format("生成预制[{0}/{1}]>>{2}", i + 1, count, fileName), (float)(i + 1) / (float)count);
            }
            ShowNotification(new GUIContent(string.Format("生成{0}个预制文件", files.Length)));
        }
        EditorUtility.ClearProgressBar();
        ResetSub();
    }

    void CheckVersion()
    {
        verFiles.Clear();
        SelectIndex = -1;
        string rootPath = Application.dataPath + "/Data/ui/panel";
        string newPath = SubPaths[SubIndex];
        List<string> subFiles = new List<string>();
        if (Directory.Exists(rootPath))
        {
            subFiles.AddRange(Directory.GetFiles(newPath, "*.prefab", SearchOption.AllDirectories));
            string[] files = Directory.GetFiles(rootPath, "*.prefab", SearchOption.AllDirectories);
            for (int i = 0, count = files.Length; i < count; i++)
            {
                string fileName = Path.GetFileName(files[i]);
                string dirPath = Path.GetDirectoryName(files[i]);
                string dirName = dirPath.Substring(dirPath.IndexOf('\\'));
                dirPath = dirName + "\\" + fileName;

                bool exist = false;
                for (int j = 0, count2 = subFiles.Count; j < count2; j++)
                {
                    if (subFiles[j].EndsWith(dirPath))
                    {
                        if (CheckChange(files[i], subFiles[j]))
                            verFiles.Add(new VersionFile(subFiles[j], files[i], VersionState.Update));
                        exist = true;
                        subFiles.RemoveAt(j);
                        break;
                    }
                }
                if (!exist)
                    verFiles.Add(new VersionFile(null, files[i], VersionState.New));
                EditorUtility.DisplayProgressBar("检查", string.Format("检查[{0}/{1}]>>{2}", i + 1, count, fileName), (float)(i + 1) / (float)count);
            }

            for (int i = subFiles.Count - 1; i >= 0; i--)
                verFiles.Add(new VersionFile(subFiles[i], null, VersionState.Delete));
        }
        EditorUtility.ClearProgressBar();
        ShowNotification(new GUIContent(verFiles.Count > 0 ? string.Format("检查出{0}个文件", verFiles.Count) : "没有任何改动文件"));
    }

    bool CheckChange(string path, string pathSource)
    {
        bool isChange = false;
        BinaryReader reader = null;
        BinaryReader readerOrigin = null;
        try
        {
            reader = new BinaryReader(new FileStream(path, FileMode.Open));
            readerOrigin = new BinaryReader(new FileStream(pathSource, FileMode.Open));
            if (reader.BaseStream.Length == readerOrigin.BaseStream.Length)
            {
                while (reader.BaseStream.Position < reader.BaseStream.Length)
                {
                    if (reader.ReadByte() != readerOrigin.ReadByte())
                    {
                        isChange = true;
                        break;
                    }
                }
            }
            else isChange = true;
        }
        catch (Exception)
        {
            Debug.LogError(path);
        }
        finally
        {
            if (reader != null) reader.Close();
            if (readerOrigin != null) readerOrigin.Close();
        }
        return isChange;
    }

    void DeleteVersion()
    {
        string path = SubPaths[SubIndex];
        if (Directory.Exists(path))
        {
            Directory.Delete(path, true);
            ResetSub();
            ShowNotification(new GUIContent(string.Format("删除版本：{0}", path)));
        }
    }

    void ClearVersion()
    {
        for (int i = SubPaths.Length - 1; i >= 0; i--)
        {
            string path = SubPaths[i];
            if (Directory.Exists(path)) Directory.Delete(path, true);
        }
        SubPaths = null;
        SubIndex = -1;
        ShowNotification(new GUIContent("清空所有版本"));
    }

    struct VersionFile
    {
        public string SourceFile;//版本下的文件
        public string DestFile;//目录下的文件
        public VersionState State;//文件状态

        public VersionFile(string source, string dest, VersionState sta)
        {
            SourceFile = source;
            DestFile = dest;
            State = sta;
        }

        public string GetFileName()
        {
            string fileName = string.Empty;
            switch (State)
            {
                case VersionState.Update: fileName = DestFile; break;
                case VersionState.New: fileName = DestFile; break;
                case VersionState.Delete: fileName = SourceFile; break;
            }
            return Path.GetFileName(fileName);
        }

        public Color GetColor()
        {
            Color color = Color.white;
            switch (State)
            {
                case VersionState.Update: color = Color.yellow; break;
                case VersionState.New: color = Color.green; break;
                case VersionState.Delete: color = Color.red; break;
            }
            return color;
        }

        public override string ToString()
        {
            return string.Format("{0}:{1}", State.ToString(), GetFileName());
        }
    }

    enum VersionState
    {
        /// <summary>
        /// 更新
        /// </summary>
        Update,
        /// <summary>
        /// 新建
        /// </summary>
        New,
        /// <summary>
        /// 已删除
        /// </summary>
        Delete
    }
}
