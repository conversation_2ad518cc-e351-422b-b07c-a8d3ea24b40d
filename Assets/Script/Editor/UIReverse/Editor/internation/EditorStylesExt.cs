using UnityEditor;
using UnityEngine;

public static class EditorStylesExt
{
    public static Texture2D popupIcon;
    public static Texture2D helpIcon;
    public static Texture2D errorIcon;
    public static Texture2D warnIcon;
    public static Texture2D infoIcon;
    public static Texture2D toolbarPlus;
    public static Texture2D toolbarMinus;
    public static Texture2D iCodeLogo;

    public static GUIStyle btnLeft;
    public static GUIStyle btnMid;
    public static GUIStyle btnRight;
    public static GUIStyle preButton;
    public static GUIStyle helpBox;
    public static GUIStyle toolbar;
    public static GUIStyle toolbarbutton;
    public static GUIStyle toolbarTE;
    public static GUIStyle toolbarbuttonTE;
    public static GUIStyle largeButton;
    public static GUIStyle tooltip;
    public static GUIStyle createRect;
    public static GUIStyle canvasBackground;
    public static GUIStyle selectionRect;
    public static GUIStyle elementBackground;
    public static GUIStyle breadcrumbLeft;
    public static GUIStyle breadcrumbMiddle;
    public static GUIStyle wrappedLabel;
    public static GUIStyle wrappedLabelLeft;
    public static GUIStyle variableHeader;
    public static GUIStyle label;
    public static GUIStyle centeredLabel;
    public static GUIStyle inspectorTitle;
    public static GUIStyle inspectorTitleText;
    public static GUIStyle stateLabelGizmo;
    public static GUIStyle instructionLabel;
    public static GUIStyle shortcutLabel;
    public static GUIStyle browserPopup;
    public static GUIStyle renderWindow;
    public static GUIStyle centerLabel;

    static EditorStylesExt()
    {
        popupIcon = EditorGUIUtility.FindTexture("_popup");
        helpIcon = EditorGUIUtility.FindTexture("_help");
        errorIcon = EditorGUIUtility.FindTexture("d_console.erroricon.sml");
        warnIcon = EditorGUIUtility.FindTexture("console.warnicon");
        infoIcon = EditorGUIUtility.FindTexture("console.infoicon");
        toolbarPlus = EditorGUIUtility.FindTexture("Toolbar Plus");
        toolbarMinus = EditorGUIUtility.FindTexture("Toolbar Minus");

        btnLeft = "ButtonLeft";
        btnMid = "ButtonMid";
        btnRight = "ButtonRight";
        preButton = "PreButton";
        helpBox = "HelpBox";
        toolbar = "Toolbar";
        toolbarbutton = "Toolbarbutton";
        toolbarTE = new GUIStyle("TE Toolbar")
        {
            alignment = TextAnchor.MiddleLeft,
        };
        toolbarbuttonTE = "TE toolbarbutton";
        largeButton = "LargeButton";
        tooltip = "Tooltip";
        createRect = "U2D.createRect";
        canvasBackground = "flow background";
        selectionRect = "SelectionRect";
        elementBackground = new GUIStyle("PopupCurveSwatchBackground")
        {
            padding = new RectOffset()
        };
        breadcrumbLeft = "GUIEditor.BreadcrumbLeft";
        breadcrumbMiddle = "GUIEditor.BreadcrumbMid";
        wrappedLabel = new GUIStyle("label")
        {
            fixedHeight = 0,
            wordWrap = true
        };
        wrappedLabelLeft = new GUIStyle("label")
        {
            fixedHeight = 0,
            wordWrap = true,
            alignment = TextAnchor.UpperLeft
        };
        variableHeader = "flow overlay header lower left";
        label = "label";
        inspectorTitle = "IN Title";
        inspectorTitleText = "IN TitleText";
        iCodeLogo = (Texture2D)Resources.Load("ICodeLogo");
        stateLabelGizmo = new GUIStyle("HelpBox")
        {
            alignment = TextAnchor.UpperCenter,
            fontSize = 21
        };
        centeredLabel = new GUIStyle("Label")
        {
            alignment = TextAnchor.UpperCenter,
        };
        instructionLabel = new GUIStyle("TL Selection H2")
        {
            padding = new RectOffset(3, 3, 3, 3),
            alignment = TextAnchor.UpperLeft,
            fixedHeight = 0,
            wordWrap = true
        };
        shortcutLabel = new GUIStyle("ObjectPickerLargeStatus")
        {
            padding = new RectOffset(3, 3, 3, 3),
            alignment = TextAnchor.UpperLeft
        };
        browserPopup = new GUIStyle("label")
        {
            contentOffset = new Vector2(0, 2)
        };
        renderWindow = new GUIStyle("window")
        {
            padding = new RectOffset(10, 10, 0, 10)
        };
        centerLabel = new GUIStyle("label")
        {
            alignment = TextAnchor.UpperCenter,
            fontStyle = FontStyle.Bold
        };
    }
}