using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;

[CustomEditor(typeof(UIReverse))]
public class UIReverseEditor : Editor
{
    private UIReverse comp;
    void OnEnable()
    {
        comp = target as UIReverse;
    }

    public override void OnInspectorGUI()
    {
        bool oldIgnore = comp.ignore;
        if(comp.ignore)
        {
            comp.ignore = EditorGUILayout.Toggle("Ignore", comp.ignore);
        }
        else
        {
            if(comp.includeChild)
            {
                comp.ignore = EditorGUILayout.Toggle("Ignore", comp.ignore);
                comp.includeChild = EditorGUILayout.Toggle("IncludeChild", comp.includeChild);
            }
            else
            {
                base.OnInspectorGUI();
                EditorGUILayout.HelpBox("OnlyRectTransform只会翻转RectTransform，其他的都会翻转RectTransform+所选", MessageType.Info);
            }
        }

        if(oldIgnore != comp.ignore) EditorUtility.SetDirty(comp.transform);
    }
}
