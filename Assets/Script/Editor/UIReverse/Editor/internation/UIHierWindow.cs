using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

public class UIHierWindow : EditorWindow
{
    /// <summary>
    /// UI层级
    /// </summary>
    class UIHierarchy
    {
        public RectTransform Rect;
        public bool isChoise;//是否选中
        public bool isFold;//是否展开
        public List<UIHierarchy> UIChildren;//子对象

        public UIHierarchy(RectTransform rect)
        {
            Rect = rect;
            isChoise = true;
        }
    }

    static EditorWindow _window;
    static GameObject _targetUI;
    static UIHierarchy _topHierarchy;
    Vector2 _scrollValue;
    Rect _area = new Rect(10, 20, 500, 400);

    const int BUTTON_WIDTH = 35;

    public static void ShowWindow()
    {
        _window = GetWindow(typeof(UIHierWindow), true, "UI预制翻转");
        InitHierarchy();
        _window.Show();
    }

    void OnGUI()
    {
        GUILayout.BeginHorizontal();
        GUILayout.Label("操作：", GUILayout.Width(100));
        if (GUILayout.Button("拾取预制", GUILayout.Width(100)))
        {
            InitHierarchy();
        }
        if (GUILayout.Button("翻转UI", GUILayout.Width(100)))
        {
            if (_topHierarchy != null)
            {
                int count = 0;
                ReversalChildrenUI(_topHierarchy, ref count);
                ShowNotification(new GUIContent(string.Format("翻转UI共{0}个", count)));
            }
        }
        GUILayout.EndHorizontal();

        if (_targetUI == null) return;
        GUILayout.BeginArea(_area);
        _scrollValue = GUILayout.BeginScrollView(_scrollValue);
        DrawHierarchyTree(_topHierarchy, 0);
        GUILayout.EndScrollView();
        GUILayout.EndArea();
    }

    /// <summary>
    /// 递归翻转UI层级
    /// </summary>
    /// <param name="hie"></param>
    /// <param name="count"></param>
    void ReversalChildrenUI(UIHierarchy hie, ref int count)
    {
        for (int i = 0, len = hie.UIChildren.Count; i < len; i++)
            ReversalChildrenUI(hie.UIChildren[i], ref count);

        if (hie.isChoise)
        {
            UIInternationUility.ReversalRect(hie.Rect);
            count++;
        }
    }

    /// <summary>
    /// 绘制层级树
    /// </summary>
    /// <param name="hie"></param>
    /// <param name="layer"></param>
    void DrawHierarchyTree(UIHierarchy hie, int layer)
    {
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField("", GUILayout.Width(layer * 20));
        hie.isChoise = EditorGUILayout.Toggle(hie.isChoise, GUILayout.Width(20));
        //展开
        if (hie.UIChildren.Count > 0 && GUILayout.Button(hie.isFold ? "▼" : "▶", GUILayout.Width(18), GUILayout.Height(18)))
            hie.isFold = !hie.isFold;
        else
            EditorGUILayout.LabelField("", GUILayout.Width(18), GUILayout.Height(18));
        //选中对象
        if (GUILayout.Button(hie.Rect.name, GUILayout.MaxWidth(160))) Selection.activeGameObject = hie.Rect.gameObject;
        //显示名称
        GUILayout.Label(layer.ToString(), GUILayout.Width(20));
        if (hie.UIChildren.Count > 0 && GUILayout.Button(hie.isFold ? "收起" : "展开", GUILayout.Width(BUTTON_WIDTH))) ExpandTree(hie, !hie.isFold);
        if (GUILayout.Button("全选", GUILayout.Width(BUTTON_WIDTH))) AllChoise(hie, !hie.isChoise);
        if (GUILayout.Button("反选", GUILayout.Width(BUTTON_WIDTH))) ReverseChoise(hie);
        if (GUILayout.Button("翻转", GUILayout.Width(BUTTON_WIDTH)))
        {
            UIInternationUility.ReversalRect(hie.Rect);
            Selection.activeGameObject = hie.Rect.gameObject;
        }
        EditorGUILayout.EndHorizontal();

        //展开继续绘制子层级
        if (hie.isFold)
        {
            for (int i = 0, len = hie.UIChildren.Count; i < len; i++)
            {
                UIHierarchy child = hie.UIChildren[i];
                DrawHierarchyTree(child, layer + 1);
            }
        }
    }

    /// <summary>
    /// 全选
    /// </summary>
    /// <param name="hie"></param>
    /// <param name="isChoise"></param>
    void AllChoise(UIHierarchy hie, bool isChoise)
    {
        hie.isChoise = isChoise;
        for (int i = 0, len = hie.UIChildren.Count; i < len; i++)
        {
            AllChoise(hie.UIChildren[i], isChoise);
        }
    }

    /// <summary>
    /// 反选
    /// </summary>
    /// <param name="hie"></param>
    void ReverseChoise(UIHierarchy hie)
    {
        hie.isChoise = !hie.isChoise;
        for (int i = 0, len = hie.UIChildren.Count; i < len; i++)
        {
            ReverseChoise(hie.UIChildren[i]);
        }
    }

    /// <summary>
    /// 展开树
    /// </summary>
    /// <param name="hie"></param>
    /// <param name="isFold"></param>
    void ExpandTree(UIHierarchy hie, bool isFold)
    {
        hie.isFold = isFold;
        for (int i = 0, len = hie.UIChildren.Count; i < len; i++)
            ExpandTree(hie.UIChildren[i], isFold);
    }

    /// <summary>
    /// 初始化对象层级
    /// </summary>
    static void InitHierarchy()
    {
        if (Selection.activeGameObject && Selection.activeGameObject.GetComponent<RectTransform>() && Selection.activeGameObject != _targetUI)
        {
            _targetUI = Selection.activeGameObject;
            _topHierarchy = new UIHierarchy(_targetUI.GetComponent<RectTransform>());
            CreateChildren(_topHierarchy);
        }
    }

    /// <summary>
    /// 创建层级树
    /// </summary>
    /// <param name="hie"></param>
    static void CreateChildren(UIHierarchy hie)
    {
        RectTransform rect = hie.Rect;
        hie.UIChildren = new List<UIHierarchy>();
        for (int i = 0, len = rect.childCount; i < len; i++)
        {
            RectTransform childRect = rect.GetChild(i).GetComponent<RectTransform>();
            if (childRect != null)
            {
                UIHierarchy childHie = new UIHierarchy(childRect);
                hie.UIChildren.Add(childHie);
                CreateChildren(childHie);
            }
        }
    }
}
