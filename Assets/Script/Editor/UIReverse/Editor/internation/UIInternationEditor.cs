using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;
using UnityEditor;
using UnityEngine;
using UnityEngine.UI;

public class UIInternationEditor : Editor
{
    #region UI水平翻转
    static int FrameCount;//帧数记录

    [MenuItem("GameObject/国际化UI/单个翻转(不含子UI)", false, 30)]
    public static void ReversalUI()
    {
        if (IsSameFrame()) return;

        //遍历翻转选中的UI对象
        if (Selection.activeGameObject && Selection.activeGameObject.GetComponent<RectTransform>())
        {
            Object[] objs = Selection.objects;
            foreach (Object obj in objs)
                ReversalRect((obj as GameObject).GetComponent<RectTransform>());
        }
    }

    [MenuItem("GameObject/国际化UI/整个翻转(包含子UI)", false, 30)]
    public static void ReversalAllUI()
    {
        if (IsSameFrame()) return;

        //遍历翻转选中的UI对象
        if (Selection.activeGameObject && Selection.activeGameObject.GetComponent<RectTransform>())
            ReversalUI(Selection.objects);
    }

    /// <summary>
    /// 检查是否为同一次操作
    /// </summary>
    /// <returns></returns>
    static bool IsSameFrame()
    {
        bool isSameFrame = FrameCount == Time.frameCount;
        if (!isSameFrame)
        {
            FrameCount = Time.frameCount;
            //隐藏显示一次，强制帧数更新
            if (Selection.activeGameObject)
            {
                GameObject selection = Selection.activeGameObject;
                bool active = selection.activeInHierarchy;
                selection.SetActive(!active);
                selection.SetActive(active);
            }
        }
        return isSameFrame;
    }

    [MenuItem("GameObject/国际化UI/界面/层级界面", false, 53)]
    static void ReversalUIPanel()
    {
        UIHierWindow.ShowWindow();
    }

    [MenuItem("GameObject/国际化UI/界面/正则验证界面", false, 54)]
    static void ShowRegexWin()
    {
        RegexWindow.ShowWindow();
    }

    [MenuItem("GameObject/国际化UI/界面/UI版本界面", false, 55)]
    static void ShowUIVersion()
    {
        UIVersionWindow.ShowWindow();
    }
    #endregion

    #region 预制操作
    const string PATTERN = "/panel(_lr)?/";
    const string PANEL = "panel";
    const string PANEL_LR = "panel_lr";

    [MenuItem("Assets/国际化/预制控制/移动Panel", false, 110)]
    static void MoveToPanel()
    {   //移动所有选中预制到Panel文件夹
        if (Selection.activeObject)
        {
            int moveCount = 0;
            Object[] objs = Selection.objects;
            foreach (Object obj in objs)
                if (MoveUIPrefab(PANEL, obj)) moveCount++;
            AssetDatabase.Refresh();
            Debug.Log(string.Format("成功移动预制文件到panel【{0}】个", moveCount));
        }
    }

    [MenuItem("Assets/国际化/预制控制/移动Panel_LR", false, 110)]
    static void MoveToPanelLR()
    {   //移动所有选中预制到Panel文件夹
        if (Selection.activeObject)
        {
            int moveCount = 0;
            Object[] objs = Selection.objects;
            foreach (Object obj in objs)
                if (MoveUIPrefab(PANEL_LR, obj)) moveCount++;
            AssetDatabase.Refresh();
            Debug.Log(string.Format("成功移动预制文件到panel_lr【{0}】个", moveCount));
        }
    }

    static bool MoveUIPrefab(string targetPanel, Object targetObj)
    {
        bool isSuccess = false;
        if (targetObj is GameObject && (targetObj as GameObject).transform.parent == null)
        {
            string path = AssetDatabase.GetAssetPath(targetObj);
            Match match = Regex.Match(path, PATTERN);
            if (match.Success)
            {
                string panelName = match.Value.Substring(1, match.Value.Length - 2);
                if (panelName != targetPanel)
                {
                    string newPath = path.Replace(panelName, targetPanel);
                    AssetDatabase.MoveAsset(path, newPath);
                    isSuccess = true;
                }
            }
        }
        return isSuccess;
    }

    [MenuItem("Assets/国际化/预制控制/反向选择 %f", false, 110)]
    static void ReverseChoise()
    {
        if (Selection.activeGameObject)
        {
            string path = AssetDatabase.GetAssetPath(Selection.activeGameObject);
            Match match = Regex.Match(path, PATTERN);
            if (match.Success)
            {
                string panelName = match.Value.Substring(1, match.Value.Length - 2);
                string newPath = null;
                if (panelName == PANEL)
                    newPath = path.Replace(@"/panel/", @"/panel_lr/");
                else
                    newPath = path.Replace(@"/panel_lr/", @"/panel/");
                GameObject target = AssetDatabase.LoadAssetAtPath<GameObject>(newPath);
                if (target != null)
                    Selection.activeGameObject = target;
                else
                    Debug.LogWarningFormat("没有找到对应的预制，路径：{0}", newPath);
            }
        }
    }

    [MenuItem("Assets/国际化/预制控制/拷贝布局", false, 110)]
    public static void CopyUI()
    {
        if (Selection.activeGameObject == null)
        {
            EditorUtility.DisplayDialog("失败", "还没有选择拷贝对象", "确认");
            return;
        }
        if (CopyGameObject == Selection.activeGameObject)
            CopyGameObject = null;
        else
        {
            CopyGameObject = Selection.activeGameObject;
            Debug.LogFormat("成功复制对象[{0}]", CopyGameObject.name);
        }
    }

    [MenuItem("Assets/国际化/预制控制/粘贴布局", false, 110)]
    public static void PasteUI()
    {
        if (CopyGameObject == null)
        {
            EditorUtility.DisplayDialog("失败", "还没有选择拷贝对象", "确认");
            return;
        }
        if (Selection.activeGameObject == null)
        {
            EditorUtility.DisplayDialog("失败", "还没有选择粘贴对象", "确认");
            return;
        }

        PasteCount = 0;
        PasteUIHierarchy(CopyGameObject.transform, Selection.activeGameObject.transform);
        if (PasteCount > 0)
        {
            //EditorUtility.SetDirty(Selection.activeGameObject.transform);
            //AssetDatabase.Refresh();
            Debug.LogFormat("粘贴对象成功[{0}] 组件[{1}]个", Selection.activeGameObject.name, PasteCount);
        }
        else
            Debug.LogErrorFormat("粘贴对象失败[{0}]", Selection.activeGameObject.name);
    }

    [MenuItem("GameObject/国际化UI/拷贝布局", false, 30)]
    static void CopyUI_GameObject()
    {
        CopyUI();
    }

    [MenuItem("GameObject/国际化UI/粘贴布局", false, 30)]
    static void PasteUI_GameObject()
    {
        PasteUI();
    }

    [MenuItem("Assets/国际化/预制控制/移动Panel", true)]
    static bool MoveToPanel_Check() { return InPanel(); }

    [MenuItem("Assets/国际化/预制控制/移动Panel_LR", true)]
    static bool MoveToPanelLR_Check() { return InPanel(); }

    [MenuItem("Assets/国际化/预制控制/反向选择 #f", true)]
    static bool ReverseChoise_Check() { return InPanel(); }

    [MenuItem("Assets/国际化/预制控制/拷贝布局", true)]
    static bool CopyUI_Check() { return InPanel(); }

    [MenuItem("Assets/国际化/预制控制/粘贴布局", true)]
    static bool PasteUI_Check() { return InPanel() && CopyGameObject; }
    #endregion

    #region 进度检查
    //排除界面
    static List<string> exclusivePanels = new List<string>() {
    "AllianceNameListPanel", "BuildMovePanel", "StartBuildPanel", "PaoPaoPanel", "CollegePanel",
    "CollegeTechTreePanel", "PopupTip_AlliancePanel", "PopupTip_ScrollPanel", "ReceiveItemEffectPanel", "LoginActivityPopPanel",
    "GuideDialogPanel", "GuideMainPanel", "MedalInfoPanel", "LordSkillUseTipsPanel", "LuckyGiftActivationPanel",
    "ClickPraisePanel", "PlayerOperationPanel", "RechargeCardPanel", "ServerMapPanel", "ErrorPanel",
    "TestGodPanel", "NormalTransitionPanel", "VipIntroducePopPanel", "WatchTowerWarningPanel", "BatteryWarStateTipPanel",
    "CastleNamePanel", "CastleOfficialPanel", "GridOptPanel", "MonsterNamePanel", "PalaceStateTipPanel",
    "PalaceWarStateTipPanel", "ResourceOwnerPanel", "ResourcePointLevelPanel", "TreasureChestPanel","CastleTimerPanel",
    "SoldierHelpTipPanel","BuyItemEffectPanel"};
    //未使用界面
    static List<string> unusedPanels = new List<string>() {
    "AllianceListSetPanel", "PyramidPanel", "RankMenuPanel", "KingdomPalaceChangePanel", "NewPlayerRewardPanel" };
    //动态改变界面
    static List<string> dynamicPanels = new List<string>() {
    "AllianceFlagPanel", "AllianceNoticeEditPanel", "EventBillboardPopPanel", "CastleLevelUpPanel","TreasurePoolPanel",
    "ResourceNotePanel"};

    [MenuItem("Assets/国际化/界面转换进度", false, 150)]
    static void CheckPanel()
    {
        string rootPath = Application.dataPath + "/Data/ui/";

        List<string> SrcPanels = GetAllPrefabs(rootPath + "panel");
        List<string> DestPanels = GetAllPrefabs(rootPath + "panel_lr");
        List<string> HandledPanels = new List<string>();

        //查找未处理的Panel
        for (int i = SrcPanels.Count - 1; i >= 0; i--)
        {
            string file_src = SrcPanels[i];
            for (int j = DestPanels.Count - 1; j >= 0; j--)
            {
                string file_dest = DestPanels[j];
                if (Path.GetFileName(file_src) == Path.GetFileName(file_dest))
                {
                    SrcPanels.RemoveAt(i);
                    DestPanels.RemoveAt(j);
                    HandledPanels.Add(file_src);
                    break;
                }
            }
        }

        Debug.Log(GetPrint(SrcPanels, "未处理界面", "red"));
        Debug.Log(GetPrint(HandledPanels, "已处理界面", "green"));
        Debug.Log(GetPrint(dynamicPanels, "动态界面", "orange"));
        Debug.Log(GetPrint(exclusivePanels, "忽略界面", "yellow"));
        Debug.Log(GetPrint(unusedPanels, "未启用界面", "cyan"));
    }

    static string GetPrint(List<string> list, string title, string color)
    {
        StringBuilder sb = new StringBuilder();
        sb.AppendLine(string.Format("<color={2}>{0}【{1}】个:</color>", title, list.Count, color));
        for (int i = 0, len = list.Count; i < len; i++)
            sb.AppendLine(string.Format("<color={2}>{0}:{1}</color>", i + 1, list[i], color));
        return sb.ToString();
    }

    static List<string> GetAllPrefabs(string path, string pattern = "*Panel*.prefab")
    {
        List<string> panels = new List<string>();
        if (Directory.Exists(path))
        {
            string[] files = Directory.GetFiles(path, pattern, SearchOption.AllDirectories);
            for (int i = 0; i < files.Length; i++)
            {
                string fileName = Path.GetFileNameWithoutExtension(files[i]);
                if (!exclusivePanels.Contains(fileName) && !unusedPanels.Contains(fileName) && !dynamicPanels.Contains(fileName))
                    panels.Add(files[i]);
            }
        }
        return panels;
    }
    
    [MenuItem("Assets/国际化/UI匹配进度", false, 150)]
    static void CheckUIHierarchy()
    {
        string path = Application.dataPath + "/Data/ui/panel/";
        List<string> prafabs = CheckPrefabs(path);
        Debug.Log(GetPrint(prafabs, "不匹配预制", "red"));
    }
    
    //检查记录不匹配预制
    static List<string> CheckPrefabs(string path)
    {
        string[] files = Directory.GetFiles(path, "*.prefab", SearchOption.AllDirectories);//获取指定文件夹下所有的prefab文件路径
        List<string> prafabs = new List<string>();//记录不匹配预制
        for (int i = 0; i < files.Length; i++)
        {
            string file = files[i];
            if (File.Exists(file.Replace(@"/panel/", @"/panel_lr/")))
            {
                string filePath = GetAssetPath(file);
                string destFilePath = filePath.Replace(@"/panel/", @"/panel_lr/");
                if (!CheckHierarchy(AssetDatabase.LoadAssetAtPath<GameObject>(filePath), AssetDatabase.LoadAssetAtPath<GameObject>(destFilePath)))
                    prafabs.Add(file);
            }
        }
        return prafabs;
    }

    //检查UIHierarchy是否相同
    static bool CheckHierarchy(GameObject src, GameObject dest)
    {
        UIHierarchy srcHie = src.GetComponent<UIHierarchy>();
        UIHierarchy destHie = dest.GetComponent<UIHierarchy>();

        if (srcHie == null && destHie == null) return true;

        if (srcHie.widgets.Count != destHie.widgets.Count) return false;
        for (int i = 0, len = srcHie.widgets.Count; i < len; i++)
            if (srcHie.widgets[i].name != destHie.widgets[i].name) return false;

        if (srcHie.effects.Count != destHie.effects.Count) return false;
        for (int i = 0, len = srcHie.effects.Count; i < len; i++)
            if (srcHie.effects[i].name != destHie.effects[i].name) return false;

        if (srcHie.externals.Count != destHie.externals.Count) return false;
        for (int i = 0, len = srcHie.externals.Count; i < len; i++)
            if (srcHie.externals[i].name != destHie.externals[i].name) return false;

        return true;
    }

    static int _fileTotalCount;//文件总数
    static int _fileCount;//已查找的文件数
    [MenuItem("Assets/国际化/预制引用查找", false, 150)]
    static void FindUIReference()
    {
        if (Selection.activeGameObject)
        {
            //统计文件总数
            _fileTotalCount = _fileCount = 0;
            CountUIPrefab(Application.dataPath + @"/Data/ui/panel/");
            CountUIPrefab(Application.dataPath + @"/Data/ui/panel_lr/");
            CountUIPrefab(Application.dataPath + @"/Data/ui_tr/panel/");
            CountUIPrefab(Application.dataPath + @"/Data/ui_tr/panel_lr/");

            //查找引用
            List<string> matchFiles = new List<string>();
            FindUI(Application.dataPath + @"/Data/ui/panel/", matchFiles);
            FindUI(Application.dataPath + @"/Data/ui/panel_lr/", matchFiles);
            FindUI(Application.dataPath + @"/Data/ui_tr/panel/", matchFiles);
            FindUI(Application.dataPath + @"/Data/ui_tr/panel_lr/", matchFiles);
            StringBuilder sb = new StringBuilder();
            sb.AppendLine(string.Format("引用预制【{0}】个:", matchFiles.Count));
            for (int i = 0, len = matchFiles.Count; i < len; i++)
                sb.AppendLine(string.Format("{0}:{1}", i + 1, matchFiles[i]));
            Debug.Log(sb.ToString());
            EditorUtility.ClearProgressBar();
        }
    }

    [MenuItem("Assets/国际化/预制引用查找", true)]
    static bool FindUIReference_Check() { return InPanel(); }

    //计算预制总数
    static void CountUIPrefab(string path)
    {
        if (Directory.Exists(path))
        {
            int count = Directory.GetFiles(path, "*.prefab", SearchOption.AllDirectories).Length; ;
            _fileTotalCount += count;
            Debug.Log(path + ":" + count);
        }
    }

    //查找预制引用
    static void FindUI(string path, List<string> matchFiles)
    {
        if (!Directory.Exists(path)) return;
        GameObject target = Selection.activeGameObject;
        string[] files = Directory.GetFiles(path, "*.prefab", SearchOption.AllDirectories);
        for (int i = 0; i < files.Length; i++)
        {
            string file = files[i];
            UIHierarchy hei = AssetDatabase.LoadAssetAtPath<GameObject>(GetAssetPath(file)).GetComponent<UIHierarchy>();
            if (hei == null) continue;
            for (int j = 0, len = hei.externals.Count; j < len; j++)
            {
                if (hei.externals[j].item == target)
                {
                    matchFiles.Add(file);
                    break;
                }
            }
            //显示进度条
            _fileCount++;
            EditorUtility.DisplayProgressBar("引用查找", string.Format("正在查找该预制的引用[{0}/{1}] Path:{2}", _fileCount, _fileTotalCount, file), (float)_fileCount / (float)_fileTotalCount);
        }
    }

    [MenuItem("Assets/国际化/查找丢失组件", false, 150)]
    static void FindMissComponent()
    {
        List<string> prefabs = new List<string>();
        prefabs.AddRange(GetAllPrefabs(Application.dataPath + "/Data/ui/panel/", "*.prefab"));
        prefabs.AddRange(GetAllPrefabs(Application.dataPath + "/Data/ui/panel_lr/", "*.prefab"));
        prefabs.AddRange(GetAllPrefabs(Application.dataPath + "/Data/ui_tr/panel/", "*.prefab"));
        prefabs.AddRange(GetAllPrefabs(Application.dataPath + "/Data/ui_tr/panel_lr/", "*.prefab"));
        List<string> missPanel = new List<string>();
        for (int i = 0, len = prefabs.Count; i < len; i++)
        {
            string path = prefabs[i];
            path = path.Substring(path.IndexOf("/Assets/") + 1);
            GameObject go = AssetDatabase.LoadAssetAtPath<GameObject>(path);
            if (go && go.GetComponent<UIHierarchy>())
            {
                UIHierarchy hie = go.GetComponent<UIHierarchy>();
                if (HasMiss(hie))
                    missPanel.Add(path);
                EditorUtility.DisplayProgressBar("查找丢失组件", string.Format("正在查找丢失组件[{0}/{1}] Path:{2}", i + 1, len, path), (float)(i + 1) / (float)len);
            }
        }
        EditorUtility.ClearProgressBar();
        Debug.Log(GetPrint(missPanel, "丢失组件", "red"));
    }

    [MenuItem("Assets/国际化/检查TR未替换的预制", false, 150)]
    static void CheckTRNeverReplacePrefabs()
    {
        //string strLog = "";
        //int iIndex = 0;

        //// 通过路径检查TR未替换的预制
        //CheckTRNeverReplacePrefabsByPath(Application.dataPath + "/Data/ui/panel/", ref strLog, ref iIndex);
        //CheckTRNeverReplacePrefabsByPath(Application.dataPath + "/Data/ui/panel_lr/", ref strLog, ref iIndex);

        //// 创建本地日志文件
        //StringBuilder logHandle = new StringBuilder();
        //logHandle.AppendLine(strLog);
        //LogFile.WriteLog("CheckTRNeverReplacePrefabs", logHandle.ToString());

        //ClearConsole();
        //Debug.Log(strLog);
        //Debug.Log("Check Finish");
    }

    // 通过路径检查TR未替换的预制
    static void CheckTRNeverReplacePrefabsByPath(string strUIPath, ref string strLog, ref int iIndex)
    {
        strLog += "<color=#41D241FF>" + strUIPath + ": </color> \n";
        string[] files = Directory.GetFiles(strUIPath, "*.prefab", SearchOption.AllDirectories);
        for (int i = 0; i < files.Length; ++i)
        {
            string strFile = files[i];
            string strPath = strFile.Replace(@"/panel/", @"/panel_lr/");
            strPath = strPath.Replace(@"/ui/", @"/ui_tr/");
            if (!File.Exists(strPath))
            {
                string filePath = GetAssetPath(strFile);
                GameObject obj = AssetDatabase.LoadAssetAtPath<GameObject>(filePath);
                if (!strLog.Contains(obj.name))
                {
                    filePath = filePath.Replace(@"Assets/Data/ui/", "");
                    filePath = filePath.Replace(@"\", @"/");
                    iIndex += 1;
                    strLog += iIndex + ". [路径:] " + filePath + ". [预制:] " + obj.name  + "\n";
                }
            }
        }
    }

    [MenuItem("Assets/国际化/检查带特效的预制", false, 150)]
    static void CheckEffectPrefabs()
    {
        //string strLog = "";
        //int iIndex = 1;
        //string[] guids = AssetDatabase.FindAssets("t:prefab", new string[] { Config.UI_ASSETS_PATH + "/panel", Config.UI_ASSETS_PATH + "/panel_lr" }); 
        //string path;
        //GameObject root;
        //foreach (string guid in guids)
        //{
        //    path = AssetDatabase.GUIDToAssetPath(guid);
        //    root = AssetDatabase.LoadAssetAtPath<GameObject>(path);

        //    if (root == null) continue;

        //    //ParticleSystem[] particleSystems = root.GetComponentsInChildren<ParticleSystem>(true);
        //    ParticleSystemRenderer[] particleSystems = root.GetComponentsInChildren<ParticleSystemRenderer>(true);
        //    SDEffectSprite[] sdEffectSpties = root.GetComponentsInChildren<SDEffectSprite>(true);
        //    SDParticleSystem[] sdParticleSystems = root.GetComponentsInChildren<SDParticleSystem>(true);

        //    if (particleSystems.Length > 0 || sdEffectSpties.Length > 0 || sdParticleSystems.Length > 0)
        //    {
        //        path = path.Replace(@"Assets/Data/ui_tr/", "");
        //        path = path.Replace(@"\", @"/");
        //        strLog += iIndex + ". [路径:] " + path + ". [预制:] " + root.name  + "\n";
        //        iIndex += 1;

        //        string strPath = "";
        //        if (particleSystems.Length > 0)
        //        {
        //            strLog += "ParticleSystem : " + "\n";
        //            for (int i = 0; i < particleSystems.Length; i++)
        //            {
        //                GameObject obj = particleSystems[i].gameObject;
        //                strPath = GetGameObjectPathInHierarchy(obj, root.gameObject);
        //                strLog += " -- " + strPath + ". [mainTexture] :" + particleSystems[i].sharedMaterial.mainTexture.name + "\n" ;
        //            }
        //        }

        //        if (sdEffectSpties.Length > 0)
        //        {
        //            strLog += "SDEffectSprite : " + "\n";
        //            for (int i = 0; i < sdEffectSpties.Length; i++)
        //            {
        //                GameObject obj = sdEffectSpties[i].gameObject;
        //                strPath = GetGameObjectPathInHierarchy(obj, root.gameObject);
        //                Image xx = sdEffectSpties[i] as Image;
        //                strLog += " -- " + strPath + "\n";
        //            }
        //        }

        //        if (sdParticleSystems.Length > 0)
        //        {
        //            strLog += "SDParticleSystem : " + "\n";
        //            for (int i = 0; i < sdParticleSystems.Length; i++)
        //            {
        //                GameObject obj = sdParticleSystems[i].gameObject;
        //                strPath = GetGameObjectPathInHierarchy(obj, root.gameObject);
        //                strLog += " -- " + strPath + "\n";
        //                //sdParticleSystems[i].GetEffectSprite()

        //            }
        //        }
        //    }
        //}

        //// 创建本地日志文件
        //StringBuilder logHandle = new StringBuilder();
        //logHandle.AppendLine(strLog);
        //LogFile.WriteLog("CheckEffectPrefabs", logHandle.ToString());

        //ClearConsole();
        //Debug.Log(strLog);
        //Debug.Log("Check Finish");
    }

    // 获取层级路径
    public static string GetGameObjectPathInHierarchy(GameObject obj, GameObject objParent)
    {
        string path = "/" + obj.name;
        while (obj.transform.parent != null && obj.transform.parent != objParent)
        {
            obj = obj.transform.parent.gameObject;
            path = "/" + obj.name + path;
        }
        return path;
    }

    static bool HasMiss(UIHierarchy hie)
    {
        for (int i = 0, len = hie.widgets.Count; i < len; i++)
            if (hie.widgets[i].item == null) return true;

        for (int i = 0, len = hie.effects.Count; i < len; i++)
            if (hie.effects[i].item == null) return true;

        for (int i = 0, len = hie.externals.Count; i < len; i++)
            if (hie.externals[i].item == null) return true;
        return false;
    }
    #endregion

    #region 语言设置
    //const string LANGUAGE_ARAB = "ar";//阿拉伯语
    //const string LANGUAGE_EN = "en";//英文
    //const string LANGUAGE_CN = "zh_cn";//中文
    //const string LANGUAGE_DE = "de";//德语
    //const string LANGUAGE_ES = "es";//西班牙语
    //const string LANGUAGE_FR = "fr";//法语
    //const string LANGUAGE_RU = "ru";//俄语
    //const string LANGUAGE_IT = "it";//意大利
    //const string LANGUAGE_PT = "pt";//葡萄牙
    //const string LANGUAGE_TR = "tr";//土耳其

    //[MenuItem("Assets/国际化/切换语言/中文", false, 100)]
    //static void SwitchCN()
    //{
    //    PlayerPrefs.SetString(Config.KEY_LANGUAGE, LANGUAGE_CN);
    //    Debug.Log(string.Format("<color=green>切换语言[中文]</color>"));
    //}

    //[MenuItem("Assets/国际化/切换语言/阿拉伯语", false, 100)]
    //static void SwitchARAB()
    //{
    //    PlayerPrefs.SetString(Config.KEY_LANGUAGE, LANGUAGE_ARAB);
    //    Debug.Log(string.Format("<color=orange>切换语言[阿拉伯语]</color>"));
    //}

    //[MenuItem("Assets/国际化/切换语言/英文", false, 100)]
    //static void SwitchEN()
    //{
    //    PlayerPrefs.SetString(Config.KEY_LANGUAGE, LANGUAGE_EN);
    //    Debug.Log(string.Format("<color=#008BFF>切换语言[英文]</color>"));
    //}

    //[MenuItem("Assets/国际化/切换语言/德语", false, 100)]
    //static void SwitchDE()
    //{
    //    PlayerPrefs.SetString(Config.KEY_LANGUAGE, LANGUAGE_DE);
    //    Debug.Log(string.Format("<color=red>切换语言[德语]</color>"));
    //}

    //[MenuItem("Assets/国际化/切换语言/西班牙语", false, 100)]
    //static void SwitchES()
    //{
    //    PlayerPrefs.SetString(Config.KEY_LANGUAGE, LANGUAGE_ES);
    //    Debug.Log(string.Format("<color=grey>切换语言[西班牙语]</color>"));
    //}

    //[MenuItem("Assets/国际化/切换语言/法语", false, 100)]
    //static void SwitchFR()
    //{
    //    PlayerPrefs.SetString(Config.KEY_LANGUAGE, LANGUAGE_FR);
    //    Debug.Log(string.Format("<color=#00FFC0>切换语言[法语]</color>"));
    //}

    //[MenuItem("Assets/国际化/切换语言/俄语", false, 100)]
    //static void SwitchRU()
    //{
    //    PlayerPrefs.SetString(Config.KEY_LANGUAGE, LANGUAGE_RU);
    //    Debug.Log(string.Format("<color=#FFF800>切换语言[俄语]</color>"));
    //}

    //[MenuItem("Assets/国际化/切换语言/意大利语", false, 100)]
    //static void SwitchIT()
    //{
    //    PlayerPrefs.SetString(Config.KEY_LANGUAGE, LANGUAGE_IT);
    //    Debug.Log(string.Format("<color=#00FFB5>切换语言[意大利语]</color>"));
    //}

    //[MenuItem("Assets/国际化/切换语言/葡萄牙语", false, 100)]
    //static void SwitchPT()
    //{
    //    PlayerPrefs.SetString(Config.KEY_LANGUAGE, LANGUAGE_PT);
    //    Debug.Log(string.Format("<color=#FF45DB>切换语言[葡萄牙语]</color>"));
    //}

    //[MenuItem("Assets/国际化/切换语言/土耳其语", false, 100)]
    //static void SwitchTR()
    //{
    //    PlayerPrefs.SetString(Config.KEY_LANGUAGE, LANGUAGE_TR);
    //    Debug.Log(string.Format("<color=#CE8E03>切换语言[土耳其语]</color>"));
    //}

    //[MenuItem("Assets/国际化/切换语言/中文", true)]
    //static bool CheckCN()
    //{
    //    return PlayerPrefs.GetString(Config.KEY_LANGUAGE) != LANGUAGE_CN;
    //}

    //[MenuItem("Assets/国际化/切换语言/阿拉伯语", true)]
    //static bool CheckARAB()
    //{
    //    return PlayerPrefs.GetString(Config.KEY_LANGUAGE) != LANGUAGE_ARAB;
    //}

    //[MenuItem("Assets/国际化/切换语言/英文", true)]
    //static bool CheckEN()
    //{
    //    return PlayerPrefs.GetString(Config.KEY_LANGUAGE) != LANGUAGE_EN;
    //}

    //[MenuItem("Assets/国际化/切换语言/德语", true)]
    //static bool CheckDE()
    //{
    //    return PlayerPrefs.GetString(Config.KEY_LANGUAGE) != LANGUAGE_DE;
    //}

    //[MenuItem("Assets/国际化/切换语言/西班牙语", true)]
    //static bool CheckES()
    //{
    //    return PlayerPrefs.GetString(Config.KEY_LANGUAGE) != LANGUAGE_ES;
    //}

    //[MenuItem("Assets/国际化/切换语言/法语", true)]
    //static bool CheckFR()
    //{
    //    return PlayerPrefs.GetString(Config.KEY_LANGUAGE) != LANGUAGE_FR;
    //}

    //[MenuItem("Assets/国际化/切换语言/俄语", true)]
    //static bool CheckRU()
    //{
    //    return PlayerPrefs.GetString(Config.KEY_LANGUAGE) != LANGUAGE_RU;
    //}

    //[MenuItem("Assets/国际化/切换语言/意大利语", true)]
    //static bool CheckIT()
    //{
    //    return PlayerPrefs.GetString(Config.KEY_LANGUAGE) != LANGUAGE_IT;
    //}

    //[MenuItem("Assets/国际化/切换语言/葡萄牙语", true)]
    //static bool CheckPT()
    //{
    //    return PlayerPrefs.GetString(Config.KEY_LANGUAGE) != LANGUAGE_PT;
    //}

    //[MenuItem("Assets/国际化/切换语言/土耳其语", true)]
    //static bool CheckTR()
    //{
    //    return PlayerPrefs.GetString(Config.KEY_LANGUAGE) != LANGUAGE_TR;
    //}
    #endregion

    #region 通用函数
    /// <summary>
    /// 翻转UI对象
    /// </summary>
    /// <param name="rect"></param>
    public static void ReversalRect(RectTransform rect)
    {
        UIInternationUility.FilpRect(rect);
        UIInternationUility.FilpText(rect);
        UIInternationUility.FilpSlider(rect);
        UIInternationUility.FilpLoopHorizontalScrollRect(rect);
        UIInternationUility.FilpGridLayoutGroup(rect);
        UIInternationUility.FilpVerticalLayoutGroup(rect);
        UIInternationUility.FilpHorizontalLayoutGroup(rect);
        UIInternationUility.FilpUILineFocusScroll(rect);
    }

    /// <summary>
    /// 递归翻转UI层级
    /// </summary>
    /// <param name="objs"></param>
    public static void ReversalUI(Object[] objs)
    {
        List<RectTransform> RectList = new List<RectTransform>();
        foreach (Object obj in objs)
            ReversalChildrenUI((obj as GameObject).GetComponent<RectTransform>(), RectList);
        RectList.Clear();
    }

    static void ReversalChildrenUI(RectTransform rect, List<RectTransform> RectList)
    {
        if (!IsFlip(rect, RectList)) return;

        ////剔除正反图片
        //for (int i = 0, len = rect.childCount; i < len; i++)
        //{
        //    RectTransform child = rect.GetChild(i).GetComponent<RectTransform>();
        //    if (child == null) continue;
        //    int state = 0;
        //    if (Mathf.RoundToInt(child.localScale.x) == -1) state = 1;
        //    else if (Mathf.RoundToInt(child.localScale.y) == -1) state = 2;
        //    else if (Mathf.RoundToInt(child.localRotation.eulerAngles.y) == 180) state = 3;
        //    else if (Mathf.RoundToInt(child.localRotation.eulerAngles.z) == 90) state = 4;
        //    if (state != 0)
        //    {
        //        ImageEx image = child.GetComponent<ImageEx>();
        //        if (!image) continue;
        //        for (int j = 0; j < len; j++)
        //        {
        //            RectTransform childOther = rect.GetChild(j).GetComponent<RectTransform>();
        //            ImageEx imageOther = childOther.GetComponent<ImageEx>();
        //            if (imageOther && !RectList.Contains(childOther) && image.SpriteName == imageOther.SpriteName && image != imageOther)
        //            {
        //                if ((state == 1 && Mathf.RoundToInt(childOther.localScale.x) == 1)
        //                    || (state == 2 && Mathf.RoundToInt(childOther.localScale.y) == 1)
        //                    || (state == 3 && Mathf.RoundToInt(childOther.localRotation.eulerAngles.y) == 0)
        //                    || (state == 4 && Mathf.RoundToInt(childOther.localRotation.eulerAngles.z) == 270))
        //                {
        //                    RectList.Add(child);
        //                    RectList.Add(childOther);
        //                    break;
        //                }
        //            }
        //        }
        //    }
        //}

        //递归翻转
        for (int i = 0, len = rect.childCount; i < len; i++)
        {
            RectTransform child = rect.GetChild(i).GetComponent<RectTransform>();
            if (IsFlip(child, RectList)) ReversalChildrenUI(child, RectList);
        }

        //执行翻转操作
        if (IsFlip(rect, RectList))
        {
            RectList.Add(rect);
            ReversalRect(rect);
        }
    }

    private static bool IsFlip(RectTransform rect, List<RectTransform> list)
    {
        return rect != null && !list.Contains(rect) && !rect.CompareTag(UIInternationUility.__D);
    }

    public static bool IsDynamic(Transform rect)
    {
        return rect != null && rect.CompareTag(UIInternationUility.__D);
    }

    public static void AddOrDefTag(string strTag)
    {
        if (Selection.activeObject)
        {
            GameObject[] objs = Selection.gameObjects;
            for (int i = 0, len = objs.Length; i < len; i++)
            {
                AddOrDefTagForTarget( objs[i].transform, strTag);
            }
        }
    }
    public static void AddOrDefTagForTarget(Transform target, string strTag)
    {
        Transform tran = target.transform;

        if (tran.CompareTag(strTag))
        {
            tran.tag = UIInternationUility.__U;
        }
        else
        {
            tran.tag = strTag;
        }
        EditorUtility.SetDirty(tran);
    }

    /// <summary>
    /// 检查选中对象是否在Panel文件夹内
    /// </summary>
    /// <returns></returns>
    static bool InPanel()
    {
        bool isUsed = false;
        if (Selection.activeGameObject)
        {
            string path = AssetDatabase.GetAssetPath(Selection.activeGameObject);
            Match match = Regex.Match(path, PATTERN);
            isUsed = match.Success;
        }
        return isUsed;
    }

    public static GameObject CopyGameObject;//复制对象
    static int PasteCount;//粘贴次数
    static void PasteUIHierarchy(Transform copyObj, Transform pasteObj)
    {
        if (copyObj.name != pasteObj.name) return;
        if (PasteUI(copyObj, pasteObj)) PasteCount++;
        for (int i = 0, len = copyObj.childCount; i < len; i++)
        {
            Transform copyTran = copyObj.GetChild(i);
            if (pasteObj.childCount > i)
            {
                Transform pasteTran = pasteObj.GetChild(i);
                PasteUIHierarchy(copyTran, pasteTran);
            }
        }
    }

    static bool PasteUI(Transform copyObj, Transform pasteObj)
    {
        int pasteTime = 0;

        //复制布局
        if (PasteComponent<RectTransform>(copyObj, pasteObj)) pasteTime++;

        //复制文本
        if (copyObj.GetComponent<Text>() && pasteObj.GetComponent<Text>())
        {
            pasteObj.GetComponent<Text>().alignment = copyObj.GetComponent<Text>().alignment;
            pasteTime++;
        }

        //复制滑动条
        if (copyObj.GetComponent<Slider>() && pasteObj.GetComponent<Slider>())
        {
            pasteObj.GetComponent<Slider>().direction = copyObj.GetComponent<Slider>().direction;
            pasteTime++;
        }

        //复制网格布局
        if (copyObj.GetComponent<GridLayoutGroup>() && pasteObj.GetComponent<GridLayoutGroup>())
        {
            pasteObj.GetComponent<GridLayoutGroup>().startCorner = copyObj.GetComponent<GridLayoutGroup>().startCorner;
            pasteObj.GetComponent<GridLayoutGroup>().childAlignment = copyObj.GetComponent<GridLayoutGroup>().childAlignment;
            pasteObj.GetComponent<GridLayoutGroup>().padding = copyObj.GetComponent<GridLayoutGroup>().padding;
            pasteTime++;
        }

        //复制垂直布局
        if (copyObj.GetComponent<VerticalLayoutGroup>() && pasteObj.GetComponent<VerticalLayoutGroup>())
        {
            pasteObj.GetComponent<VerticalLayoutGroup>().childAlignment = copyObj.GetComponent<VerticalLayoutGroup>().childAlignment;
            pasteObj.GetComponent<VerticalLayoutGroup>().padding = copyObj.GetComponent<VerticalLayoutGroup>().padding;
            pasteTime++;
        }

        //复制水平布局
        if (copyObj.GetComponent<HorizontalLayoutGroup>() && pasteObj.GetComponent<HorizontalLayoutGroup>())
        {
            pasteObj.GetComponent<HorizontalLayoutGroup>().childAlignment = copyObj.GetComponent<HorizontalLayoutGroup>().childAlignment;
            pasteObj.GetComponent<HorizontalLayoutGroup>().padding = copyObj.GetComponent<HorizontalLayoutGroup>().padding;
            pasteTime++;
        }

        //复制图片填充方向
        if (copyObj.GetComponent<Image>() && pasteObj.GetComponent<Image>())
        {
            pasteObj.GetComponent<Image>().fillOrigin = copyObj.GetComponent<Image>().fillOrigin;
            pasteTime++;
        }

        return pasteTime > 0;
    }

    static bool PasteComponent<T>(Transform copyObj, Transform pasteObj) where T : Component
    {
        if (copyObj.GetComponent<T>() && pasteObj.GetComponent<T>())
        {
            UnityEditorInternal.ComponentUtility.CopyComponent(copyObj.GetComponent<T>());
            UnityEditorInternal.ComponentUtility.PasteComponentValues(pasteObj.GetComponent<T>());
            return true;
        }
        return false;
    }

    public static string GetCopyName()
    {
        string name = string.Empty;
        if (CopyGameObject) name = CopyGameObject.name;
        return name;
    }
    #endregion

    #region 切换项目方案
    private const string DEFINE_TR = "TR";
    // [MenuItem("Assets/项目切换/DK", false, 101)]
    // public static void SwitchTRUI()
    // {
    //     SwitchDefine();
    // }

    // [MenuItem("Assets/项目切换/TR", false, 101)]
    // public static void SwitchDKUI()
    // {
    //     SwitchDefine();
    // }

    // [MenuItem("Assets/项目切换/DK", true)]
    // static bool CheckDKUI()
    // {
    //     return PlayerSettings.GetScriptingDefineSymbolsForGroup(EditorUserBuildSettings.selectedBuildTargetGroup).Contains(DEFINE_TR);
    // }

    // [MenuItem("Assets/项目切换/TR", true)]
    // static bool CheckTRUI()
    // {
    //     return !PlayerSettings.GetScriptingDefineSymbolsForGroup(EditorUserBuildSettings.selectedBuildTargetGroup).Contains(DEFINE_TR);
    // }

    public static void SwitchDefine()
    {
        string defineGroup = PlayerSettings.GetScriptingDefineSymbolsForGroup(EditorUserBuildSettings.selectedBuildTargetGroup);
        string[] defines = defineGroup.Split(';');

        StringBuilder define = new StringBuilder();
        if (defineGroup.Contains(DEFINE_TR))
        {
            foreach (string def in defines)
                if (def != DEFINE_TR)
                    define.AppendFormat("{0};", def);
            define = define.Remove(define.Length - 1, 1);
            Debug.LogFormat("<color=red>切换UI[DK]</color> ScriptingDefine:{0}", define);
        }
        else
        {
            foreach (string def in defines)
                define.AppendFormat("{0};", def);
            define.Append(DEFINE_TR);
            Debug.LogFormat("<color=green>切换UI[TR]</color> ScriptingDefine:{0}", define);
        }
        PlayerSettings.SetScriptingDefineSymbolsForGroup(EditorUserBuildSettings.selectedBuildTargetGroup, define.ToString());
    }

    private const string DKPath = @"/Data/ui/";
    private const string TRPath = @"/Data/ui_tr/";

    // [MenuItem("Assets/项目切换/预制切换 &f", false, 110)]
    static void SwitchChoise()
    {
        if (Selection.activeGameObject)
        {
            string path = AssetDatabase.GetAssetPath(Selection.activeGameObject);
            Match match = Regex.Match(path, PATTERN);
            if (match.Success)
            {
                string newPath = null;
                if (path.Contains(DKPath))
                    newPath = path.Replace(DKPath, TRPath);
                else if (path.Contains(TRPath))
                    newPath = path.Replace(TRPath, DKPath);
                GameObject target = AssetDatabase.LoadAssetAtPath<GameObject>(newPath);
                if (target == null)
                {
                    if (newPath.Contains(@"/panel/"))
                        newPath = newPath.Replace(@"/panel/", @"/panel_lr/");
                    else if (newPath.Contains(@"/panel_lr/"))
                        newPath = newPath.Replace(@"/panel_lr/", @"/panel/");
                    target = AssetDatabase.LoadAssetAtPath<GameObject>(newPath);
                }
                if (target != null)
                    Selection.activeGameObject = target;
                else
                    Debug.LogWarningFormat("没有找到对应的预制，路径：{0}", newPath);
            }
        }
    }
    #endregion

    #region 清空Console
    static void ClearConsole()
    {
        // This simply does "LogEntries.Clear()" the long way:
        var logEntries = System.Type.GetType("UnityEditorInternal.LogEntries,UnityEditor.dll");
        var clearMethod = logEntries.GetMethod("Clear", System.Reflection.BindingFlags.Static | System.Reflection.BindingFlags.Public);
        clearMethod.Invoke(null, null);
    }
    #endregion

    static UIHierarchy CacheHierarchy;//缓存Hierarchy
    static List<ItemPair> Pairs = new List<ItemPair>();
    static Regex regexAnnotate = new Regex(@"^\s*(--).*$");//过滤注释
    static Regex regexClass = new Regex(@"local.*class\(.*, (.*)\)");//过滤父类
    static Regex regexOne = new Regex(@".*self:GenerateOne\(\s*(self.)?.*,\s*(self.)?(.*),\s*(self.)?(.*)\)");
    static Regex regexItems = new Regex(@".*self:GenerateItemList\(\s*self.*,.*,.*,\s*(self.)?(.*),\s*(self.)?(.*)\)");
    static Regex regexCreate = new Regex(@".*GOUtil.InstantiateUI\(\s*(self.)?(.*),\s*(self.)?(.*)\)");
    // public static void AddOrDelF()
    // {
    //     if (Selection.activeObject)
    //     {
    //         GameObject[] objs = Selection.gameObjects;
    //         for (int i = 0, len = objs.Length; i < len; i++)
    //         {
    //             Transform tran = objs[i].transform;
    //             if (tran.CompareTag(UIInternationUility.__F))
    //                 tran.tag = UIInternationUility.__U;
    //             else tran.tag = UIInternationUility.__F;
    //             EditorUtility.SetDirty(tran);
    //         }
    //     }

    // }

    public static bool IsReverse(Transform tran)
    {
        return tran.CompareTag(UIInternationUility.__F);
    }
    public static bool IsScaleFlip(Transform tran)
    {
        return tran.CompareTag(UIInternationUility.__SF);
    }

    //生成配置数据
    public static List<ItemPair> GenerateItemPairs(UIHierarchy hie)
    {
        if (CacheHierarchy == hie) return Pairs;
        CacheHierarchy = hie;
        Pairs.Clear();
        string fileName = SearchLuaScript(hie.name);
        if (fileName != null) AnalyseLuaScript(fileName);

        //补全未匹配到的预制
        List<UIHierarchy.ItemInfo> externals = CacheHierarchy.externals;
        for (int i = 0, len = externals.Count; i < len; i++)
        {
            UIHierarchy.ItemInfo item = externals[i];
            bool isExist = false;
            for (int j = 0, len2 = Pairs.Count; j < len2; j++)
            {
                if (Pairs[j].Prefab == item.item)
                {
                    isExist = true;
                    break;
                }
            }
            if (!isExist)
            {
                Pairs.Add(new ItemPair(item.name, "None", item.item as GameObject, null, false));
            }
        }
        return Pairs;
    }

    //查找对应Lua脚本
    private static string SearchLuaScript(string prefabName)
    {
        prefabName = prefabName.Replace("(Clone)", "");
        string luaFile = null;
        string rootPath = Application.dataPath + "/Lua/game/modules/ui/";
        string fileName = prefabName.Replace("Panel", "View").Replace("_lr", "");
        string[] files = Directory.GetFiles(rootPath, string.Format(@"{0}.lua", fileName), SearchOption.AllDirectories);
        if (files == null || files.Length == 0)
            files = Directory.GetFiles(rootPath, string.Format(@"{0}.lua", prefabName), SearchOption.AllDirectories);
        if (files != null && files.Length > 0)
            luaFile = Path.GetFileNameWithoutExtension(files[0]);
        return luaFile;
    }

    //分析Lua脚本寻找配置数据
    private static void AnalyseLuaScript(string fileName)
    {
        string rootPath = Application.dataPath + "/Lua/game/modules/ui/";
        string[] files = Directory.GetFiles(rootPath, string.Format(@"{0}.lua", fileName), SearchOption.AllDirectories);
        if (files.Length > 0)
        {
            string path = files[0];
            string parentClass = null;
            using (StreamReader reader = new StreamReader(path, System.Text.Encoding.Default))
            {
                string line;
                while ((line = reader.ReadLine()) != null)
                {
                    //跳过注释
                    Match mat = regexAnnotate.Match(line);
                    if (mat.Success) continue;

                    //匹配父类
                    if (parentClass == null)
                    {
                        mat = regexClass.Match(line);
                        if (mat.Success && mat.Groups.Count >= 2)
                        {
                            parentClass = mat.Groups[1].Value;
                            continue;
                        }
                    }

                    //匹配self:GenerateOne
                    mat = regexOne.Match(line);
                    if (mat.Success && mat.Groups.Count >= 5)
                    {
                        CreatePair(mat.Groups[3].Value.Replace("\"", ""), mat.Groups[5].Value.Replace("\"", ""), true);
                        continue;
                    }

                    //匹配self:GenerateItemList
                    mat = regexItems.Match(line);
                    if (mat.Success && mat.Groups.Count >= 4)
                    {
                        CreatePair(mat.Groups[2].Value.Replace("\"", ""), mat.Groups[4].Value.Replace("\"", ""), false);
                        continue;
                    }

                    //匹配GOUtil.InstantiateUI
                    mat = regexCreate.Match(line);
                    if (mat.Success && mat.Groups.Count >=4)
                    {
                        CreatePair(mat.Groups[2].Value, mat.Groups[4].Value, false);
                    }
                }
            }

            //递归查找父类
            if (parentClass != null)
            {
                if (parentClass.StartsWith("me.")) parentClass = parentClass.Substring(3);
                if (parentClass == "ObjectBase") return;
                AnalyseLuaScript(parentClass);
            }
        }
    }

    //创建配对
    private static void CreatePair(string prefabName, string parentName, bool isUnique)
    {
        //父容器名称处理
        if (parentName.IndexOf(',') > 0) parentName = parentName.Substring(0, parentName.IndexOf(','));
        parentName = parentName.Replace(".transform", "").Replace(".gameObject", "");
        bool inContent = parentName.IndexOf(".content") > 0;
        if (inContent) parentName = parentName.Replace(".content", "");

        //检查是否已经存在该元素
        bool isExist = false;
        for (int i = 0, len = Pairs.Count; i < len; i++)
        {
            isExist = Pairs[i].isEquals(prefabName, parentName);
            if (isExist) return;
        }

        GameObject prefab = null;
        List<UIHierarchy.ItemInfo> externals = CacheHierarchy.externals;
        for (int i = 0, len = externals.Count; i < len; i++)
        {
            if (externals[i].name == prefabName)
            {
                prefab = externals[i].item as GameObject;
                break;
            }
        }

        Transform parent = null;
        if (parentName == "gameObject") parent = CacheHierarchy.transform;
        else
        {
            List<UIHierarchy.ItemInfo> widgets = CacheHierarchy.widgets;
            for (int i = 0, len = widgets.Count; i < len; i++)
            {
                if (widgets[i].name == parentName)
                {
                    UnityEngine.Object obj = widgets[i].item;
                    if (obj is GameObject) parent = (obj as GameObject).transform;
                    else if (obj is Component) parent = (obj as Component).transform;
                    if (inContent) parent = parent.GetChild(0).GetChild(0);
                    break;
                }
            }
        }

        if (prefab)
            Pairs.Add(new ItemPair(prefabName, parentName, prefab, parent, isUnique));
    }

    public static void ClearPair()
    {
        for (int i = 0, len = Pairs.Count; i < len; i++)
            ClearPair(Pairs[i]);
    }

    public static void ClearPair(ItemPair pair)
    {
        List<GameObject> prefabs = pair.Prefabs;
        for (int i = 0, len = prefabs.Count; i < len; i++)
        {
            GameObject prefab = prefabs[i];
            if (prefab)
            {
                UnityEngine.Object.DestroyImmediate(prefab);
            }
        }
        prefabs.Clear();
    }

    //保存预制
    public static void SavePrefab(RectTransform rect, bool isArab, bool isShowDialog = true)
    {
        string msg = string.Empty;
        // string path = AssetDatabase.GetAssetPath(PrefabUtility.GetCorrespondingObjectFromSource(rect));
        // if (path.Length == 0) path = AssetDatabase.GetAssetPath(PrefabUtility.GetPrefabInstanceHandle(rect));
        // if (path.Length == 0) path = PrefabStageUtility.GetPrefabStage(rect.gameObject).assetPath;
        string path = UIPrefabHelper.GetPrefabAssetPath(rect.gameObject);

        if (!string.IsNullOrEmpty(path))
        {
            if (path.Length == 0)
            {
                string prefabName = rect.name + ".prefab";
                string[] files = Directory.GetFiles(Application.dataPath + @"/Data/ui/", prefabName, SearchOption.AllDirectories);
                if (files.Length > 0)
                {
                    path = files[0];
                    path = path.Substring(path.IndexOf("Assets/")).Replace(@"\", @"/");
                }
                else msg = "保存预制失败：无法获取该预制的路径 : " + prefabName;
            }

            if (path.Length > 0)
            {
                path = isArab ? path.Replace("/panel_lr/", "/panel/") : path.Replace("/panel/", "/panel_lr/");

                string dir = path.Substring(0, path.LastIndexOf('/'));
                if (!Directory.Exists(dir)) Directory.CreateDirectory(dir);

                GameObject targetPrefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                if (targetPrefab)
                {
                    PrefabUtility.SaveAsPrefabAsset(rect.gameObject, path);
                    msg = string.Format("替换预制成功,路径：{0}", path);
                }
                else
                {
                    bool suc;
                    PrefabUtility.SaveAsPrefabAsset(rect.gameObject, path, out suc);
                    if(suc) msg = string.Format("新建预制成功,路径：{0}", path);
                    else msg = "创建预制失败";
                }
            }
        }
        else msg = "保存预制失败：该对象非UI预制 : " + rect.name;

        if (isShowDialog)
        {
            EditorUtility.DisplayDialog("保存结果", msg, "确认");
            return;
        }
        Debug.Log(msg);
    }

    //更新预制上的引用预制
    public static void UpdateExternals(RectTransform mTran, bool isArab)
    {
        UIHierarchy hie = mTran.GetComponent<UIHierarchy>();
        if (hie == null)
        {
            Debug.Log(string.Format("{0}不存在UIHierarchy组件，请选择挂载UIHierarchy组件的物体", mTran.name));
            return;
        }

        var externals = hie.externals;
        for (int i = 0, length = externals.Count; i < length; i++)
        {
            GameObject root = (GameObject)externals[i].item;
            string path = AssetDatabase.GetAssetPath(root);

            path = isArab ? path.Replace("/panel_lr/", "/panel/") : path.Replace("/panel/", "/panel_lr/");
            GameObject targetPrefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
            if (targetPrefab)
            {
                externals[i].item = targetPrefab;
            }
            else Debug.Log(string.Format("在路径{0}未找到{1}，请先在该路径下生成指定预制后再试", root.name, path));
        }

        CacheHierarchy = null;
        SavePrefab(mTran, isArab);
    }

    //获取资源路径
    public static string GetAssetPath(string path)
    {
        int index = path.IndexOf("Assets");
        return path.Substring(index, path.Length - index);
    }
}

/// <summary>
/// 预制容器配对
/// </summary>
public class ItemPair
{
    public string PrefabName;
    public string ParentName;
    public GameObject Prefab;
    public Transform Parent;
    public bool isUnique;//是否唯一
    public List<GameObject> Prefabs;

    public ItemPair(string preName, string parName, GameObject pre, Transform par, bool isUni)
    {
        PrefabName = preName;
        ParentName = parName;
        Prefab = pre;
        Parent = par;
        isUnique = isUni;
        Prefabs = new List<GameObject>();
    }

    public void Generate(Transform parent = null)
    {
        if (isUnique && Prefabs.Count >= 1) return;
        GameObject go = (GameObject)PrefabUtility.InstantiatePrefab(Prefab);// UnityEngine.Object.Instantiate(Prefab, parent ? parent : Parent);
        go.SetParent(parent ? parent : Parent);
        go.transform.localEulerAngles = Vector3.zero;
        go.transform.localPosition = Vector3.zero;
        go.transform.localScale = Vector3.one;
        go.name = Prefab.name;
        Prefabs.Add(go);
        //PrefabUtility.ConnectGameObjectToPrefab(go, Prefab);
    }

    public int Count { get { return Prefabs.Count; } }

    public bool isEquals(string pre, string par)
    {
        return PrefabName == pre && ParentName == par;
    }

    public void ChoisePrefab()
    {
        if (Prefab)
            Selection.activeGameObject = Prefab.gameObject;
    }

    public void ChoiseParent()
    {
        if (Parent)
            Selection.activeGameObject = Parent.gameObject;
    }
}
