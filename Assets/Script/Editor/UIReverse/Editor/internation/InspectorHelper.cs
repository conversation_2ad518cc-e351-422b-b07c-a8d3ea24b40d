using System;
using UnityEditor;
using UnityEngine;

public class InspectorHelper
{

    public static bool BeginFoldOut(bool foldOut, string text, string url = null, Action action = null)
    {
        text = "<b><size=12>" + text + "</size></b>";
        GUILayout.BeginHorizontal(EditorStylesExt.toolbarTE);
        if (GUILayout.Button((foldOut ? EditorStylesExt.toolbarMinus : EditorStylesExt.toolbarPlus), EditorStylesExt.toolbarbuttonTE, GUILayout.ExpandWidth(false))) foldOut = !foldOut;
        if (GUILayout.But<PERSON>(text, EditorStylesExt.toolbarTE, GUILayout.ExpandWidth(true))) foldOut = !foldOut;
        if (GUILayout.Button(EditorStylesExt.helpIcon, EditorStylesExt.toolbarbuttonTE, GUILayout.ExpandWidth(false)) && url != null) Application.OpenURL(url);
        if (GUILayout.But<PERSON>(EditorStylesExt.popupIcon, EditorStylesExt.toolbarbuttonTE, GUILayout.ExpandWidth(false)) && action != null) action();
        GUILayout.EndHorizontal();
        return foldOut;
    }

    public static void FixedGroup(string text)
    {
        text = "<b><size=12>" + text + "</size></b>";
        GUILayout.Toggle(true, text, "dragtab");
    }

    public static bool BeginFoldOut(string text, bool foldOut, bool endSpace = true)
    {

        text = "<b><size=12>" + text + "</size></b>";
        if (foldOut)
        {
            text = "+ " + text;
        }
        else
        {
            text = "~ " + text;
        }

        if (!GUILayout.Toggle(true, text, "dragtab"))
        {
            foldOut = !foldOut;
        }

        if (!foldOut && endSpace) GUILayout.Space(5f);

        return foldOut;
    }

    public static bool Toggle(string text, bool value, bool leftToggle)
    {
        GUI.backgroundColor = value ? Color.green : Color.white;
        value = leftToggle ? EditorGUILayout.ToggleLeft(text, value) : EditorGUILayout.Toggle(text, value);
        GUI.backgroundColor = Color.white;
        return value;
    }

    public static void BeginGroup(int padding = 0)
    {
        GUILayout.BeginHorizontal();
        GUILayout.Space(padding);
        EditorGUILayout.BeginHorizontal("TextArea", GUILayout.MinHeight(10f));
        GUILayout.BeginVertical();
        GUILayout.Space(5f);
    }

    public static void EndGroup(bool endSpace = true)
    {
        GUILayout.Space(5f);
        GUILayout.EndVertical();
        EditorGUILayout.EndHorizontal();
        GUILayout.Space(3f);
        GUILayout.EndHorizontal();
        GUILayout.Space(5f);
    }

}
