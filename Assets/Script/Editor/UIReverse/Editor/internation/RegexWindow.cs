using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using System.Text.RegularExpressions;

public class RegexWindow : EditorWindow
{
    static EditorWindow _window;
    string Pattern = string.Empty;
    string Text = string.Empty;
    List<string> Results = new List<string>();

    public static void ShowWindow()
    {
        _window = GetWindow(typeof(RegexWindow), true, "正则验证");
        _window.Show();
    }

    void OnGUI()
    {
        Pattern = EditorGUILayout.TextField("正则表达式：", Pattern);
        EditorGUILayout.LabelField("文本：");
        Text = EditorGUILayout.TextArea(Text, GUILayout.Height(54));
        if (GUILayout.Button("Match")) Match();

        if (Results.Count > 0)
        {
            for (int i = 0, count = Results.Count; i < count; i++)
                GUILayout.TextField(string.Format("[{0}] {1}", i, Results[i]));
        }
    }

    void Match()
    {
        Results.Clear();
        Regex regex = new Regex(Pattern);
        Match mat = regex.Match(Text);
        if (mat.Success)
        {
            for (int i = 0, count = mat.Groups.Count; i < count; i++)
                Results.Add(mat.Groups[i].Value);
        }
        else
            Results.Add("匹配失败");
    }
}
