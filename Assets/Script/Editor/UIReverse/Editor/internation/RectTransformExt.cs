using System;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using UnityEngine.UI;
using UnityEditor.Experimental.SceneManagement;

/// <summary>
/// RectTransform组件拓展
/// </summary>
[CustomEditor(typeof(RectTransform))]
[CanEditMultipleObjects]
public class RectTransformExt : DecoratorEditor
{
    private static bool isFoldout;//是否展开
    private static string[] tips = { "单个翻转", "层级翻转", "拷贝布局", "粘贴布局", "添加组件", "移除组件", "清除组件", "Vertex翻转", "激活对象", "自动创建", "清空", "中文预制", "更新Externals", "Dynamic不自动翻","ScaleX翻转" };
    private static GUIContent[] GUIContents;
    const int WIDTH = 40;
    const int HEIGHT = 18;
    const string HelpURL = @"https://www.teambition.com/project/58f97e40a10431394ad770f6/posts/post/596881d41f2d170ed33e1136";

    private bool showSceneAnchors;

    #region 初始化
    private void OnEnable()
    {
        Init();
    }

    private void Init()
    {
        if (GUIContents == null)
        {
            GUIContents = new GUIContent[tips.Length];
            //设置图片和提示文本
            for (int i = 0; i < GUIContents.Length; i++)
                GUIContents[i] = new GUIContent(tips[i], tips[i]);
        }
        if (uiPanel == null)
        {
            uiPanel = GameObject.Find("GameMain/UI");
            GameObject cameraObj = GameObject.Find("GameMain/UICamera");
            if (cameraObj)
                uiCamera = cameraObj.GetComponent<Camera>();
        }
    }
    #endregion

    public RectTransformExt() : base("RectTransformEditor") { }

    #region UI绘制
    /// <summary>
    /// 重绘RectTransform
    /// </summary>
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();

        EditorGUILayout.Space();
        if (GUILayout.Button("数值取整"))
        {
            RectTransform rectTransform = target as RectTransform;
            rectTransform.anchoredPosition = new Vector2(Mathf.RoundToInt(rectTransform.anchoredPosition.x), Mathf.RoundToInt(rectTransform.anchoredPosition.y));
            rectTransform.sizeDelta = new Vector2(Mathf.RoundToInt(rectTransform.sizeDelta.x), Mathf.RoundToInt(rectTransform.sizeDelta.y));
            rectTransform.localEulerAngles = new Vector3(Mathf.RoundToInt(rectTransform.localEulerAngles.x), Mathf.RoundToInt(rectTransform.localEulerAngles.y), Mathf.RoundToInt(rectTransform.localEulerAngles.z));
            rectTransform.localScale = new Vector3((float)Math.Round(rectTransform.localScale.x, 2), (float)Math.Round(rectTransform.localScale.y, 2), (float)Math.Round(rectTransform.localScale.z, 2));
        } 

        EditorGUILayout.Space();
        showSceneAnchors = InspectorHelper.Toggle("场景显示Anchors", showSceneAnchors, true);
        EditorGUILayout.Space();

        RectTransform rect = target as RectTransform;
        int attach = GetAttach(rect);
        string language = string.Empty;
        switch (attach)
        {
            case 1: language = "<color=green>[中文]</color>"; break;
            case 2: language = "<color=orange>[阿语]</color>"; break;
            default: break;
        }
        isFoldout = InspectorHelper.BeginFoldOut(isFoldout, string.Format("国际化选项 {0}", language), HelpURL, CallMenu);
        if (isFoldout && GUIContents != null)
        {
            InspectorHelper.BeginGroup();
            DrawButtons(rect);
            DrawItems(rect);
            InspectorHelper.EndGroup();
        }
    }

    void DrawButtons(RectTransform rect)
    {
        //翻转UI
        GUILayout.BeginHorizontal();
        GUILayout.Space(10);
        if (GUILayout.Button(GUIContents[0], EditorStylesExt.btnLeft, GUILayout.Height(HEIGHT), GUILayout.MinWidth(WIDTH))) UIInternationEditor.ReversalUI();
        if (GUILayout.Button(GUIContents[1], EditorStylesExt.btnRight, GUILayout.Height(HEIGHT), GUILayout.MinWidth(WIDTH))) UIInternationEditor.ReversalAllUI();
        GUILayout.Space(10);
        GUILayout.EndHorizontal();

        //组件
        GUILayout.BeginHorizontal();
        GUILayout.Space(10);
        if (rect.GetComponent<UIReverse>())
        {
            GUI.color = Color.green;
            if (GUILayout.Button(GUIContents[5], EditorStylesExt.btnLeft, GUILayout.Height(HEIGHT), GUILayout.MinWidth(WIDTH))) AddComponent();
            GUI.color = Color.white;
        }
        else if (GUILayout.Button(GUIContents[4], EditorStylesExt.btnLeft, GUILayout.Height(HEIGHT), GUILayout.MinWidth(WIDTH))) AddComponent();
        UIReverse[] cmps = rect.GetComponentsInChildren<UIReverse>();
        if (cmps.Length > 0)
        {
            GUI.color = Color.yellow;
            if (GUILayout.Button(string.Format("清除组件[{0}]", cmps.Length), EditorStylesExt.btnRight, GUILayout.Height(HEIGHT), GUILayout.MinWidth(WIDTH))) ClearComponents();
            GUI.color = Color.white;
        }
        else
        {
            GUI.enabled = false;
            GUILayout.Button(GUIContents[6], EditorStylesExt.btnRight, GUILayout.Height(HEIGHT), GUILayout.MinWidth(WIDTH));
            GUI.enabled = true;
        }
        GUILayout.Space(10);
        GUILayout.EndHorizontal();

        //拷贝&粘贴
        GUILayout.BeginHorizontal();
        GUILayout.Space(10);
        if (UIInternationEditor.CopyGameObject) GUI.color = Color.green;
        if (GUILayout.Button(GUIContents[2], EditorStylesExt.btnLeft, GUILayout.Height(HEIGHT), GUILayout.MinWidth(WIDTH))) UIInternationEditor.CopyUI();
        GUI.color = Color.white;
        if (!UIInternationEditor.CopyGameObject)
        {
            GUI.enabled = false;
            GUILayout.Button(GUIContents[3], EditorStylesExt.btnRight, GUILayout.Height(HEIGHT), GUILayout.MinWidth(WIDTH));
            GUI.enabled = true;
        }
        else if (GUILayout.Button(GUIContents[3], EditorStylesExt.btnRight, GUILayout.Height(HEIGHT), GUILayout.MinWidth(WIDTH))) UIInternationEditor.PasteUI();
        GUILayout.Space(10);
        GUILayout.EndHorizontal();

        //标记处理
        GUILayout.BeginHorizontal();
        GUILayout.Space(10);
        if (UIInternationEditor.IsReverse(rect)) GUI.color = Color.green;
        if (GUILayout.Button(GUIContents[7], EditorStylesExt.btnLeft, GUILayout.Height(HEIGHT), GUILayout.ExpandWidth(true))) UIInternationEditor.AddOrDefTag(UIInternationUility.__F);
        GUI.color = Color.white;
        if (UIInternationEditor.IsDynamic(rect)) GUI.color = Color.green;
        if (GUILayout.Button(GUIContents[13], EditorStylesExt.btnRight, GUILayout.Height(HEIGHT), GUILayout.ExpandWidth(true))) UIInternationEditor.AddOrDefTag(UIInternationUility.__D);
        GUI.color = Color.white;
        if (UIInternationEditor.IsScaleFlip(rect)) GUI.color = Color.green;
        if (GUILayout.Button(GUIContents[14], EditorStylesExt.btnRight, GUILayout.Height(HEIGHT), GUILayout.ExpandWidth(true))) UIInternationEditor.AddOrDefTag(UIInternationUility.__SF);
        GUI.color = Color.white;
        GUILayout.Space(10);
        GUILayout.EndHorizontal();

        //其他
        GUILayout.BeginHorizontal();
        GUILayout.Space(10);
        if (GUILayout.Button(GUIContents[8], EditorStylesExt.btnLeft, GUILayout.Height(HEIGHT), GUILayout.ExpandWidth(true))) SetActive(rect);
        if (GUILayout.Button(GUIContents[12], EditorStylesExt.btnRight, GUILayout.Height(HEIGHT), GUILayout.ExpandWidth(true))) UpdateExternals(rect);
        GUILayout.Space(10);
        GUILayout.EndHorizontal();
    }

    void DrawItems(RectTransform rect)
    {
        //添加Item
        UIHierarchy hie = UIInternationUility.GetExternals(rect);
        if (hie != null)
        {
            List<ItemPair> pairs = UIInternationEditor.GenerateItemPairs(hie);//生成预制容器配对
            InspectorHelper.BeginGroup(10);
            GUILayout.BeginHorizontal(EditorStylesExt.toolbar);
            GUILayout.Label(string.Format("[{0}]元素列表", hie.name), EditorStylesExt.toolbarbutton);
            GUILayout.Space(4);
            if (GUILayout.Button("Revert", EditorStylesExt.toolbarbutton, GUILayout.ExpandWidth(false))) RevertPrefab();
            GUILayout.EndHorizontal();

            if (pairs != null && pairs.Count > 0)
            {
                for (int i = 0, len = pairs.Count; i < len; i++)
                {
                    ItemPair item = pairs[i];
                    GUILayout.BeginHorizontal();
                    GUILayout.Space(10);
                    if (item.Count >= 1) GUI.color = item.isUnique ? Color.yellow : Color.green;
                    if (GUILayout.Button(EditorGUIUtility.FindTexture("VisibilityOn"), EditorStylesExt.preButton, GUILayout.Width(25))) item.ChoisePrefab();
                    if (GUILayout.Button(string.Format("{0}[{1}]", item.PrefabName, item.Count), EditorStylesExt.preButton)) item.Generate(rect);
                    if (item.Parent == null) GUI.enabled = false;
                    GUIContent content = GUIContents[9];
                    content.tooltip = string.Format("父容器:{0}", item.ParentName);
                    if (GUILayout.Button(content, EditorStylesExt.preButton, GUILayout.MaxWidth(60))) item.Generate();
                    if (GUILayout.Button(EditorGUIUtility.FindTexture("SceneLoadIn"), EditorStylesExt.preButton, GUILayout.Width(25))) item.ChoiseParent();
                    GUI.enabled = true;
                    if (item.Prefabs.Count == 0) GUI.enabled = false;
                    if (GUILayout.Button(EditorGUIUtility.FindTexture("Refresh"), EditorStylesExt.preButton, GUILayout.Width(25))) UIInternationEditor.ClearPair(item);
                    GUI.enabled = true;
                    GUI.color = Color.white;
                    GUILayout.Space(10);
                    GUILayout.EndHorizontal();
                }
            }
            InspectorHelper.EndGroup();
        }
    }

    void CallMenu()
    {
        RectTransform rect = target as RectTransform;
        GenericMenu menu = new GenericMenu();
        menu.AddItem(new GUIContent("保存预制/中文"), false, () => UIInternationEditor.UpdateExternals(rect, false));
        menu.AddItem(new GUIContent("保存预制/阿语"), false, () => UIInternationEditor.UpdateExternals(rect, true));
        menu.ShowAsContext();
    }

    public new void OnSceneGUI()
    {
        if (showSceneAnchors)   //报错规避、、、、
        {
            CallInspectorMethod("OnSceneGUI");
        }
    }

    #endregion

    //检查路径，判断UI归属
    int GetAttach(RectTransform rect)
    {
        if (rect == null) return 0;

        string path = UIPrefabHelper.GetPrefabAssetPath(rect.gameObject);
        if (string.IsNullOrEmpty(path)) return 0;

        if (path.IndexOf("/panel_lr/") > 0)
            return 1;
        else if (path.IndexOf("/panel/") > 0)
            return 2;
        else return 0;
    }

    //添加动态翻转组件
    private void AddComponent()
    {
        if (Selection.activeGameObject)
        {
            object[] selections = Selection.objects;
            for (int i = selections.Length - 1; i >= 0; i--)
            {
                GameObject obj = selections[i] as GameObject;
                if (obj)
                {
                    UIReverse cmp = obj.GetComponent<UIReverse>();
                    if (cmp)
                        DestroyImmediate(cmp, true);
                    else
                    {
                        cmp = obj.AddComponent<UIReverse>();
                        cmp.Init();
                    }
                    EditorUtility.SetDirty(obj);
                }
            }
        }
    }

    //移除所有动态翻转组件
    private void ClearComponents()
    {
        GameObject obj = Selection.activeGameObject;
        UIReverse[] cmps = obj.GetComponentsInChildren<UIReverse>();
        for (int i = cmps.Length - 1; i >= 0; i--)
            DestroyImmediate(cmps[i], true);
    }

    //激活全部UI
    private void SetActive(Transform mTran)
    {
        Action<Transform> action = null;
        action = tran =>
        {
            ActiveTran(tran);
            for (int i = 0, len = tran.childCount; i < len; i++)
            {
                Transform child = tran.GetChild(i);
                ActiveTran(child);
                if (child.childCount > 0) action(child);
            }
        };
        action(mTran);
        mTran.gameObject.SetActive(false);
        mTran.gameObject.SetActive(true);
    }

    private void UpdateExternals(RectTransform mTran)
    {
        int attach = GetAttach(mTran);
        UIInternationEditor.UpdateExternals(mTran, attach == 2 && attach != 1);
    }

    //激活对象
    private void ActiveTran(Transform mTran)
    {
        Undo.RegisterFullObjectHierarchyUndo(mTran, "ActiveTran");
        // SoarDText input = mTran.GetComponent<SoarDText>();
        // if (input != null && input.text.Length == 0)
            // input.text = mTran.name;

        Text text = mTran.GetComponent<Text>();
        if (text && text.text.Length == 0)
        {
            if (text.name.IndexOf("Num", StringComparison.OrdinalIgnoreCase) >= 0)
                text.text = UnityEngine.Random.Range(1000, 9999).ToString();
            else
                text.text = mTran.name;
        }

        Slider slide = mTran.GetComponent<Slider>();
        if (slide)
            slide.value = .7f;

        // SDImage image = mTran.GetComponent<SDImage>();
        // if (image && image.sprite == null && image.color == Color.white)
        // {
        //     string spriteName = string.Format("icon_hero_equip_{0:000}", UnityEngine.Random.Range(1, 36));
        //     image.SetSpriteName(spriteName);
        // }

        Mask mask = mTran.GetComponent<Mask>();
        if (mask) mask.enabled = false;
        mTran.gameObject.SetActive(true);
    }

    //恢复预制
    private void RevertPrefab()
    {
        PrefabUtility.RevertPrefabInstance((target as RectTransform).gameObject, InteractionMode.AutomatedAction);
        UIInternationEditor.ClearPair();
    }

    #region UI预览
    private static PreviewRenderUtility previewRenderUtility;
    private static Camera uiCamera;
    private static GameObject uiPanel;
    private static bool isPreview;//是否启用预览
    private static bool isDynamic = true;//是否启动动态UI

    private static UnityEngine.Object _target;
    public static GameObject _targetPrefab;
    private Vector2 _drag;

    private Mesh _targetMesh;
    private Material _material;

    public override bool HasPreviewGUI()
    {
        bool hasPreview = false;
        // PrefabType prefabType = PrefabUtility.GetPrefabType(target);
        // if (prefabType == PrefabType.Prefab)
        PrefabAssetType prefabType = PrefabUtility.GetPrefabAssetType(target);
        if (prefabType == PrefabAssetType.Regular)
        {
            RectTransform rect = target as RectTransform;
            Image img = rect.GetComponent<Image>();
            if (img == null) hasPreview = true;
        }
        return hasPreview;
    }

    private void ValidateData()
    {
        if (uiPanel == null || uiCamera == null || !isPreview) return;

        if (_target != target)
        {
            _target = target;

            //预览工具
            previewRenderUtility = new PreviewRenderUtility();
            previewRenderUtility.camera.transform.position = new Vector3(0, 0, -5);
            previewRenderUtility.camera.transform.rotation = Quaternion.identity;
            previewRenderUtility.camera.fieldOfView = 10;

            //生成UI预制实例
            if (_targetPrefab != null) DestroyImmediate(_targetPrefab);
            PrefabAssetType prefabType = PrefabUtility.GetPrefabAssetType(target);
            if (prefabType == PrefabAssetType.Regular)
            {
                _targetPrefab = Instantiate(Selection.activeGameObject, uiPanel.transform);
                //生成动态UI
                if (isDynamic)
                {
                    Transform prefabTran = _targetPrefab.transform;
                    Func<Transform, string, Transform> findChild = null;
                    findChild = (tran, name) =>
                    {
                        foreach (Transform t in tran)
                        {
                            if (t.name == name)
                                return t;
                        }
                        foreach (Transform t in tran)
                        {
                            Transform child = findChild(t, name);
                            if (child) return child;
                        }
                        return null;
                    };

                    //生成共用组件
                    string path = AssetDatabase.GetAssetPath(_target);
                    if (path.Length > 0)
                    {
                        string strUIPath = "Assets/Data/ui/panel_lr/commonitems"; ;
                        Transform Top = findChild(prefabTran, "DynamicTop");
                        Transform Back = findChild(prefabTran, "DynamicBack");
                        Transform Resource = findChild(prefabTran, "DynamicResource");

                        CreateUIItem(strUIPath + "/Top.prefab", Top);
                        CreateUIItem(strUIPath + "/Back.prefab", Back);
                        CreateUIItem(strUIPath + "/HeadResource.prefab", Resource);
                    }
                }
            }
            else
                _targetPrefab = target as GameObject;

            //生成Mesh
            if (_targetMesh == null)
            {
                float width = 1f / 2;
                float height = 1.5f / 2;
                Vector3[] newVertices = { new Vector3(width, height, 0), new Vector3(-width, height, 0), new Vector3(-width, -height, 0), new Vector3(width, -height, 0) };
                Vector2[] newUV = { new Vector2(1, 1), new Vector2(0, 1), new Vector2(0, 0), new Vector2(1, 0) };
                int[] newTriangles = { 0, 2, 1, 0, 3, 2, 0, 1, 2, 0, 2, 3 };
                _targetMesh = new Mesh();
                _targetMesh.vertices = newVertices;
                _targetMesh.uv = newUV;
                _targetMesh.triangles = newTriangles;
            }

            //生成材质
            if (_material == null)
            {
                _material = new Material(Shader.Find("Unlit/Texture"));
                _material.color = Color.white;
            }

            //相机渲染,生成快照
            Vector2 size = uiPanel.GetComponent<RectTransform>().sizeDelta;
            RenderTexture rt = new RenderTexture((int)size.x, (int)size.y, 16);
            uiCamera.targetTexture = rt;
            RenderTexture.active = rt;
            uiCamera.Render();

            //删除预制，刷新视图
            if (_targetPrefab != null) DestroyImmediate(_targetPrefab);
            //SceneView.lastActiveSceneView.Repaint();

            //读取缓冲区像素信息
            Texture2D tex = new Texture2D(rt.width, rt.height);
            tex.ReadPixels(new Rect(0, 0, rt.width, rt.height), 0, 0);
            tex.Apply();
            _material.mainTexture = tex;

            uiCamera.targetTexture = null;
            RenderTexture.active = null;
        }
    }

    private void CreateUIItem(string strPath, Transform transParent)
    {
        if (transParent == null)
            return;
        Debug.Log(strPath);
        GameObject objItem = AssetDatabase.LoadAssetAtPath<GameObject>(strPath);
        if (objItem == null)
            return;
        Debug.Log("create:" + strPath);

        Instantiate(objItem, transParent);
    }

    public override void OnPreviewGUI(Rect r, GUIStyle background)
    {
        ValidateData();
        _drag = DragUI(_drag, r);
        if (isPreview && Event.current.type == EventType.Repaint)
        {
            if (uiPanel == null || uiCamera == null)
                EditorGUI.DropShadowLabel(r, "预览需要切换到GameMain场景");
            else
            {
                if (previewRenderUtility == null || previewRenderUtility.camera == null) return;
                previewRenderUtility.BeginPreview(r, background);

                previewRenderUtility.DrawMesh(_targetMesh, Matrix4x4.identity, _material, 0);
                previewRenderUtility.camera.transform.rotation = Quaternion.Euler(new Vector3(-_drag.y, -_drag.x, 0));
                previewRenderUtility.camera.transform.position = previewRenderUtility.camera.transform.forward * -9f;
                previewRenderUtility.camera.Render();

                Texture resultRender = previewRenderUtility.EndPreview();
                GUI.DrawTexture(r, resultRender, ScaleMode.StretchToFill, false);
            }
        }
        isPreview = false;
    }

    public override void OnPreviewSettings()
    {
        isPreview = true;
        bool dynamic = GUILayout.Toggle(isDynamic, "Dynamic");
        if (dynamic != isDynamic)
        {
            isDynamic = dynamic;
            _target = null;
        }
        if (GUILayout.Button("Create", EditorStyles.whiteMiniLabel))
        {
            // PrefabType prefabType = PrefabUtility.GetPrefabType(target);
            // if (prefabType == PrefabType.Prefab)
            PrefabAssetType prefabType = PrefabUtility.GetPrefabAssetType(target);
            if (prefabType == PrefabAssetType.Regular)
            {
                GameObject prefab = Instantiate(Selection.activeGameObject, uiPanel.transform);
                // PrefabUtility.ConnectGameObjectToPrefab(prefab, Selection.activeGameObject);
                Debug.LogError("功能注释，需要重写");
            }
        }
        if (GUILayout.Button("Reset", EditorStyles.whiteMiniLabel))
        {
            _target = null;
            _drag = Vector2.zero;
            foreach (Transform tran in uiPanel.transform)
                DestroyImmediate(tran.gameObject);
        }

        if (uiPanel == null || uiCamera == null)
        {
            if (GUILayout.Button("Jump", EditorStyles.whiteMiniLabel))
                UnityEditor.SceneManagement.EditorSceneManager.OpenScene("Assets/Scenes/GameMain.unity");
        }
    }

    void OnDestroy()
    {
        if (previewRenderUtility != null)
            previewRenderUtility.Cleanup();
    }

    public static Vector2 DragUI(Vector2 scrollPosition, Rect position)
    {
        int controlID = GUIUtility.GetControlID("Slider".GetHashCode(), FocusType.Passive);
        Event current = Event.current;
        switch (current.GetTypeForControl(controlID))
        {
            case EventType.MouseDown:
                if (position.Contains(current.mousePosition) && position.width > 50f)
                {
                    GUIUtility.hotControl = controlID;
                    current.Use();
                    EditorGUIUtility.SetWantsMouseJumping(1);
                }
                break;
            case EventType.MouseUp:
                if (GUIUtility.hotControl == controlID)
                    GUIUtility.hotControl = 0;
                EditorGUIUtility.SetWantsMouseJumping(0);
                break;
            case EventType.MouseDrag:
                if (GUIUtility.hotControl == controlID)
                {
                    scrollPosition -= current.delta * (float)((!current.shift) ? 1 : 3) / Mathf.Min(position.width, position.height) * 140f;
                    scrollPosition.y = Mathf.Clamp(scrollPosition.y, -90f, 90f);
                    current.Use();
                    GUI.changed = true;
                }
                break;
        }
        return scrollPosition;
    }
    #endregion
}